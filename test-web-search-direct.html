<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Web Search Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .loading {
            color: #007bff;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Direct OpenAI Web Search Test</h1>
        <p>This test directly calls OpenAI's web search API to verify functionality.</p>

        <div class="test-section">
            <h3>Test 1: Direct API Call</h3>
            <p>Test the raw OpenAI web search API call</p>
            <button onclick="testDirectAPI()">Test Direct API Call</button>
            <div id="direct-results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Service Method</h3>
            <p>Test through our AIWebSearchService</p>
            <button onclick="testServiceMethod()">Test Service Method</button>
            <div id="service-results" class="results"></div>
        </div>
    </div>

    <script type="module">
        // Import the service
        import { AIWebSearchService } from './src/services/aiWebSearchService.js';

        const service = new AIWebSearchService();

        window.testDirectAPI = async function() {
            const resultsDiv = document.getElementById('direct-results');
            const button = event.target;
            
            try {
                button.disabled = true;
                resultsDiv.innerHTML = '<span class="loading">🔄 Testing direct OpenAI API call...</span>';

                const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
                if (!apiKey) {
                    throw new Error('No OpenAI API key found');
                }

                const response = await fetch('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: 'gpt-4o',
                        messages: [
                            {
                                role: 'user',
                                content: 'Search the web for information about automotive part number "11-28001-SX". I need manufacturer, specifications, and vehicle compatibility.'
                            }
                        ],
                        tools: [
                            {
                                type: "web_search"
                            }
                        ],
                        tool_choice: "auto"
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API Error: ${response.status} - ${errorText}`);
                }

                const data = await response.json();
                
                resultsDiv.innerHTML = `<span class="success">✅ Direct API call successful!</span>\n\n` +
                    `Response structure:\n${JSON.stringify(data, null, 2)}`;

            } catch (error) {
                resultsDiv.innerHTML = `<span class="error">❌ Direct API call failed:</span>\n${error.message}`;
            } finally {
                button.disabled = false;
            }
        };

        window.testServiceMethod = async function() {
            const resultsDiv = document.getElementById('service-results');
            const button = event.target;
            
            try {
                button.disabled = true;
                resultsDiv.innerHTML = '<span class="loading">🔄 Testing service method...</span>';

                const basicData = {
                    partArticleNumber: '11-28001-SX',
                    productName: 'Test Product',
                    manufacturer: 'Test Manufacturer',
                    stockQuantity: 10,
                    retailPrice: 50.00
                };

                const result = await service.processWithOpenAIWebSearch(basicData);
                
                resultsDiv.innerHTML = `<span class="success">✅ Service method successful!</span>\n\n` +
                    `Enhanced data:\n${JSON.stringify(result, null, 2)}`;

            } catch (error) {
                resultsDiv.innerHTML = `<span class="error">❌ Service method failed:</span>\n${error.message}`;
            } finally {
                button.disabled = false;
            }
        };
    </script>
</body>
</html>

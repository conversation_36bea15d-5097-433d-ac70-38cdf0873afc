import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import {
  Download,
  Edit3,
  Check,
  X,
  AlertTriangle,
  Loader2,
  Search,
  RefreshCw,
  Upload,
  Image as ImageIcon
} from 'lucide-react';
import { TyreProduct, BrakeProduct } from '../types/product.types';
import { searchByArticleNumber, TecDocSearchResult } from '@/services/tecdocService';
import { useUser } from '@/contexts/UserContext';

interface ImportReviewTableProps {
  products: Partial<TyreProduct | BrakeProduct>[];
  onProductUpdate: (index: number, updatedProduct: Partial<TyreProduct | BrakeProduct>) => void;
  onBatchFetchData: () => Promise<void>;
  categoryId: string;
  isLoading?: boolean;
}

interface EditingCell {
  rowIndex: number;
  field: string;
}

interface ImageUploadState {
  [productIndex: number]: {
    primaryImage?: File;
    additionalImages?: File[];
    uploading?: boolean;
  };
}

export const ImportReviewTable: React.FC<ImportReviewTableProps> = ({
  products,
  onProductUpdate,
  onBatchFetchData,
  categoryId,
  isLoading = false
}) => {
  const { t } = useTranslation();
  const { userRole, isMerchant, isSupplier } = useUser();
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null);
  const [editValue, setEditValue] = useState<string>('');
  const [fetchingData, setFetchingData] = useState<Set<number>>(new Set());
  const [imageUploads, setImageUploads] = useState<ImageUploadState>({});

  // Define columns based on user role and category
  const getTableColumns = () => {
    const baseColumns = [
      { key: 'status', label: 'Status', width: '100px' },
      { key: 'partArticleNumber', label: 'Part Article #', width: '150px', editable: true },
      { key: 'name', label: 'Product Name', width: '200px', editable: true },
      { key: 'manufacturer', label: 'Manufacturer', width: '150px', editable: true },
      { key: 'category', label: 'Category', width: '120px', editable: true },
      { key: 'subcategory', label: 'Subcategory', width: '120px', editable: true },
      { key: 'stockQuantity', label: 'Stock Qty', width: '100px', editable: true, type: 'number' },
    ];

    // Add role-specific pricing columns
    if (isMerchant()) {
      baseColumns.push({ key: 'retailPrice', label: 'Retail Price', width: '120px', editable: true, type: 'number' });
    } else if (isSupplier()) {
      baseColumns.push({ key: 'wholesalePrice', label: 'Wholesale Price', width: '140px', editable: true, type: 'number' });
    }

    // Add common columns
    baseColumns.push(
      { key: 'supplierName', label: 'Supplier', width: '150px', editable: true },
      { key: 'shippingOrigin', label: 'Shipping Origin', width: '140px', editable: true },
      { key: 'vehicleCompatibility', label: 'Vehicle Compatibility', width: '200px', editable: true, type: 'textarea' },
      { key: 'primaryImage', label: 'Images', width: '120px', type: 'image' }
    );

    // Add category-specific columns
    if (categoryId === 'tyres') {
      const tyreColumns = [
        { key: 'width', label: 'Width', width: '80px', editable: true, type: 'number' },
        { key: 'aspectRatio', label: 'Aspect Ratio', width: '100px', editable: true, type: 'number' },
        { key: 'rimDiameter', label: 'Rim Diameter', width: '110px', editable: true, type: 'number' },
        { key: 'loadIndex', label: 'Load Index', width: '100px', editable: true, type: 'number' },
        { key: 'speedRating', label: 'Speed Rating', width: '110px', editable: true },
        { key: 'season', label: 'Season', width: '100px', editable: true, type: 'select' }
      ];
      baseColumns.push(...tyreColumns);
    }

    // Add final columns
    baseColumns.push(
      { key: 'descriptionAndSpecifications', label: 'Description', width: '250px', editable: true, type: 'textarea' },
      { key: 'productStatus', label: 'Product Status', width: '120px', editable: true, type: 'select' },
      { key: 'actions', label: 'Actions', width: '100px' }
    );

    return baseColumns;
  };

  const columns = getTableColumns();

  // Handle image upload
  const handleImageUpload = useCallback((productIndex: number, files: FileList | null, type: 'primary' | 'additional') => {
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('Image size must be less than 5MB');
      return;
    }

    setImageUploads(prev => ({
      ...prev,
      [productIndex]: {
        ...prev[productIndex],
        [type === 'primary' ? 'primaryImage' : 'additionalImages']: type === 'primary' ? file : [...(prev[productIndex]?.additionalImages || []), file],
        uploading: true
      }
    }));

    // Create preview URL and update product
    const previewUrl = URL.createObjectURL(file);
    const updatedProduct = {
      ...products[productIndex],
      [type === 'primary' ? 'primaryImage' : 'additionalImages']: type === 'primary' ? previewUrl : [...(products[productIndex].additionalImages || []), previewUrl],
      updatedAt: new Date()
    };

    onProductUpdate(productIndex, updatedProduct);

    // Reset uploading state
    setTimeout(() => {
      setImageUploads(prev => ({
        ...prev,
        [productIndex]: {
          ...prev[productIndex],
          uploading: false
        }
      }));
    }, 1000);

    toast.success('Image uploaded successfully');
  }, [products, onProductUpdate]);

  // Handle cell editing
  const startEditing = (rowIndex: number, field: string, currentValue: any) => {
    setEditingCell({ rowIndex, field });
    setEditValue(currentValue?.toString() || '');
  };

  const saveEdit = () => {
    if (!editingCell) return;

    const { rowIndex, field } = editingCell;
    const product = products[rowIndex];

    let processedValue: any = editValue;

    // Process value based on field type
    const numericFields = ['stockQuantity', 'retailPrice', 'wholesalePrice', 'width', 'aspectRatio', 'rimDiameter', 'loadIndex'];
    if (numericFields.includes(field)) {
      processedValue = parseFloat(editValue) || 0;
    }

    // Handle field mapping
    let targetField = field;
    if (field === 'productStatus') {
      targetField = 'status'; // Map productStatus to status field
    }

    const updatedProduct = {
      ...product,
      [targetField]: processedValue,
      updatedAt: new Date()
    };

    onProductUpdate(rowIndex, updatedProduct);
    setEditingCell(null);
    setEditValue('');
  };

  const cancelEdit = () => {
    setEditingCell(null);
    setEditValue('');
  };

  // Handle individual data fetching
  const fetchProductData = async (rowIndex: number) => {
    const product = products[rowIndex];
    
    if (!product.partArticleNumber) {
      toast.error(t('products.articleNumberRequired'));
      return;
    }

    setFetchingData(prev => new Set(prev).add(rowIndex));

    try {
      const result = await searchByArticleNumber(product.partArticleNumber);
      
      if (result.success && result.data) {
        const updatedProduct = {
          ...product,
          name: result.data.productName || product.name,
          manufacturer: result.data.brandName || product.manufacturer,
          descriptionAndSpecifications: result.data.description || product.descriptionAndSpecifications,
          // Add any additional specifications from TecDoc
          ...(result.data.specifications && Object.keys(result.data.specifications).length > 0 && {
            specifications: result.data.specifications
          }),
          updatedAt: new Date(),
          dataFetchedFromTecDoc: true,
          tecDocSource: result.source
        };
        
        onProductUpdate(rowIndex, updatedProduct);
        toast.success(t('products.dataFetchedSuccess', { articleNumber: product.partArticleNumber }));
      } else {
        toast.warning(t('products.noDataFound', { articleNumber: product.partArticleNumber }));
      }
    } catch (error) {
      console.error('Error fetching product data:', error);
      toast.error(t('products.fetchDataFailed'));
    } finally {
      setFetchingData(prev => {
        const newSet = new Set(prev);
        newSet.delete(rowIndex);
        return newSet;
      });
    }
  };

  // Enhanced cell rendering for different types
  const renderCell = (value: any, rowIndex: number, column: any) => {
    const { key: field, type, editable } = column;
    const isEditing = editingCell?.rowIndex === rowIndex && editingCell?.field === field;

    // Handle image upload column
    if (type === 'image') {
      return (
        <div className="flex flex-col gap-1">
          {/* Primary Image Upload */}
          <div className="flex items-center gap-1">
            <input
              type="file"
              accept="image/*"
              onChange={(e) => handleImageUpload(rowIndex, e.target.files, 'primary')}
              className="hidden"
              id={`primary-image-${rowIndex}`}
            />
            <label
              htmlFor={`primary-image-${rowIndex}`}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-50 hover:bg-blue-100 rounded cursor-pointer"
            >
              {imageUploads[rowIndex]?.uploading ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <Upload className="h-3 w-3" />
              )}
              Upload
            </label>
          </div>
          {/* Show uploaded image preview */}
          {products[rowIndex].primaryImage && (
            <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
              <ImageIcon className="h-4 w-4 text-gray-500" />
            </div>
          )}
        </div>
      );
    }

    // Handle select fields
    if (type === 'select') {
      if (isEditing) {
        const getSelectOptions = () => {
          if (field === 'productStatus') {
            return [
              { value: 'draft', label: 'Draft' },
              { value: 'active', label: 'Active' },
              { value: 'out_of_stock', label: 'Out of Stock' },
              { value: 'discontinued', label: 'Discontinued' }
            ];
          }
          if (field === 'season') {
            return [
              { value: 'summer', label: 'Summer' },
              { value: 'winter', label: 'Winter' },
              { value: 'all-season', label: 'All Season' }
            ];
          }
          return [];
        };

        return (
          <div className="flex items-center gap-1">
            <Select value={editValue} onValueChange={setEditValue}>
              <SelectTrigger className="text-xs h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {getSelectOptions().map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button size="sm" variant="ghost" onClick={saveEdit} className="h-6 w-6 p-0">
              <Check className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="ghost" onClick={cancelEdit} className="h-6 w-6 p-0">
              <X className="h-3 w-3" />
            </Button>
          </div>
        );
      }
    }

    // Handle editable fields
    if (editable) {
      if (isEditing) {
        return (
          <div className="flex items-center gap-1">
            {type === 'textarea' ? (
              <Textarea
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="min-h-[60px] text-xs"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && e.ctrlKey) {
                    saveEdit();
                  } else if (e.key === 'Escape') {
                    cancelEdit();
                  }
                }}
              />
            ) : (
              <Input
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="text-xs"
                type={type === 'number' ? 'number' : 'text'}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    saveEdit();
                  } else if (e.key === 'Escape') {
                    cancelEdit();
                  }
                }}
              />
            )}
            <Button size="sm" variant="ghost" onClick={saveEdit} className="h-6 w-6 p-0">
              <Check className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="ghost" onClick={cancelEdit} className="h-6 w-6 p-0">
              <X className="h-3 w-3" />
            </Button>
          </div>
        );
      }

      return (
        <div
          className="cursor-pointer hover:bg-gray-100 p-1 rounded text-xs min-h-[20px] flex items-center"
          onClick={() => startEditing(rowIndex, field, value)}
        >
          <span className="flex-1">{value || <em className="text-gray-400">Click to edit</em>}</span>
          <Edit3 className="h-3 w-3 ml-1 opacity-50" />
        </div>
      );
    }

    // Handle non-editable fields (like status badges)
    if (field === 'status' && !editable) {
      return getStatusBadge(products[rowIndex], rowIndex);
    }

    // Handle actions column
    if (field === 'actions') {
      return (
        <Button
          size="sm"
          variant="outline"
          onClick={() => fetchProductData(rowIndex)}
          disabled={!products[rowIndex].partArticleNumber || fetchingData.has(rowIndex)}
          className="text-xs"
        >
          {fetchingData.has(rowIndex) ? (
            <Loader2 className="h-3 w-3 animate-spin" />
          ) : (
            <Search className="h-3 w-3" />
          )}
        </Button>
      );
    }

    // Default display for non-editable fields
    return (
      <div className="text-xs p-1">
        {value || '-'}
      </div>
    );
  };

  // Get status badge for product
  const getStatusBadge = (product: Partial<TyreProduct | BrakeProduct>, rowIndex: number) => {
    const hasArticleNumber = !!product.partArticleNumber;
    const hasBasicInfo = !!(product.name && product.manufacturer);
    const isFetching = fetchingData.has(rowIndex);
    
    if (isFetching) {
      return <Badge variant="secondary"><Loader2 className="h-3 w-3 mr-1 animate-spin" />Fetching</Badge>;
    }
    
    if (hasArticleNumber && hasBasicInfo) {
      return <Badge variant="default">Complete</Badge>;
    }
    
    if (hasArticleNumber && !hasBasicInfo) {
      return <Badge variant="secondary">Needs Fetch</Badge>;
    }
    
    if (!hasArticleNumber) {
      return <Badge variant="destructive">Missing Article #</Badge>;
    }
    
    return <Badge variant="outline">Incomplete</Badge>;
  };

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">Review Import Data</h3>
          <Badge variant="outline">{products.length} products</Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={onBatchFetchData}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            Fetch Missing Data
          </Button>
        </div>
      </div>

      {/* Data Quality Summary */}
      <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {products.filter(p => p.partArticleNumber && p.name && p.manufacturer).length}
          </div>
          <div className="text-xs text-gray-600">Complete</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-600">
            {products.filter(p => p.partArticleNumber && (!p.name || !p.manufacturer)).length}
          </div>
          <div className="text-xs text-gray-600">Needs Fetch</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">
            {products.filter(p => !p.partArticleNumber).length}
          </div>
          <div className="text-xs text-gray-600">Missing Article #</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {products.filter(p => p.dataFetchedFromTecDoc).length}
          </div>
          <div className="text-xs text-gray-600">TecDoc Fetched</div>
        </div>
      </div>

      {/* Enhanced Review Table with All Columns */}
      <div className="border rounded-lg overflow-hidden">
        <div className="overflow-x-auto max-h-96">
          <table className="w-full text-xs">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={column.key}
                    className={`p-2 text-left ${index < columns.length - 1 ? 'border-r' : ''}`}
                    style={{ width: column.width }}
                  >
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {products.map((product, productIndex) => (
                <tr key={productIndex} className="border-b hover:bg-gray-50">
                  {columns.map((column, columnIndex) => {
                    // Handle special field mappings
                    let value = product[column.key as keyof typeof product];
                    if (column.key === 'productStatus') {
                      value = product.status; // Map productStatus to status field
                    }

                    return (
                      <td
                        key={`${productIndex}-${column.key}`}
                        className={`p-2 ${columnIndex < columns.length - 1 ? 'border-r' : ''}`}
                        style={{ width: column.width }}
                      >
                        {renderCell(value, productIndex, column)}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Enhanced Instructions */}
      <div className="text-xs text-gray-600 space-y-1">
        <p>• <strong>Complete Column Review:</strong> All columns from the main product table are displayed for comprehensive review</p>
        <p>• <strong>Inline Editing:</strong> Click any editable cell to modify values directly</p>
        <p>• <strong>Image Upload:</strong> Use the "Upload" button in the Images column to add product images (upload once only during review)</p>
        <p>• <strong>Role-Based Pricing:</strong> {isMerchant() ? 'Retail Price column is shown for merchant users' : 'Wholesale Price column is shown for supplier users'}</p>
        <p>• <strong>Auto Data Fetch:</strong> Use "Fetch Missing Data" to automatically fill data for all products with Part Article Numbers</p>
        <p>• <strong>Individual Fetch:</strong> Use the search button in Actions column for single product data fetching</p>
        <p>• <strong>Zero Data Loss:</strong> All unmapped columns from your original file are preserved in the Description field</p>
        <p>• <strong>Category-Specific Fields:</strong> {categoryId === 'tyres' ? 'Tyre-specific fields (Width, Aspect Ratio, etc.) are included' : 'Category-specific fields are automatically included'}</p>
      </div>
    </div>
  );
};

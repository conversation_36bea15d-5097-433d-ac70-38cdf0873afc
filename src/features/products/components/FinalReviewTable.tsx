import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle2, 
  AlertTriangle, 
  Edit3, 
  Eye, 
  Image as ImageIcon,
  Database,
  Globe,
  Brain,
  Star,
  TrendingUp,
  Shield
} from 'lucide-react';
import { TyreProduct, BrakeProduct } from '../types/product.types';
import { ProductEnrichmentResult } from '@/services/dataEnrichmentService';

interface FinalReviewTableProps {
  enrichmentResults: ProductEnrichmentResult[];
  onProductUpdate: (index: number, updatedProduct: Partial<TyreProduct | BrakeProduct>) => void;
  onProceedToImport: () => void;
  onBackToEnrichment: () => void;
  className?: string;
}

interface EditingCell {
  rowIndex: number;
  field: string;
}

export const FinalReviewTable: React.FC<FinalReviewTableProps> = ({
  enrichmentResults,
  onProductUpdate,
  onProceedToImport,
  onBackToEnrichment,
  className = ''
}) => {
  const { t } = useTranslation();
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null);
  const [editValue, setEditValue] = useState<string>('');
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  const handleEditStart = (rowIndex: number, field: string, currentValue: string) => {
    setEditingCell({ rowIndex, field });
    setEditValue(currentValue || '');
  };

  const handleEditSave = () => {
    if (!editingCell) return;
    
    const updatedProduct = {
      ...enrichmentResults[editingCell.rowIndex].enrichedProduct,
      [editingCell.field]: editValue
    };
    
    onProductUpdate(editingCell.rowIndex, updatedProduct);
    setEditingCell(null);
    setEditValue('');
  };

  const handleEditCancel = () => {
    setEditingCell(null);
    setEditValue('');
  };

  const toggleRowExpansion = (rowIndex: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(rowIndex)) {
      newExpanded.delete(rowIndex);
    } else {
      newExpanded.add(rowIndex);
    }
    setExpandedRows(newExpanded);
  };

  const getStatusBadge = (result: ProductEnrichmentResult) => {
    switch (result.status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800 border-green-200">✓ Ready</Badge>;
      case 'partial':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">⚠ Review</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800 border-red-200">✗ Failed</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getDataSourceIcons = (result: ProductEnrichmentResult) => {
    const icons = [];

    // TecDoc source with status indicator
    if (result.sources.tecdoc) {
      const tecDocSuccess = result.enrichmentDetails?.tecDocData &&
                           Object.keys(result.enrichmentDetails.tecDocData).length > 0;
      icons.push(
        <div key="tecdoc" className={`flex items-center ${tecDocSuccess ? 'text-blue-600' : 'text-gray-400'}`}
             title={tecDocSuccess ? "TecDoc Data Found" : "TecDoc Search Failed"}>
          <Database className="w-3 h-3 mr-1" />
          <span className="text-xs">TecDoc</span>
          {tecDocSuccess ? <span className="text-green-500 ml-1">✓</span> : <span className="text-red-500 ml-1">✗</span>}
        </div>
      );
    } else {
      icons.push(
        <div key="tecdoc-failed" className="flex items-center text-gray-400" title="TecDoc Not Searched">
          <Database className="w-3 h-3 mr-1" />
          <span className="text-xs">TecDoc</span>
          <span className="text-gray-400 ml-1">-</span>
        </div>
      );
    }

    // Web search source with Perplexica indicator
    if (result.sources.websearch) {
      const isPerplexica = result.enrichmentDetails?.webSearchData?.perplexicaEnhanced;
      const webSearchSuccess = result.enrichmentDetails?.webSearchData &&
                              !result.enrichmentDetails.webSearchData.fallbackUsed;

      icons.push(
        <div key="websearch" className={`flex items-center ${webSearchSuccess ? 'text-purple-600' : 'text-orange-500'}`}
             title={isPerplexica ? "Perplexica Web Search" : webSearchSuccess ? "Web Search Enhanced" : "Fallback Data Used"}>
          <Globe className="w-3 h-3 mr-1" />
          <span className="text-xs">{isPerplexica ? 'Perplexica' : 'Web'}</span>
          {webSearchSuccess ? <span className="text-green-500 ml-1">✓</span> : <span className="text-orange-500 ml-1">⚠</span>}
        </div>
      );
    } else {
      icons.push(
        <div key="websearch-failed" className="flex items-center text-gray-400" title="Web Search Not Performed">
          <Globe className="w-3 h-3 mr-1" />
          <span className="text-xs">Web</span>
          <span className="text-gray-400 ml-1">-</span>
        </div>
      );
    }

    // AI enhancement source
    if (result.sources.ai) {
      icons.push(
        <div key="ai" className="flex items-center text-green-600" title="AI Enhanced">
          <Brain className="w-3 h-3 mr-1" />
          <span className="text-xs">AI</span>
          <span className="text-green-500 ml-1">✓</span>
        </div>
      );
    }

    return icons;
  };

  const renderEditableCell = (
    rowIndex: number, 
    field: string, 
    value: string, 
    isTextarea: boolean = false
  ) => {
    const isEditing = editingCell?.rowIndex === rowIndex && editingCell?.field === field;
    
    if (isEditing) {
      return (
        <div className="flex items-center space-x-2">
          {isTextarea ? (
            <Textarea
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="min-h-[60px] text-xs"
              autoFocus
            />
          ) : (
            <Input
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="text-xs"
              autoFocus
            />
          )}
          <div className="flex flex-col space-y-1">
            <Button size="sm" onClick={handleEditSave} className="h-6 px-2 text-xs">
              ✓
            </Button>
            <Button size="sm" variant="outline" onClick={handleEditCancel} className="h-6 px-2 text-xs">
              ✗
            </Button>
          </div>
        </div>
      );
    }
    
    return (
      <div 
        className="group cursor-pointer hover:bg-gray-50 p-1 rounded relative"
        onClick={() => handleEditStart(rowIndex, field, value)}
      >
        <div className={`text-xs ${isTextarea ? 'max-h-16 overflow-hidden' : ''}`}>
          {value || '-'}
        </div>
        <Edit3 className="w-3 h-3 absolute top-1 right-1 opacity-0 group-hover:opacity-50" />
      </div>
    );
  };

  const successCount = enrichmentResults.filter(r => r.status === 'success').length;
  const partialCount = enrichmentResults.filter(r => r.status === 'partial').length;
  const failedCount = enrichmentResults.filter(r => r.status === 'failed').length;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Summary */}
      <div className="bg-white border rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Shield className="w-5 h-5 text-blue-600" />
              Final Review & Validation
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              Review all enriched products before final import. Edit any fields as needed.
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">{enrichmentResults.length}</div>
            <div className="text-xs text-gray-600">Total Products</div>
          </div>
        </div>

        {/* Status Summary */}
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-green-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{successCount}</div>
            <div className="text-xs text-green-700">Ready to Import</div>
            <Progress value={(successCount / enrichmentResults.length) * 100} className="mt-2 h-2" />
          </div>
          <div className="bg-yellow-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{partialCount}</div>
            <div className="text-xs text-yellow-700">Need Review</div>
            <Progress value={(partialCount / enrichmentResults.length) * 100} className="mt-2 h-2" />
          </div>
          <div className="bg-red-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{failedCount}</div>
            <div className="text-xs text-red-700">Failed</div>
            <Progress value={(failedCount / enrichmentResults.length) * 100} className="mt-2 h-2" />
          </div>
        </div>
      </div>

      {/* Comprehensive Review Table */}
      <div className="bg-white border rounded-lg overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h4 className="font-medium text-gray-900">Product Details & Enhancement Results</h4>
          <p className="text-sm text-gray-600">Click any field to edit. Expand rows for detailed information.</p>
        </div>
        
        <div className="overflow-x-auto max-h-96">
          <table className="w-full text-xs">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="p-2 text-left border-r w-8"></th>
                <th className="p-2 text-left border-r min-w-[200px]">Product Name</th>
                <th className="p-2 text-left border-r min-w-[120px]">Manufacturer</th>
                <th className="p-2 text-left border-r min-w-[120px]">Part Number</th>
                <th className="p-2 text-left border-r min-w-[100px]">Category</th>
                <th className="p-2 text-left border-r min-w-[80px]">Images</th>
                <th className="p-2 text-left border-r min-w-[120px]">Data Sources</th>
                <th className="p-2 text-left border-r min-w-[100px]">Quality Score</th>
                <th className="p-2 text-left min-w-[80px]">Status</th>
              </tr>
            </thead>
            <tbody>
              {enrichmentResults.map((result, index) => (
                <React.Fragment key={index}>
                  <tr className="border-b hover:bg-gray-50">
                    <td className="p-2 border-r">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => toggleRowExpansion(index)}
                        className="h-6 w-6 p-0"
                      >
                        {expandedRows.has(index) ? '−' : '+'}
                      </Button>
                    </td>
                    <td className="p-2 border-r">
                      {renderEditableCell(index, 'name', result.enrichedProduct.name || '')}
                    </td>
                    <td className="p-2 border-r">
                      {renderEditableCell(index, 'manufacturer', result.enrichedProduct.manufacturer || '')}
                    </td>
                    <td className="p-2 border-r font-mono">
                      {renderEditableCell(index, 'partArticleNumber', result.enrichedProduct.partArticleNumber || '')}
                    </td>
                    <td className="p-2 border-r">
                      {result.enrichedProduct.category || '-'}
                    </td>
                    <td className="p-2 border-r text-center">
                      <div className="flex items-center justify-center">
                        <ImageIcon className="w-4 h-4 text-gray-400" />
                        <span className="ml-1">{result.enrichmentDetails?.imagesFound || 0}</span>
                      </div>
                    </td>
                    <td className="p-2 border-r">
                      <div className="flex flex-wrap gap-1">
                        {getDataSourceIcons(result)}
                      </div>
                    </td>
                    <td className="p-2 border-r">
                      <div className="space-y-1">
                        <div className="flex items-center">
                          <TrendingUp className="w-3 h-3 text-blue-600 mr-1" />
                          <span>{result.dataQuality?.completeness || 0}%</span>
                        </div>
                        <Progress value={result.dataQuality?.completeness || 0} className="h-1" />
                      </div>
                    </td>
                    <td className="p-2">
                      {getStatusBadge(result)}
                    </td>
                  </tr>
                  
                  {/* Expanded Row Details */}
                  {expandedRows.has(index) && (
                    <tr className="bg-gray-50">
                      <td colSpan={9} className="p-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <h5 className="font-medium text-gray-900 mb-2">Description & Specifications</h5>
                            {renderEditableCell(
                              index, 
                              'descriptionAndSpecifications', 
                              result.enrichedProduct.descriptionAndSpecifications || '',
                              true
                            )}
                          </div>
                          <div>
                            <h5 className="font-medium text-gray-900 mb-2">Vehicle Compatibility</h5>
                            {renderEditableCell(
                              index, 
                              'vehicleCompatibility', 
                              result.enrichedProduct.vehicleCompatibility || '',
                              true
                            )}
                          </div>
                        </div>
                        
                        {/* Enhancement Details */}
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <h5 className="font-medium text-gray-900 mb-2">Enhancement Details</h5>
                          <div className="grid grid-cols-3 gap-4 text-xs">
                            <div>
                              <span className="font-medium">Data Quality:</span>
                              <div className="mt-1">
                                <div>Completeness: {result.dataQuality?.completeness || 0}%</div>
                                <div>Accuracy: {result.dataQuality?.accuracy || 0}%</div>
                                <div>Marketplace Ready: {result.dataQuality?.marketplaceReady ? 'Yes' : 'No'}</div>
                              </div>
                            </div>
                            <div>
                              <span className="font-medium">Sources Used:</span>
                              <div className="mt-1 space-y-1">
                                <div className="flex items-center">
                                  <span className="w-20">TecDoc:</span>
                                  {result.sources.tecdoc ? (
                                    result.enrichmentDetails?.tecDocData ?
                                      <span className="text-green-600">✓ Data Found</span> :
                                      <span className="text-red-600">✗ No Data</span>
                                  ) : (
                                    <span className="text-gray-500">- Not Searched</span>
                                  )}
                                </div>
                                <div className="flex items-center">
                                  <span className="w-20">Web Search:</span>
                                  {result.sources.websearch ? (
                                    result.enrichmentDetails?.webSearchData?.perplexicaEnhanced ?
                                      <span className="text-purple-600">✓ Perplexica</span> :
                                      result.enrichmentDetails?.webSearchData?.fallbackUsed ?
                                        <span className="text-orange-600">⚠ Fallback</span> :
                                        <span className="text-green-600">✓ Enhanced</span>
                                  ) : (
                                    <span className="text-gray-500">- Not Searched</span>
                                  )}
                                </div>
                                <div className="flex items-center">
                                  <span className="w-20">AI:</span>
                                  {result.sources.ai ?
                                    <span className="text-green-600">✓ Enhanced</span> :
                                    <span className="text-gray-500">- Not Applied</span>
                                  }
                                </div>
                              </div>
                            </div>
                            <div>
                              <span className="font-medium">Issues:</span>
                              <div className="mt-1">
                                {result.errors.length > 0 && (
                                  <div className="text-red-600">
                                    {result.errors.slice(0, 2).map((error, idx) => (
                                      <div key={idx}>• {error}</div>
                                    ))}
                                  </div>
                                )}
                                {result.warnings?.length > 0 && (
                                  <div className="text-yellow-600">
                                    {result.warnings.slice(0, 2).map((warning, idx) => (
                                      <div key={idx}>• {warning}</div>
                                    ))}
                                  </div>
                                )}
                                {result.errors.length === 0 && (!result.warnings || result.warnings.length === 0) && (
                                  <div className="text-green-600">No issues found</div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-4 border-t">
        <Button
          variant="outline"
          onClick={onBackToEnrichment}
          className="flex items-center gap-2"
        >
          ← Back to Enrichment
        </Button>
        
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-600">
            {successCount} products ready • {partialCount} need review • {failedCount} failed
          </div>
          <Button
            onClick={onProceedToImport}
            disabled={successCount === 0}
            className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
          >
            <CheckCircle2 className="w-4 h-4" />
            Import {successCount} Products
          </Button>
        </div>
      </div>
    </div>
  );
};

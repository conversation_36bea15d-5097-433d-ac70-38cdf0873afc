import React from 'react';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface VehicleCompatibilityTextInputProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  description?: string;
  placeholder?: string;
}

/**
 * Simple text input for vehicle compatibility
 * Replaces the complex dropdown selector for better import compatibility
 */
const VehicleCompatibilityTextInput: React.FC<VehicleCompatibilityTextInputProps> = ({
  value = '',
  onChange,
  label = 'Vehicle Type Compatibility',
  description = 'Enter compatible vehicle types (e.g., "BMW 3 Series, Audi A4, Mercedes C-Class")',
  placeholder = 'Enter compatible vehicle types...'
}) => {
  return (
    <div className="space-y-2">
      <Label htmlFor="vehicle-compatibility">
        {label}
      </Label>
      
      {description && (
        <p className="text-sm text-muted-foreground">
          {description}
        </p>
      )}
      
      <Textarea
        id="vehicle-compatibility"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        rows={3}
        className="resize-none"
      />
      
      <p className="text-xs text-muted-foreground">
        Separate multiple vehicle types with commas or line breaks
      </p>
    </div>
  );
};

export default VehicleCompatibilityTextInput;

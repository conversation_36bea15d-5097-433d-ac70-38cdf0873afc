import React from 'react';
import { Badge } from '@/components/ui/badge';
import { CheckCircle2, Alert<PERSON><PERSON>gle, <PERSON><PERSON>hart3, Target, Layers } from 'lucide-react';
import { AIColumnMappingResult } from '@/services/aiColumnMappingService';

interface AIMappingSummaryProps {
  aiMappingResult: AIColumnMappingResult;
  className?: string;
}

export const AIMappingSummary: React.FC<AIMappingSummaryProps> = ({
  aiMappingResult,
  className = ''
}) => {
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600 bg-green-50 border-green-200';
    if (confidence >= 75) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (confidence >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 90) return 'Excellent';
    if (confidence >= 75) return 'Good';
    if (confidence >= 60) return 'Fair';
    return 'Needs Review';
  };

  const criticalMappings = aiMappingResult.mappings?.filter(m => 
    ['partArticleNumber', 'name', 'manufacturer', 'retailPrice'].includes(m.targetField || m.field || '')
  ) || [];

  const hasIssues = (aiMappingResult.confidence || 0) < 75 || 
                   (aiMappingResult.unmappedColumns || []).length > 5 ||
                   criticalMappings.length < 3;

  return (
    <div className={`bg-white border rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <BarChart3 className="w-5 h-5 text-blue-600" />
          <h4 className="font-medium text-gray-900">Column Analysis Summary</h4>
        </div>
        <Badge 
          variant="outline" 
          className={getConfidenceColor(aiMappingResult.confidence || 0)}
        >
          {aiMappingResult.confidence || 0}% {getConfidenceLabel(aiMappingResult.confidence || 0)}
        </Badge>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <Target className="w-4 h-4 text-blue-600 mr-1" />
            <span className="text-lg font-bold text-blue-600">
              {(aiMappingResult.mappings || []).length}
            </span>
          </div>
          <div className="text-xs text-gray-600">Mappings Found</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <Layers className="w-4 h-4 text-green-600 mr-1" />
            <span className="text-lg font-bold text-green-600">
              {aiMappingResult.suggestedCategory || 'N/A'}
            </span>
          </div>
          <div className="text-xs text-gray-600">Category</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            {hasIssues ? (
              <AlertTriangle className="w-4 h-4 text-yellow-600 mr-1" />
            ) : (
              <CheckCircle2 className="w-4 h-4 text-green-600 mr-1" />
            )}
            <span className={`text-lg font-bold ${hasIssues ? 'text-yellow-600' : 'text-green-600'}`}>
              {hasIssues ? 'Review' : 'Ready'}
            </span>
          </div>
          <div className="text-xs text-gray-600">Status</div>
        </div>
      </div>

      {/* Category Selection */}
      <div className="bg-gray-50 rounded-lg p-3 mb-4">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm font-medium text-gray-900">
              Category: <span className="text-blue-600">{aiMappingResult.suggestedCategory}</span>
            </div>
            {aiMappingResult.suggestedSubcategory && (
              <div className="text-xs text-gray-600 mt-1">
                Subcategory: {aiMappingResult.suggestedSubcategory}
              </div>
            )}
          </div>
          <CheckCircle2 className="w-4 h-4 text-green-600" />
        </div>
      </div>

      {/* Critical Mappings */}
      <div className="space-y-2 mb-4">
        <h5 className="text-sm font-medium text-gray-900">Key Field Mappings:</h5>
        <div className="grid grid-cols-2 gap-2">
          {criticalMappings.slice(0, 4).map((mapping, index) => (
            <div key={index} className="flex items-center justify-between bg-green-50 rounded p-2 text-xs">
              <span className="font-medium text-gray-700">
                {mapping.originalColumn || mapping.col}
              </span>
              <span className="text-green-700">
                → {mapping.targetField || mapping.field}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Issues and Warnings */}
      {hasIssues && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div className="text-xs">
              <div className="font-medium text-yellow-800 mb-1">Review Recommended:</div>
              <ul className="text-yellow-700 space-y-1">
                {(aiMappingResult.confidence || 0) < 75 && (
                  <li>• Low confidence score - verify mappings</li>
                )}
                {criticalMappings.length < 3 && (
                  <li>• Missing critical field mappings</li>
                )}
                {(aiMappingResult.unmappedColumns || []).length > 5 && (
                  <li>• Many unmapped columns - check for missing data</li>
                )}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Unmapped Columns Summary */}
      {(aiMappingResult.unmappedColumns || []).length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="text-xs text-gray-600">
            <span className="font-medium">Unmapped columns:</span> {(aiMappingResult.unmappedColumns || []).length} 
            {(aiMappingResult.unmappedColumns || []).length <= 3 ? (
              <span className="ml-1">
                ({(aiMappingResult.unmappedColumns || []).join(', ')})
              </span>
            ) : (
              <span className="ml-1">
                ({(aiMappingResult.unmappedColumns || []).slice(0, 3).join(', ')}, +{(aiMappingResult.unmappedColumns || []).length - 3} more)
              </span>
            )}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            These will be added to product descriptions
          </div>
        </div>
      )}
    </div>
  );
};

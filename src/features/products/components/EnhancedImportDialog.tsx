/**
 * Enhanced Product Import Dialog with AI-Powered Web Search
 * 3-Step Flow: File Upload → AI Enhancement → Review & Complete
 */

import React, { useState, useCallback } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, Search, CheckCircle, XCircle, AlertCircle, Eye } from 'lucide-react';
import { toast } from 'sonner';
import { 
  enhancedImportService, 
  type ImportSession, 
  type ImportProgress,
  type EnhancedProductData 
} from '@/services/enhancedImportService';

interface EnhancedImportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete: (products: EnhancedProductData[]) => void;
}

export function EnhancedImportDialog({ 
  open, 
  onOpenChange, 
  onImportComplete 
}: EnhancedImportDialogProps) {
  const [currentStep, setCurrentStep] = useState<'upload' | 'enhance' | 'review'>('upload');
  const [session, setSession] = useState<ImportSession | null>(null);
  const [progress, setProgress] = useState<ImportProgress | null>(null);
  const [reviewData, setReviewData] = useState<Array<{
    original: any;
    enhanced: EnhancedProductData;
    status: 'pending' | 'approved' | 'rejected';
  }>>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const validTypes = [
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      
      if (!validTypes.includes(file.type) && !file.name.match(/\.(csv|xlsx|xls)$/i)) {
        toast.error('Please select a CSV or Excel file');
        return;
      }
      
      setSelectedFile(file);
    }
  }, []);

  const startImport = useCallback(async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    
    try {
      // Step 1: Start import session
      const sessionId = await enhancedImportService.startImportSession(
        selectedFile,
        (progressUpdate) => {
          setProgress(progressUpdate);
        }
      );

      const importSession = enhancedImportService.getSession(sessionId);
      if (importSession) {
        setSession(importSession);
        setCurrentStep('enhance');
        
        // Step 2: Start AI enhancement
        await enhancedImportService.enhanceProducts(sessionId);
        
        // Step 3: Prepare review data
        const { reviewData: data } = enhancedImportService.getEnhancedDataForReview(sessionId);
        setReviewData(data);
        setCurrentStep('review');
        
        toast.success('AI enhancement completed! Please review the results.');
      }
    } catch (error) {
      console.error('Import failed:', error);
      toast.error(error instanceof Error ? error.message : 'Import failed');
    } finally {
      setIsProcessing(false);
    }
  }, [selectedFile]);

  const handleProductApproval = useCallback((index: number, approved: boolean) => {
    setReviewData(prev => prev.map((item, i) => 
      i === index ? { ...item, status: approved ? 'approved' : 'rejected' } : item
    ));
  }, []);

  const completeImport = useCallback(async () => {
    if (!session) return;

    const approvedProducts = reviewData
      .filter(item => item.status === 'approved')
      .map(item => item.enhanced);

    if (approvedProducts.length === 0) {
      toast.error('Please approve at least one product to import');
      return;
    }

    try {
      await enhancedImportService.completeImport(session.id, approvedProducts);
      onImportComplete(approvedProducts);
      onOpenChange(false);
      
      // Reset state
      setCurrentStep('upload');
      setSession(null);
      setProgress(null);
      setReviewData([]);
      setSelectedFile(null);
      
      toast.success(`Successfully imported ${approvedProducts.length} products!`);
    } catch (error) {
      console.error('Import completion failed:', error);
      toast.error('Failed to complete import');
    }
  }, [session, reviewData, onImportComplete, onOpenChange]);

  const getStepIcon = (step: string, status: string) => {
    if (status === 'completed') return <CheckCircle className="w-5 h-5 text-green-500" />;
    if (status === 'error') return <XCircle className="w-5 h-5 text-red-500" />;
    if (status === 'in-progress') return <AlertCircle className="w-5 h-5 text-blue-500 animate-pulse" />;
    return <div className="w-5 h-5 rounded-full border-2 border-gray-300" />;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            AI-Powered Product Import
          </DialogTitle>
        </DialogHeader>

        {/* Progress Steps */}
        <div className="flex items-center justify-between mb-6">
          {session?.steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className="flex flex-col items-center">
                {getStepIcon(step.id, step.status)}
                <span className="text-sm mt-1 text-center max-w-24">{step.name}</span>
                {step.status === 'in-progress' && (
                  <Progress value={step.progress} className="w-20 mt-1" />
                )}
              </div>
              {index < session.steps.length - 1 && (
                <div className="w-16 h-px bg-gray-300 mx-4" />
              )}
            </div>
          ))}
        </div>

        {/* Current Progress */}
        {progress && (
          <Card className="mb-4">
            <CardContent className="pt-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">{progress.message}</span>
                <span className="text-sm text-gray-500">{progress.progress}%</span>
              </div>
              <Progress value={progress.progress} />
            </CardContent>
          </Card>
        )}

        {/* Step Content */}
        <Tabs value={currentStep} className="w-full">
          {/* Step 1: File Upload */}
          <TabsContent value="upload" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Upload Product File
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <div className="space-y-2">
                    <p className="text-lg font-medium">Upload CSV or Excel file</p>
                    <p className="text-sm text-gray-500">
                      Supported formats: .csv, .xlsx, .xls
                    </p>
                    <input
                      type="file"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileSelect}
                      className="hidden"
                      id="file-upload"
                    />
                    <label
                      htmlFor="file-upload"
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer"
                    >
                      Choose File
                    </label>
                  </div>
                </div>

                {selectedFile && (
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{selectedFile.name}</p>
                      <p className="text-sm text-gray-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <Button
                      onClick={startImport}
                      disabled={isProcessing}
                      className="flex items-center gap-2"
                    >
                      {isProcessing ? (
                        <>
                          <AlertCircle className="w-4 h-4 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Search className="w-4 h-4" />
                          Start AI Enhancement
                        </>
                      )}
                    </Button>
                  </div>
                )}

                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">What happens next?</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Extract product data from your file</li>
                    <li>• Search the web for comprehensive product information</li>
                    <li>• Generate detailed descriptions and vehicle compatibility</li>
                    <li>• Present enhanced data for your review</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Step 2: AI Enhancement (Auto-progresses) */}
          <TabsContent value="enhance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="w-5 h-5" />
                  AI Web Search Enhancement
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="animate-pulse mb-4">
                    <Search className="w-16 h-16 mx-auto text-blue-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">Enhancing Product Data</h3>
                  <p className="text-gray-600 mb-4">
                    Our AI is searching the web for comprehensive product information...
                  </p>
                  {session && (
                    <div className="max-w-md mx-auto">
                      <div className="flex justify-between text-sm mb-2">
                        <span>Progress</span>
                        <span>{session.steps[1]?.progress || 0}%</span>
                      </div>
                      <Progress value={session.steps[1]?.progress || 0} />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Step 3: Review & Complete */}
          <TabsContent value="review" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  Review Enhanced Products
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reviewData.map((item, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium">{item.enhanced.productName}</h4>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant={item.status === 'approved' ? 'default' : 'outline'}
                            onClick={() => handleProductApproval(index, true)}
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant={item.status === 'rejected' ? 'destructive' : 'outline'}
                            onClick={() => handleProductApproval(index, false)}
                          >
                            <XCircle className="w-4 h-4 mr-1" />
                            Reject
                          </Button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p><strong>Part Number:</strong> {item.enhanced.partArticleNumber}</p>
                          <p><strong>Manufacturer:</strong> {item.enhanced.manufacturer}</p>
                          <p><strong>Category:</strong> {item.enhanced.category}</p>
                        </div>
                        <div>
                          <p><strong>Compatibility:</strong> {item.enhanced.vehicleCompatibility}</p>
                          <p><strong>Confidence:</strong> 
                            <Badge variant={item.enhanced.confidence > 80 ? 'default' : 'secondary'} className="ml-1">
                              {item.enhanced.confidence}%
                            </Badge>
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex justify-between items-center mt-6 pt-4 border-t">
                  <div className="text-sm text-gray-600">
                    {reviewData.filter(item => item.status === 'approved').length} of {reviewData.length} products approved
                  </div>
                  <Button onClick={completeImport} className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Complete Import
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

/**
 * Part Lookup Dialog
 *
 * Simplified single-part lookup interface that replaces the complex ImportDialog.
 * Uses OpenAI to identify parts based on part numbers and provides structured product data.
 *
 * Current Implementation:
 * - Uses OpenAI GPT-4o-mini for part identification
 * - Based on automotive knowledge rather than live web search
 * - Provides reasonable estimates for pricing and availability
 *
 * Future Enhancements:
 * - Add web search capabilities for real-time data
 * - Integrate with TecDoc API for verified part information
 * - Add image verification and download functionality
 * - Support batch part lookup
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { TyreProduct, BrakeProduct } from '../types/product.types';
import { lookupPartByNumber, PartLookupResult } from '@/services/partLookupService';
import { mapToProduct, validateLookupResult } from '@/services/partLookupMappingService';
import { fetchImageFromUrl, downloadImageBlob } from '@/services/imageProxyService';
import { useUser } from '@/contexts/UserContext';
import { toast } from 'sonner';
import {
  Search,
  Loader2,
  CheckCircle2,
  AlertTriangle,
  Download,
  Edit3,
  X,
  Package,
  DollarSign,
  Upload,
  Image as ImageIcon,
  Trash2
} from 'lucide-react';

interface PartLookupDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (products: Partial<TyreProduct | BrakeProduct>[]) => Promise<void>;
  categoryId: string;
}

export const PartLookupDialog: React.FC<PartLookupDialogProps> = ({
  isOpen,
  onClose,
  onImport,
  categoryId,
}) => {
  const { t } = useTranslation();
  const { userRole } = useUser();
  
  // Form state - single input like playground
  const [partNumber, setPartNumber] = useState('');
  
  // Lookup state
  const [isLooking, setIsLooking] = useState(false);
  const [lookupResult, setLookupResult] = useState<PartLookupResult | null>(null);
  const [mappedProduct, setMappedProduct] = useState<Partial<TyreProduct | BrakeProduct> | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  
  // Edit state
  const [isEditing, setIsEditing] = useState(false);
  const [editableStockQuantity, setEditableStockQuantity] = useState<number>(0);
  const [editableRetailPrice, setEditableRetailPrice] = useState<number>(0);
  
  // Import state
  const [isImporting, setIsImporting] = useState(false);

  // Manual image upload state
  const [manualImage, setManualImage] = useState<File | null>(null);
  const [manualImagePreview, setManualImagePreview] = useState<string | null>(null);
  const [imageSource, setImageSource] = useState<'web' | 'manual'>('web');

  const resetState = () => {
    setPartNumber('');
    setLookupResult(null);
    setMappedProduct(null);
    setValidationErrors([]);
    setIsEditing(false);
    setEditableStockQuantity(0);
    setEditableRetailPrice(0);
    // Reset manual image state
    if (manualImagePreview) {
      URL.revokeObjectURL(manualImagePreview);
    }
    setManualImage(null);
    setManualImagePreview(null);
    setImageSource('web');
  };

  const resetForNewSearch = () => {
    setLookupResult(null);
    setMappedProduct(null);
    setValidationErrors([]);
    setIsEditing(false);
    setEditableStockQuantity(0);
    setEditableRetailPrice(0);
    // Reset manual image state but keep part number
    if (manualImagePreview) {
      URL.revokeObjectURL(manualImagePreview);
    }
    setManualImage(null);
    setManualImagePreview(null);
    setImageSource('web');
  };

  const handleLookup = async () => {
    if (!partNumber.trim()) {
      toast.error('Please enter a part number');
      return;
    }

    setIsLooking(true);
    // Reset state for new search but keep part number
    resetForNewSearch();

    try {
      console.log('Looking up part:', partNumber.trim());

      // Perform the lookup - exact same as playground
      const result = await lookupPartByNumber(partNumber.trim());
      setLookupResult(result);

      // Validate the result
      const validation = validateLookupResult(result);
      if (!validation.valid) {
        setValidationErrors(validation.errors);
        toast.warning(`Part found but has validation issues: ${validation.errors.join(', ')}`);
      } else {
        const confidence = result.data?.confidence || 0;
        toast.success(`Part found with ${confidence}% confidence`);
      }

      // Map to product structure
      console.log('Lookup result:', result);
      console.log('Lookup result success:', result.success);
      console.log('Lookup result data:', result.data);

      try {
        // Use dummy user data for mapping since we don't have real user context
        const product = mapToProduct(result, 'temp-user-id', 'Temp Supplier');
        console.log('Mapped product:', product);
        console.log('Mapped product fields:', {
          name: product.name,
          sku: product.sku,
          partArticleNumber: product.partArticleNumber,
          manufacturer: product.manufacturer,
          category: product.category,
          subcategory: product.subcategory,
          description: product.description,
          vehicleCompatibility: product.vehicleCompatibility
        });
        setMappedProduct(product);
        setEditableStockQuantity(product.stockQuantity || 0);
        setEditableRetailPrice(product.retailPrice || 0);
      } catch (mappingError) {
        console.error('Product mapping failed:', mappingError);
        console.error('Mapping error details:', mappingError);
        toast.error('Failed to map product data');
      }

    } catch (error) {
      console.error('Lookup failed:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to lookup part');
    } finally {
      setIsLooking(false);
    }
  };

  const handleDownloadImage = async () => {
    if (!lookupResult?.primaryImageUrl) return;

    try {
      const result = await fetchImageFromUrl(lookupResult.primaryImageUrl);
      if (result.success && result.blob) {
        const filename = `${lookupResult.partArticleNumber || 'part'}-image`;
        downloadImageBlob(result.blob, filename);
        toast.success('Image downloaded successfully');
      } else {
        toast.error(result.error || 'Failed to download image');
      }
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error('Failed to download image');
    }
  };

  const handleSaveEdits = () => {
    if (mappedProduct) {
      setMappedProduct({
        ...mappedProduct,
        stockQuantity: editableStockQuantity,
        retailPrice: editableRetailPrice,
      });
      setIsEditing(false);
      toast.success('Product details updated');
    }
  };

  const handleManualImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload a valid image file (JPG, PNG, GIF, or WebP)');
      return;
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      toast.error('Image file size must be less than 5MB');
      return;
    }

    setManualImage(file);
    setImageSource('manual');

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    setManualImagePreview(previewUrl);

    toast.success('Image uploaded successfully');
  };

  const handleRemoveManualImage = () => {
    if (manualImagePreview) {
      URL.revokeObjectURL(manualImagePreview);
    }
    setManualImage(null);
    setManualImagePreview(null);
    setImageSource('web');
    toast.info('Manual image removed');
  };

  const handleAccept = async () => {
    if (!mappedProduct) {
      toast.error('No product to import');
      return;
    }

    if (validationErrors.length > 0) {
      const proceed = window.confirm(
        `This product has validation issues:\n${validationErrors.join('\n')}\n\nDo you want to proceed anyway?`
      );
      if (!proceed) return;
    }

    setIsImporting(true);
    try {
      // Prepare product with manual image if uploaded
      let productToImport = { ...mappedProduct };

      if (imageSource === 'manual' && manualImage) {
        // Convert manual image to base64 for storage
        const reader = new FileReader();
        const imageDataUrl = await new Promise<string>((resolve, reject) => {
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(manualImage);
        });

        productToImport.primaryImage = imageDataUrl;
        productToImport.imageSource = 'manual';
      } else if (imageSource === 'web') {
        productToImport.imageSource = 'web';
      }

      await onImport([productToImport]);
      toast.success('Product imported successfully');
      resetState();
      onClose();
    } catch (error) {
      console.error('Import failed:', error);
      toast.error('Failed to import product');
    } finally {
      setIsImporting(false);
    }
  };

  const handleReject = () => {
    resetState();
    toast.info('Product discarded');
  };

  const handleClose = () => {
    resetState();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-5xl max-h-[95vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Part Lookup & Import
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 pr-2">
          {/* Search Form */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Search for Part</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="partNumber">Part Number</Label>
                <Input
                  id="partNumber"
                  value={partNumber}
                  onChange={(e) => setPartNumber(e.target.value)}
                  placeholder="Enter partArticleNumber (e.g., 05-90283-SX)"
                  disabled={isLooking}
                  className="text-lg"
                />
                <p className="text-sm text-gray-500">
                  Enter only the part number - AI will search the web for all other details
                </p>
              </div>
              
              <Button 
                onClick={handleLookup} 
                disabled={!partNumber.trim() || isLooking}
                className="w-full"
              >
                {isLooking ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-4 w-4" />
                    Search Part
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium">Validation Issues:</div>
                <ul className="list-disc list-inside mt-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Results */}
          {mappedProduct && lookupResult && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between py-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Product Found
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant={(lookupResult.data?.confidence || 0) >= 75 ? "default" : "secondary"}>
                    {lookupResult.data?.confidence || 0}% Confidence
                  </Badge>
                  {lookupResult.data?.primaryImageVerified && (
                    <Badge variant="outline" className="text-green-600">
                      Image Verified
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Compact Product Details Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">Product Name</Label>
                    <p className="font-medium">{mappedProduct.name}</p>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">SKU</Label>
                    <p className="font-medium">{mappedProduct.sku}</p>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">Part Number</Label>
                    <p className="font-medium">{mappedProduct.partArticleNumber}</p>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">Manufacturer</Label>
                    <p className="font-medium">{mappedProduct.manufacturer}</p>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">Category</Label>
                    <p className="font-medium">{mappedProduct.category}</p>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">Subcategory</Label>
                    <p className="font-medium">{mappedProduct.subcategory || 'Not specified'}</p>
                  </div>
                  <div className="col-span-2">
                    <Label className="text-xs font-medium text-muted-foreground">Vehicle Compatibility</Label>
                    <p className="font-medium">{mappedProduct.vehicleCompatibility || 'Not specified'}</p>
                  </div>
                </div>

                {/* Tyre Specifications - Compact */}
                {mappedProduct.category === 'tyres' && 'width' in mappedProduct && (
                  <div className="border-t pt-3">
                    <Label className="text-sm font-medium mb-2 block">Tyre Specifications</Label>
                    <div className="grid grid-cols-3 md:grid-cols-6 gap-2 text-xs">
                      <div><span className="text-muted-foreground">Width:</span> {(mappedProduct as any).width || 'N/A'}mm</div>
                      <div><span className="text-muted-foreground">Ratio:</span> {(mappedProduct as any).aspectRatio || 'N/A'}%</div>
                      <div><span className="text-muted-foreground">Rim:</span> {(mappedProduct as any).rimDiameter || 'N/A'}"</div>
                      <div><span className="text-muted-foreground">Load:</span> {(mappedProduct as any).loadIndex || 'N/A'}</div>
                      <div><span className="text-muted-foreground">Speed:</span> {(mappedProduct as any).speedRating || 'N/A'}</div>
                      <div><span className="text-muted-foreground">Season:</span> {(mappedProduct as any).season || 'N/A'}</div>
                    </div>
                  </div>
                )}

                <Separator />

                {/* Image Section - Compact */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label className="text-sm font-medium">Product Image</Label>
                      {(mappedProduct.primaryImage || manualImagePreview) && (
                        <Badge variant="outline" className={imageSource === 'web' ? 'text-blue-600' : 'text-green-600'}>
                          {imageSource === 'web' ? 'Web Search' : 'Manual Upload'}
                        </Badge>
                      )}
                    </div>

                    {/* Display current image (web or manual) */}
                    {(imageSource === 'manual' && manualImagePreview) || (imageSource === 'web' && mappedProduct.primaryImage) ? (
                      <div>
                        <img
                          src={imageSource === 'manual' ? manualImagePreview : mappedProduct.primaryImage}
                          alt="Product"
                          className="w-full max-w-[200px] h-auto border rounded-lg"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                        <div className="flex gap-1 mt-2">
                          {imageSource === 'web' && (
                            <Button variant="outline" size="sm" onClick={handleDownloadImage}>
                              <Download className="mr-1 h-3 w-3" />
                              Download
                            </Button>
                          )}
                          {imageSource === 'manual' && (
                            <Button variant="outline" size="sm" onClick={handleRemoveManualImage}>
                              <Trash2 className="mr-1 h-3 w-3" />
                              Remove
                            </Button>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-3 text-muted-foreground border-2 border-dashed border-gray-200 rounded-lg">
                        <ImageIcon className="mx-auto h-6 w-6 mb-1" />
                        <p className="text-xs">No image available</p>
                      </div>
                    )}

                    {/* Manual Upload Section - Compact */}
                    <div className="border-t pt-2 mt-2">
                      <div className="flex items-center gap-2">
                        <input
                          type="file"
                          accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                          onChange={handleManualImageUpload}
                          className="hidden"
                          id="manual-image-upload"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById('manual-image-upload')?.click()}
                          className="flex-1"
                        >
                          <Upload className="mr-1 h-3 w-3" />
                          {manualImage ? 'Replace' : 'Upload'}
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">Max 5MB - Upload if web image is inaccurate</p>
                    </div>
                  </div>

                  {/* Description - Organized 5 Sections */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Product Description</Label>
                    <div className="space-y-2 max-h-[300px] overflow-y-auto">
                      {lookupResult.data?.description && (
                        <>
                          <div className="bg-blue-50 p-2 rounded border">
                            <Label className="text-xs font-semibold text-blue-800">General Information</Label>
                            <p className="text-xs mt-1">{lookupResult.data.description.generalInformation || 'Not available'}</p>
                          </div>
                          <div className="bg-green-50 p-2 rounded border">
                            <Label className="text-xs font-semibold text-green-800">Technical Information</Label>
                            <p className="text-xs mt-1">{lookupResult.data.description.technicalInformation || 'Not available'}</p>
                          </div>
                          <div className="bg-purple-50 p-2 rounded border">
                            <Label className="text-xs font-semibold text-purple-800">Vehicle Applicability</Label>
                            <p className="text-xs mt-1">{lookupResult.data.description.applicability || 'Not available'}</p>
                          </div>
                          <div className="bg-orange-50 p-2 rounded border">
                            <Label className="text-xs font-semibold text-orange-800">Original Numbers</Label>
                            <p className="text-xs mt-1">{lookupResult.data.description.originalNumbers || 'Not available'}</p>
                          </div>
                          <div className="bg-red-50 p-2 rounded border">
                            <Label className="text-xs font-semibold text-red-800">OEM Numbers</Label>
                            <p className="text-xs mt-1">{lookupResult.data.description.oemNumbers || 'Not available'}</p>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Inventory & Pricing - Compact */}
                <div className="bg-gray-50 p-3 rounded border">
                  <div className="flex items-center justify-between mb-3">
                    <Label className="text-sm font-medium">Inventory & Pricing</Label>
                    <Button variant="outline" size="sm" onClick={() => setIsEditing(!isEditing)}>
                      <Edit3 className="mr-1 h-3 w-3" />
                      {isEditing ? 'Cancel' : 'Edit'}
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label className="text-xs font-medium text-muted-foreground">Stock Quantity</Label>
                      {isEditing ? (
                        <Input
                          type="number"
                          value={editableStockQuantity}
                          onChange={(e) => setEditableStockQuantity(Number(e.target.value))}
                          min="0"
                          size="sm"
                          className="mt-1"
                        />
                      ) : (
                        <div className="flex items-center gap-1 mt-1">
                          <Package className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm font-medium">{editableStockQuantity}</span>
                        </div>
                      )}
                    </div>
                    <div>
                      <Label className="text-xs font-medium text-muted-foreground">Retail Price</Label>
                      {isEditing ? (
                        <Input
                          type="number"
                          value={editableRetailPrice}
                          onChange={(e) => setEditableRetailPrice(Number(e.target.value))}
                          min="0"
                          step="0.01"
                          size="sm"
                          className="mt-1"
                        />
                      ) : (
                        <div className="flex items-center gap-1 mt-1">
                          <DollarSign className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm font-medium">{editableRetailPrice.toLocaleString('fr-DZ')} DZD</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {isEditing && (
                    <Button onClick={handleSaveEdits} size="sm" className="mt-3">
                      <CheckCircle2 className="mr-1 h-3 w-3" />
                      Save Changes
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Always show raw lookup result for comparison with playground */}
          {lookupResult && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2 text-blue-800">
                  <AlertTriangle className="h-5 w-5" />
                  Raw JSON Output (100% Copy of Playground Implementation)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-white p-3 rounded border overflow-auto max-h-60">
                  {JSON.stringify(lookupResult, null, 2)}
                </pre>
                <p className="text-sm text-blue-700 mt-2">
                  This is the exact same implementation as your playground. Compare this output with your playground results.
                </p>
              </CardContent>
            </Card>
          )}

          {/* Show validation issues if mapping failed */}
          {lookupResult && !mappedProduct && (
            <Card className="border-red-200 bg-red-50">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2 text-red-800">
                  <AlertTriangle className="h-5 w-5" />
                  Validation Issues
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-red-700">
                  The part was found but couldn't be mapped to a product. Check console for mapping errors.
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="flex-shrink-0 border-t pt-4 mt-4">
          <div className="flex items-center justify-between w-full">
            <Button variant="outline" onClick={handleClose}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>

            {mappedProduct && (
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={handleReject}>
                  Reject
                </Button>
                <Button
                  onClick={handleAccept}
                  disabled={isImporting}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isImporting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Importing...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Accept & Import
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

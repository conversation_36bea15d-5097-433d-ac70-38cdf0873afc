import React from 'react';
import { <PERSON><PERSON><PERSON>cle2, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Clock } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { EnrichmentProgress } from '@/services/dataEnrichmentService';

interface EnrichmentProgressStepperProps {
  progress: EnrichmentProgress;
  className?: string;
}

interface StepConfig {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
}

const ENRICHMENT_STEPS: StepConfig[] = [
  {
    id: 'tecdoc',
    name: 'TecDoc Data Fetch',
    description: 'Fetching automotive parts data from TecDoc API',
    icon: <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 text-sm font-bold">1</div>
  },
  {
    id: 'websearch',
    name: 'Web Enhancement',
    description: 'Searching for additional product specifications',
    icon: <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 text-sm font-bold">2</div>
  },
  {
    id: 'enhancement',
    name: 'Product Enhancement',
    description: 'Generating professional product names and descriptions',
    icon: <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 text-sm font-bold">3</div>
  },
  {
    id: 'validation',
    name: 'Data Validation',
    description: 'Validating product completeness and marketplace readiness',
    icon: <div className="w-6 h-6 rounded-full bg-orange-100 flex items-center justify-center text-orange-600 text-sm font-bold">4</div>
  }
];

export const EnrichmentProgressStepper: React.FC<EnrichmentProgressStepperProps> = ({
  progress,
  className = ''
}) => {
  const getStepStatus = (stepId: string) => {
    const currentStepIndex = ENRICHMENT_STEPS.findIndex(s => s.id === progress.step);
    const stepIndex = ENRICHMENT_STEPS.findIndex(s => s.id === stepId);
    
    if (progress.step === 'complete') {
      return 'completed';
    }
    
    if (stepIndex < currentStepIndex) {
      return 'completed';
    } else if (stepIndex === currentStepIndex) {
      return progress.status === 'error' ? 'error' : 'processing';
    } else {
      return 'pending';
    }
  };

  const getStepIcon = (step: StepConfig, status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="w-6 h-6 text-green-600" />;
      case 'processing':
        return <Loader2 className="w-6 h-6 text-blue-600 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-6 h-6 text-red-600" />;
      default:
        return <Clock className="w-6 h-6 text-gray-400" />;
    }
  };

  const getProgressPercentage = () => {
    if (progress.total === 0) return 0;
    return Math.round((progress.completed / progress.total) * 100);
  };

  const getCurrentStepProgress = () => {
    if (progress.step === 'complete') return 100;
    return getProgressPercentage();
  };

  return (
    <div className={`bg-white border rounded-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Data Enrichment Progress</h3>
          <p className="text-sm text-gray-600">
            {progress.step === 'complete' 
              ? 'All products have been processed and enriched'
              : `Processing ${progress.total} products with comprehensive data enrichment`
            }
          </p>
        </div>
        <Badge 
          variant={progress.status === 'error' ? 'destructive' : progress.status === 'completed' ? 'default' : 'secondary'}
          className="text-xs"
        >
          {progress.status === 'processing' ? 'In Progress' : 
           progress.status === 'completed' ? 'Completed' :
           progress.status === 'error' ? 'Error' : 'Pending'}
        </Badge>
      </div>

      {/* Steps */}
      <div className="space-y-4">
        {ENRICHMENT_STEPS.map((step, index) => {
          const status = getStepStatus(step.id);
          const isActive = step.id === progress.step;
          
          return (
            <div key={step.id} className="flex items-start space-x-4">
              {/* Step Icon */}
              <div className="flex-shrink-0 mt-1">
                {getStepIcon(step, status)}
              </div>
              
              {/* Step Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className={`text-sm font-medium ${
                      status === 'completed' ? 'text-green-900' :
                      status === 'processing' ? 'text-blue-900' :
                      status === 'error' ? 'text-red-900' :
                      'text-gray-500'
                    }`}>
                      {step.name}
                      {isActive && progress.currentItem && (
                        <span className="ml-2 text-xs text-gray-600">
                          • {progress.currentItem}
                        </span>
                      )}
                    </h4>
                    <p className="text-xs text-gray-600 mt-1">{step.description}</p>
                  </div>
                  
                  {/* Step Progress */}
                  {isActive && progress.status === 'processing' && (
                    <div className="text-right">
                      <div className="text-xs text-gray-600 mb-1">
                        {progress.completed} / {progress.total}
                      </div>
                      <div className="text-xs font-medium text-blue-600">
                        {getCurrentStepProgress()}%
                      </div>
                    </div>
                  )}
                  
                  {status === 'completed' && (
                    <div className="text-xs text-green-600 font-medium">
                      ✓ Complete
                    </div>
                  )}
                </div>
                
                {/* Progress Bar for Active Step */}
                {isActive && progress.status === 'processing' && (
                  <div className="mt-2">
                    <Progress 
                      value={getCurrentStepProgress()} 
                      className="h-2"
                    />
                  </div>
                )}
                
                {/* Error Messages */}
                {status === 'error' && progress.errors.length > 0 && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                    {progress.errors.slice(0, 3).map((error, idx) => (
                      <div key={idx}>• {error}</div>
                    ))}
                    {progress.errors.length > 3 && (
                      <div>• ... and {progress.errors.length - 3} more errors</div>
                    )}
                  </div>
                )}
              </div>
              
              {/* Connector Line */}
              {index < ENRICHMENT_STEPS.length - 1 && (
                <div className="absolute left-[11px] mt-8 w-0.5 h-8 bg-gray-200" 
                     style={{ marginLeft: '11px' }} />
              )}
            </div>
          );
        })}
      </div>

      {/* Overall Progress Summary */}
      {progress.step !== 'complete' && progress.total > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Overall Progress</span>
            <span className="font-medium text-gray-900">
              {progress.completed} / {progress.total} products processed
            </span>
          </div>
          <Progress 
            value={getProgressPercentage()} 
            className="mt-2 h-2"
          />
        </div>
      )}

      {/* Completion Summary */}
      {progress.step === 'complete' && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-green-50 rounded-lg p-3">
              <div className="text-lg font-bold text-green-600">
                {progress.completed}
              </div>
              <div className="text-xs text-green-700">Products Enriched</div>
            </div>
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="text-lg font-bold text-blue-600">
                100%
              </div>
              <div className="text-xs text-blue-700">Data Coverage</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-3">
              <div className="text-lg font-bold text-purple-600">
                Ready
              </div>
              <div className="text-xs text-purple-700">For Import</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

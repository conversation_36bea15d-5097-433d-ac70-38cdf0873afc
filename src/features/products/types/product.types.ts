/**
 * Product Data Grid - Type Definitions
 *
 * This file contains all type definitions related to products and the data grid.
 */

/**
 * Base Product interface with common properties across all categories
 */
export interface BaseProduct {
  id: string;
  name: string;
  sku: string;
  partArticleNumber?: string;
  category: string;
  subcategory?: string;
  description?: string; // For new table configs that expect 'description'
  descriptionAndSpecifications: string; // For legacy table implementations
  primaryImage?: string;
  additionalImages?: string[];
  manufacturer: string;
  supplierName?: string;
  supplierAccountId?: string; // CRITICAL: Supplier account ID for shipping companies
  stockQuantity: number;
  wholesalePricingTiers?: PricingTier[];
  minimumOrderQuantity?: number;
  quotationRequestEnabled?: boolean;
  retailPrice?: number;
  certifications?: string[];
  productionCapacity?: number;
  shippingOrigin?: string;
  estimatedLeadTime?: string;
  availableShippingMethods?: string[];
  packagingDetails?: string;
  vehicleCompatibility?: string; // Simple text input for vehicle compatibility
  inventoryUpdateDate: Date;
  status: ProductStatus;
  adminNotes?: string;
  createdAt: Date;
  updatedAt: Date;
  // Marketplace section this product belongs to
  marketplaceSection?: 'wholesale' | 'retail'; // 'wholesale' for suppliers, 'retail' for merchants
}

/**
 * Vehicle compatibility interface
 */
export interface VehicleCompatibility {
  id: string;
  type: string;
  brand: string;
  model: string;
  generation?: string;
  engineType?: string;
  engineDetails?: {
    code?: string;
    fuel_type?: string;
    power_kW?: number;
    power_HP?: number;
    fullName?: string;
  };
  displayName: string;
}

/**
 * Tyre specific product properties
 */
export interface TyreProduct extends BaseProduct {
  width?: number; // in mm
  aspectRatio?: number; // in percentage
  rimDiameter?: number; // in inches
  loadIndex?: number;
  speedRating?: string;
  season?: 'Summer' | 'Winter' | 'All-Season';
  vehicleCompatibility?: string; // Simple text input for vehicle compatibility
  treadLife?: string;
  tractionRating?: string;
  temperatureRating?: string;
}

/**
 * Brake Parts & Systems product properties
 * Uses the structured Description field for technical specifications
 */
export interface BrakeProduct extends BaseProduct {
  // No additional specific fields as we're using the structured Description field
  // for all technical specifications
  vehicleCompatibility?: string; // Simple text input for vehicle compatibility
}

/**
 * Product status options
 */
export type ProductStatus =
  | 'active'
  | 'draft'
  | 'pending_approval'
  | 'out_of_stock'
  | 'discontinued';

/**
 * Pricing tier structure for wholesale pricing
 */
export interface PricingTier {
  minQuantity: number;
  maxQuantity?: number;
  price: number;
}

/**
 * Column definition with metadata for rendering
 */
export interface ProductColumnDef {
  id: string;
  header: string;
  accessorKey?: string;
  cell?: string;
  enableSorting?: boolean;
  enableFiltering?: boolean;
  size?: number;
  minSize?: number;
  maxSize?: number;
  cellType?: 'text' | 'number' | 'image' | 'multiImage' | 'status' | 'date' | 'pricingTiers' | 'specifications' | 'vehicleCompatibility';
  filterType?: 'text' | 'number' | 'select' | 'date' | 'boolean';
  editable?: boolean;
  required?: boolean;
  options?: { label: string; value: string }[];
  // Role-based permissions
  allowedRoles?: ('supplier' | 'merchant' | 'consumer' | 'distribution')[];
}

/**
 * Category table configuration
 */
export interface CategoryTableConfig {
  id: string;
  name: string;
  description?: string;
  columns: ProductColumnDef[];
  defaultSortColumn?: string;
  defaultSortDirection?: 'asc' | 'desc';
  defaultFilters?: Record<string, any>;
}

/**
 * Table state for persistence
 */
export interface ProductTableState {
  categoryId: string;
  pagination: {
    pageIndex: number;
    pageSize: number;
  };
  sorting: {
    id: string;
    desc: boolean;
  }[];
  columnVisibility: Record<string, boolean>;
  filters: Record<string, any>;
}

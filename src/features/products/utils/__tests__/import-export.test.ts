/**
 * Tests for Enhanced Import/Export Functionality
 */

import { 
  analyzeImportedData, 
  mapImportedDataToProducts, 
  validateImportedProducts,
  extractPartArticleNumber,
  detectColumnMapping,
  findDuplicateArticleNumbers
} from '../import-export';

describe('Enhanced Import/Export Functionality', () => {
  
  describe('analyzeImportedData', () => {
    it('should analyze imported data correctly', () => {
      const testData = [
        { partArticleNumber: 'ABC123', name: 'Test Product 1', manufacturer: 'Test Brand' },
        { partArticleNumber: 'DEF456', name: 'Test Product 2', manufacturer: 'Test Brand' },
        { sku: 'GHI789', name: 'Test Product 3' }, // No manufacturer
      ];

      const analysis = analyzeImportedData(testData);

      expect(analysis.totalRows).toBe(3);
      expect(analysis.detectedArticleNumbers).toContain('ABC123');
      expect(analysis.detectedArticleNumbers).toContain('DEF456');
      expect(analysis.detectedArticleNumbers).toContain('GHI789');
      expect(analysis.dataQuality.completeRows).toBe(2);
      expect(analysis.dataQuality.incompleteRows).toBe(1);
    });
  });

  describe('extractPartArticleNumber', () => {
    it('should extract part article number from various field names', () => {
      const testCases = [
        { partArticleNumber: 'ABC123' },
        { part_article_number: 'DEF456' },
        { articleNumber: 'GHI789' },
        { sku: 'JKL012' },
        { productCode: 'MNO345' },
      ];

      testCases.forEach((testCase, index) => {
        const result = extractPartArticleNumber(testCase);
        expect(result).toBeDefined();
        expect(result).toMatch(/^[A-Z]{3}\d{3}$/);
      });
    });

    it('should return undefined for invalid article numbers', () => {
      const testCases = [
        { partArticleNumber: '' },
        { partArticleNumber: 'A' }, // Too short
        { partArticleNumber: 'A'.repeat(60) }, // Too long
        { partArticleNumber: 'ABC@123' }, // Invalid characters
        {},
      ];

      testCases.forEach((testCase) => {
        const result = extractPartArticleNumber(testCase);
        expect(result).toBeUndefined();
      });
    });
  });

  describe('detectColumnMapping', () => {
    it('should detect column mappings correctly', () => {
      const testData = [
        {
          'Product Name': 'Test Product',
          'Part Number': 'ABC123',
          'Brand': 'Test Brand',
          'Price': '100.00',
          'Stock': '50'
        }
      ];

      const mapping = detectColumnMapping(testData);

      expect(mapping['Product Name']).toBe('name');
      expect(mapping['Part Number']).toBe('partArticleNumber');
      expect(mapping['Brand']).toBe('manufacturer');
      expect(mapping['Price']).toBe('retailPrice');
      expect(mapping['Stock']).toBe('stockQuantity');
    });
  });

  describe('findDuplicateArticleNumbers', () => {
    it('should find duplicate article numbers', () => {
      const testData = [
        { partArticleNumber: 'ABC123', name: 'Product 1' },
        { partArticleNumber: 'DEF456', name: 'Product 2' },
        { partArticleNumber: 'ABC123', name: 'Product 3' }, // Duplicate
        { partArticleNumber: 'GHI789', name: 'Product 4' },
      ];

      const duplicates = findDuplicateArticleNumbers(testData);

      expect(duplicates).toContain('ABC123');
      expect(duplicates).not.toContain('DEF456');
      expect(duplicates).not.toContain('GHI789');
      expect(duplicates.length).toBe(1);
    });
  });

  describe('mapImportedDataToProducts', () => {
    it('should map imported data to products correctly', () => {
      const testData = [
        {
          'Product Name': 'Test Tyre',
          'Part Number': 'TYR123',
          'Brand': 'Michelin',
          'Price': '150.00',
          'Stock': '25',
          'Width': '225',
          'Aspect Ratio': '45',
          'Rim Diameter': '17'
        }
      ];

      const products = mapImportedDataToProducts(testData, 'tyres');

      expect(products).toHaveLength(1);
      expect(products[0].name).toBe('Test Tyre');
      expect(products[0].partArticleNumber).toBe('TYR123');
      expect(products[0].manufacturer).toBe('Michelin');
      expect(products[0].retailPrice).toBe(150);
      expect(products[0].stockQuantity).toBe(25);
      expect(products[0].category).toBe('tyres');
      expect(products[0].hasPartArticleNumber).toBe(true);
    });

    it('should handle missing data gracefully', () => {
      const testData = [
        {
          'Product Name': 'Incomplete Product',
          // Missing part number, price, etc.
        }
      ];

      const products = mapImportedDataToProducts(testData, 'brakes');

      expect(products).toHaveLength(1);
      expect(products[0].name).toBe('Incomplete Product');
      expect(products[0].partArticleNumber).toBeUndefined();
      expect(products[0].retailPrice).toBe(0);
      expect(products[0].stockQuantity).toBe(0);
      expect(products[0].hasPartArticleNumber).toBe(false);
      expect(products[0].needsDataFetch).toBe(true);
    });
  });

  describe('validateImportedProducts', () => {
    it('should validate products correctly', () => {
      const testProducts = [
        {
          name: 'Valid Product',
          manufacturer: 'Test Brand',
          stockQuantity: 10,
          partArticleNumber: 'ABC123'
        },
        {
          name: '', // Invalid: empty name
          manufacturer: 'Test Brand',
          stockQuantity: 5
        },
        {
          name: 'Another Product',
          manufacturer: '', // Invalid: empty manufacturer
          stockQuantity: -1 // Invalid: negative stock
        }
      ];

      const validation = validateImportedProducts(testProducts);

      expect(validation.valid).toHaveLength(1);
      expect(validation.invalid).toHaveLength(2);
      expect(validation.needsDataFetch).toHaveLength(0); // Valid product has name already

      // Check error messages
      expect(validation.invalid[0].errors).toContain('Product name is required');
      expect(validation.invalid[1].errors).toContain('Manufacturer is required');
      expect(validation.invalid[1].errors).toContain('Valid stock quantity is required');
    });

    it('should identify products that need data fetching', () => {
      const testProducts = [
        {
          name: '', // Empty name but has article number
          manufacturer: 'Test Brand',
          stockQuantity: 10,
          partArticleNumber: 'ABC123'
        }
      ];

      const validation = validateImportedProducts(testProducts);

      expect(validation.valid).toHaveLength(0);
      expect(validation.invalid).toHaveLength(1);
      // Product is invalid due to missing name, but would need fetch if name was present
    });
  });
});

// Mock data for testing
export const mockImportData = {
  validTyreData: [
    {
      'Product Name': 'Michelin Pilot Sport 4',
      'Part Number': 'MIC225451',
      'Brand': 'Michelin',
      'Price': '180.00',
      'Stock': '15',
      'Width': '225',
      'Aspect Ratio': '45',
      'Rim Diameter': '17',
      'Speed Rating': 'Y',
      'Season': 'Summer'
    }
  ],
  
  validBrakeData: [
    {
      'Product Name': 'Brembo Brake Disc',
      'Part Number': 'BRE123456',
      'Brand': 'Brembo',
      'Price': '120.00',
      'Stock': '8',
      'Description': 'High-performance brake disc for sports cars'
    }
  ],

  invalidData: [
    {
      'Part Number': 'INVALID',
      // Missing required fields
    }
  ],

  duplicateData: [
    {
      'Product Name': 'Product A',
      'Part Number': 'DUP123',
      'Brand': 'Brand A'
    },
    {
      'Product Name': 'Product B',
      'Part Number': 'DUP123', // Duplicate
      'Brand': 'Brand B'
    }
  ]
};

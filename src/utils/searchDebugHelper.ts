/**
 * Search Debug Helper
 * 
 * Quick debugging utilities to test search functionality
 * Use in browser console to debug search issues
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabase = createClient(supabaseUrl, supabaseKey);

export class SearchDebugHelper {
  /**
   * Check if products table has data
   */
  static async checkProductsTable(): Promise<void> {
    console.log('🔍 Checking products table...');
    
    try {
      // Get total count
      const { count: totalCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true });
      
      console.log(`📊 Total products: ${totalCount}`);
      
      // Get active products count
      const { count: activeCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');
      
      console.log(`✅ Active products: ${activeCount}`);
      
      // Get out of stock products count
      const { count: outOfStockCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'out_of_stock');
      
      console.log(`📦 Out of stock products: ${outOfStockCount}`);
      
      // Get sample products
      const { data: sampleProducts } = await supabase
        .from('products')
        .select('id, name, manufacturer, category, status, retail_price')
        .in('status', ['active', 'out_of_stock'])
        .limit(5);
      
      console.log('📋 Sample products:', sampleProducts);
      
      // Check for products that match common search terms
      const { data: brakeProducts } = await supabase
        .from('products')
        .select('id, name, manufacturer')
        .or('name.ilike.%brake%,description_and_specifications.ilike.%brake%')
        .in('status', ['active', 'out_of_stock'])
        .limit(3);
      
      console.log('🔧 Brake products found:', brakeProducts);
      
    } catch (error) {
      console.error('❌ Error checking products table:', error);
    }
  }

  /**
   * Test basic search query
   */
  static async testBasicSearch(query: string = 'brake'): Promise<void> {
    console.log(`🔍 Testing basic search for: "${query}"`);
    
    try {
      const { data: products, count } = await supabase
        .from('products')
        .select(`
          id, name, description_and_specifications, manufacturer, category, subcategory,
          part_article_number, retail_price, stock_quantity, status, primary_image
        `, { count: 'exact' })
        .in('status', ['active', 'out_of_stock'])
        .or(`name.ilike.%${query}%,description_and_specifications.ilike.%${query}%,manufacturer.ilike.%${query}%`)
        .limit(10);
      
      console.log(`📊 Search results for "${query}":`, {
        totalCount: count,
        productsReturned: products?.length || 0,
        products: products
      });
      
    } catch (error) {
      console.error(`❌ Error searching for "${query}":`, error);
    }
  }

  /**
   * Test search analytics table
   */
  static async testSearchAnalytics(): Promise<void> {
    console.log('🔍 Testing search analytics table...');
    
    try {
      // Check if table exists and has data
      const { data: analytics, count } = await supabase
        .from('search_analytics')
        .select('*', { count: 'exact' })
        .limit(5);
      
      console.log('📊 Search analytics:', {
        totalRecords: count,
        sampleRecords: analytics
      });
      
      // Try to insert a test record
      const { data: insertResult, error: insertError } = await supabase
        .from('search_analytics')
        .insert({
          session_id: 'debug-session-' + Date.now(),
          query: 'debug test',
          search_type: 'text',
          results_count: 0,
          search_time_ms: 100,
          language: 'en'
        })
        .select();
      
      if (insertError) {
        console.error('❌ Error inserting analytics:', insertError);
      } else {
        console.log('✅ Analytics insert successful:', insertResult);
      }
      
    } catch (error) {
      console.error('❌ Error testing search analytics:', error);
    }
  }

  /**
   * Test intelligent search service
   */
  static async testIntelligentSearch(): Promise<void> {
    console.log('🔍 Testing intelligent search service...');
    
    try {
      // Import the search service dynamically
      const { intelligentSearchService } = await import('@/services/intelligentSearchService');
      
      // Test basic search
      const result = await intelligentSearchService.search('brake');
      
      console.log('🎯 Intelligent search result:', {
        totalCount: result.totalCount,
        productsReturned: result.products.length,
        searchTime: result.searchTime,
        suggestions: result.suggestions,
        sampleProducts: result.products.slice(0, 3)
      });
      
      // Test suggestions
      const suggestions = await intelligentSearchService.getSuggestions('br');
      console.log('💡 Search suggestions for "br":', suggestions);
      
    } catch (error) {
      console.error('❌ Error testing intelligent search:', error);
    }
  }

  /**
   * Run comprehensive debug check
   */
  static async runFullDebug(): Promise<void> {
    console.log('🚀 Starting comprehensive search debug...\n');
    
    await this.checkProductsTable();
    console.log('\n');
    
    await this.testBasicSearch('brake');
    console.log('\n');
    
    await this.testBasicSearch('hankook');
    console.log('\n');
    
    await this.testSearchAnalytics();
    console.log('\n');
    
    await this.testIntelligentSearch();
    console.log('\n');
    
    console.log('🎉 Debug check completed!');
  }

  /**
   * Quick health check
   */
  static async quickCheck(): Promise<boolean> {
    try {
      const { count } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .in('status', ['active', 'out_of_stock']);
      
      console.log(`✅ Quick check: ${count} searchable products found`);
      return (count || 0) > 0;
    } catch (error) {
      console.error('❌ Quick check failed:', error);
      return false;
    }
  }
}

// Make it available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).SearchDebugHelper = SearchDebugHelper;
}

export default SearchDebugHelper;

/**
 * Search Test Helper
 * 
 * Utility functions to test the intelligent search functionality
 * Run these tests in browser console to verify everything works
 */

import { intelligentSearchService } from '@/services/intelligentSearchService';
import { searchAnalyticsService } from '@/services/searchAnalyticsService';

export class SearchTestHelper {
  /**
   * Test all 8 search patterns from UI mockup
   */
  static async testSearchPatterns(): Promise<void> {
    console.log('🧪 Testing Search Patterns...');
    
    const testQueries = [
      { query: 'Wiper blades', expectedPattern: 'car_part' },
      { query: 'Wiper blades CHAMPION', expectedPattern: 'car_part_manufacturer' },
      { query: 'Wiper blades LEXUS', expectedPattern: 'car_part_vehicle_brand' },
      { query: 'Wiper blades AHR55/B01', expectedPattern: 'car_part_item_number' },
      { query: '39-8550', expectedPattern: 'item_number' },
      { query: '39-8650 MAXGEAR', expectedPattern: 'item_number_manufacturer' },
      { query: '8521271010', expectedPattern: 'oem_number' },
      { query: '8522248160 MAXGEAR', expectedPattern: 'oem_manufacturer' }
    ];

    for (const test of testQueries) {
      try {
        const startTime = Date.now();
        const result = await intelligentSearchService.search(test.query);
        const endTime = Date.now();
        
        console.log(`✅ "${test.query}":`, {
          pattern: test.expectedPattern,
          results: result.totalCount,
          time: `${endTime - startTime}ms`,
          searchTime: `${result.searchTime}ms`
        });
      } catch (error) {
        console.error(`❌ "${test.query}":`, error);
      }
    }
  }

  /**
   * Test search suggestions
   */
  static async testSuggestions(): Promise<void> {
    console.log('🧪 Testing Search Suggestions...');
    
    const testPrefixes = ['br', 'oil', 'fil', 'tyr'];
    
    for (const prefix of testPrefixes) {
      try {
        const startTime = Date.now();
        const suggestions = await intelligentSearchService.getSuggestions(prefix);
        const endTime = Date.now();
        
        console.log(`✅ Suggestions for "${prefix}":`, {
          count: suggestions.length,
          time: `${endTime - startTime}ms`,
          suggestions: suggestions.slice(0, 3).map(s => s.text)
        });
      } catch (error) {
        console.error(`❌ Suggestions for "${prefix}":`, error);
      }
    }
  }

  /**
   * Test multilingual search
   */
  static async testMultilingual(): Promise<void> {
    console.log('🧪 Testing Multilingual Search...');
    
    const testQueries = [
      { query: 'plaquettes de frein', language: 'French' },
      { query: 'filtres à huile', language: 'French' },
      { query: 'فرامل', language: 'Arabic' },
      { query: 'إطارات', language: 'Arabic' }
    ];

    for (const test of testQueries) {
      try {
        const startTime = Date.now();
        const result = await intelligentSearchService.search(test.query);
        const endTime = Date.now();
        
        console.log(`✅ ${test.language} "${test.query}":`, {
          results: result.totalCount,
          time: `${endTime - startTime}ms`
        });
      } catch (error) {
        console.error(`❌ ${test.language} "${test.query}":`, error);
      }
    }
  }

  /**
   * Test vehicle requirement detection
   */
  static testVehicleRequirement(): void {
    console.log('🧪 Testing Vehicle Requirement Detection...');
    
    const testQueries = [
      { query: 'pneus', shouldRequire: true },
      { query: 'tyres', shouldRequire: true },
      { query: '205/55R16', shouldRequire: true },
      { query: 'brake pads', shouldRequire: false },
      { query: 'oil filter', shouldRequire: false },
      { query: 'spark plugs', shouldRequire: false }
    ];

    testQueries.forEach(test => {
      const requires = intelligentSearchService.requiresVehicleInfo(test.query);
      const status = requires === test.shouldRequire ? '✅' : '❌';
      
      console.log(`${status} "${test.query}": requires vehicle = ${requires} (expected: ${test.shouldRequire})`);
    });
  }

  /**
   * Test analytics tracking
   */
  static async testAnalytics(): Promise<void> {
    console.log('🧪 Testing Analytics Tracking...');
    
    try {
      // Track a test search
      await searchAnalyticsService.trackSearch({
        query: 'test search',
        search_type: 'text',
        results_count: 5,
        search_time_ms: 150,
        filters_applied: { category: 'brakes' },
        language: 'en'
      });
      
      console.log('✅ Analytics tracking successful');
      
      // Check queue size
      const queueSize = searchAnalyticsService.getQueueSize();
      console.log(`✅ Analytics queue size: ${queueSize}`);
      
    } catch (error) {
      console.error('❌ Analytics tracking failed:', error);
    }
  }

  /**
   * Test performance
   */
  static async testPerformance(): Promise<void> {
    console.log('🧪 Testing Performance...');
    
    const testQuery = 'brake pads';
    const iterations = 5;
    const times: number[] = [];
    
    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = Date.now();
        await intelligentSearchService.search(testQuery);
        const endTime = Date.now();
        times.push(endTime - startTime);
      } catch (error) {
        console.error(`❌ Performance test iteration ${i + 1}:`, error);
      }
    }
    
    if (times.length > 0) {
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      
      console.log('✅ Performance Results:', {
        average: `${avgTime.toFixed(1)}ms`,
        min: `${minTime}ms`,
        max: `${maxTime}ms`,
        target: '<300ms',
        status: avgTime < 300 ? '✅ PASS' : '❌ FAIL'
      });
    }
  }

  /**
   * Run all tests
   */
  static async runAllTests(): Promise<void> {
    console.log('🚀 Starting Comprehensive Search Tests...\n');
    
    try {
      await this.testSearchPatterns();
      console.log('\n');
      
      await this.testSuggestions();
      console.log('\n');
      
      await this.testMultilingual();
      console.log('\n');
      
      this.testVehicleRequirement();
      console.log('\n');
      
      await this.testAnalytics();
      console.log('\n');
      
      await this.testPerformance();
      console.log('\n');
      
      console.log('🎉 All tests completed!');
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  /**
   * Quick health check
   */
  static async healthCheck(): Promise<boolean> {
    try {
      // Test basic search
      const result = await intelligentSearchService.search('test');
      
      // Test suggestions
      const suggestions = await intelligentSearchService.getSuggestions('br');
      
      // Test analytics
      const analytics = intelligentSearchService.getSearchAnalytics();
      
      console.log('✅ Health Check PASSED:', {
        searchWorking: true,
        suggestionsWorking: suggestions.length >= 0,
        analyticsWorking: typeof analytics.cacheSize === 'number'
      });
      
      return true;
    } catch (error) {
      console.error('❌ Health Check FAILED:', error);
      return false;
    }
  }
}

// Make it available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).SearchTestHelper = SearchTestHelper;
}

export default SearchTestHelper;

/**
 * Perplexica Web Search Integration Service
 * Based on: https://github.com/ItzCrazyKns/Perplexica
 */

interface PerplexicaConfig {
  baseUrl: string;
  apiKey?: string;
  timeout: number;
}

interface PerplexicaSearchRequest {
  query: string;
  focus_mode?: 'web' | 'academic' | 'writing' | 'wolfram' | 'youtube' | 'reddit';
  optimization_mode?: 'speed' | 'balanced' | 'quality';
  chat_model?: string;
  embedding_model?: string;
}

interface PerplexicaSearchResult {
  success: boolean;
  data?: {
    answer: string;
    sources: Array<{
      title: string;
      url: string;
      snippet: string;
    }>;
    images?: string[];
    relatedQueries?: string[];
  };
  error?: string;
}

interface ProductSearchData {
  specifications?: string;
  compatibility?: string;
  images?: string[];
  additionalInfo?: string;
  sources?: string[];
}

class PerplexicaService {
  private config: PerplexicaConfig;

  constructor(config?: Partial<PerplexicaConfig>) {
    this.config = {
      baseUrl: config?.baseUrl || process.env.PERPLEXICA_BASE_URL || 'http://localhost:3001',
      apiKey: config?.apiKey || process.env.PERPLEXICA_API_KEY,
      timeout: config?.timeout || 30000
    };

    console.log('🔍 Perplexica Service initialized:', {
      baseUrl: this.config.baseUrl,
      hasApiKey: !!this.config.apiKey,
      timeout: this.config.timeout
    });
  }

  /**
   * Search for automotive product information using Perplexica
   */
  async searchProductData(
    partNumber: string,
    manufacturer?: string,
    category?: string
  ): Promise<ProductSearchData> {
    try {
      console.log(`🔍 Perplexica: Searching for product data - ${partNumber}`);

      // Build comprehensive search query
      const searchQuery = this.buildProductSearchQuery(partNumber, manufacturer, category);
      console.log(`🔍 Perplexica: Search query: "${searchQuery}"`);

      // Perform the search
      const searchResult = await this.performSearch({
        query: searchQuery,
        focus_mode: 'web',
        optimization_mode: 'balanced'
      });

      if (!searchResult.success || !searchResult.data) {
        console.warn(`📭 Perplexica: No data found for ${partNumber}`);
        return {};
      }

      // Extract and structure product data
      const productData = this.extractProductData(searchResult.data, partNumber);
      console.log(`✅ Perplexica: Extracted data for ${partNumber}:`, {
        hasSpecs: !!productData.specifications,
        hasCompatibility: !!productData.compatibility,
        imageCount: productData.images?.length || 0,
        sourceCount: productData.sources?.length || 0
      });

      return productData;

    } catch (error) {
      console.error(`❌ Perplexica: Search failed for ${partNumber}:`, error);
      return {};
    }
  }

  /**
   * Build optimized search query for automotive parts
   */
  private buildProductSearchQuery(
    partNumber: string,
    manufacturer?: string,
    category?: string
  ): string {
    const queryParts = [];

    // Core part information
    queryParts.push(`"${partNumber}"`);
    
    if (manufacturer) {
      queryParts.push(manufacturer);
    }
    
    if (category) {
      queryParts.push(category);
    }

    // Add automotive-specific terms
    queryParts.push('automotive parts specifications compatibility');

    return queryParts.join(' ');
  }

  /**
   * Perform search using Perplexica API
   */
  private async performSearch(request: PerplexicaSearchRequest): Promise<PerplexicaSearchResult> {
    try {
      console.log(`📡 Perplexica: Making API request to ${this.config.baseUrl}/api/search`);

      const response = await fetch(`${this.config.baseUrl}/api/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
        },
        body: JSON.stringify(request),
        signal: AbortSignal.timeout(this.config.timeout)
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        console.error(`❌ Perplexica API Error ${response.status}: ${errorText}`);
        
        return {
          success: false,
          error: `Perplexica API error: ${response.status} - ${response.statusText}`
        };
      }

      const data = await response.json();
      console.log(`✅ Perplexica: Search completed successfully`);

      return {
        success: true,
        data: {
          answer: data.answer || '',
          sources: data.sources || [],
          images: data.images || [],
          relatedQueries: data.relatedQueries || []
        }
      };

    } catch (error) {
      console.error('❌ Perplexica: API request failed:', error);
      
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          success: false,
          error: 'Perplexica search timeout'
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Perplexica search failed'
      };
    }
  }

  /**
   * Extract structured product data from Perplexica search results
   */
  private extractProductData(searchData: any, partNumber: string): ProductSearchData {
    const result: ProductSearchData = {
      sources: []
    };

    // Extract specifications from answer
    if (searchData.answer) {
      const specs = this.extractSpecifications(searchData.answer);
      if (specs) {
        result.specifications = specs;
      }

      const compatibility = this.extractCompatibility(searchData.answer);
      if (compatibility) {
        result.compatibility = compatibility;
      }

      const additionalInfo = this.extractAdditionalInfo(searchData.answer, partNumber);
      if (additionalInfo) {
        result.additionalInfo = additionalInfo;
      }
    }

    // Extract images
    if (searchData.images && searchData.images.length > 0) {
      result.images = searchData.images.slice(0, 5); // Limit to 5 images
    }

    // Extract source URLs
    if (searchData.sources && searchData.sources.length > 0) {
      result.sources = searchData.sources.map((source: any) => source.url).slice(0, 3);
    }

    return result;
  }

  /**
   * Extract specifications from search answer
   */
  private extractSpecifications(answer: string): string | undefined {
    const specPatterns = [
      /specifications?[:\s]+(.*?)(?:\n\n|\.|$)/i,
      /technical\s+data[:\s]+(.*?)(?:\n\n|\.|$)/i,
      /features?[:\s]+(.*?)(?:\n\n|\.|$)/i
    ];

    for (const pattern of specPatterns) {
      const match = answer.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return undefined;
  }

  /**
   * Extract vehicle compatibility from search answer
   */
  private extractCompatibility(answer: string): string | undefined {
    const compatibilityPatterns = [
      /compatibility[:\s]+(.*?)(?:\n\n|\.|$)/i,
      /fits?[:\s]+(.*?)(?:\n\n|\.|$)/i,
      /suitable\s+for[:\s]+(.*?)(?:\n\n|\.|$)/i,
      /vehicle\s+applications?[:\s]+(.*?)(?:\n\n|\.|$)/i
    ];

    for (const pattern of compatibilityPatterns) {
      const match = answer.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return undefined;
  }

  /**
   * Extract additional product information
   */
  private extractAdditionalInfo(answer: string, partNumber: string): string | undefined {
    // Remove the part number and common terms to get additional info
    const cleanAnswer = answer
      .replace(new RegExp(partNumber, 'gi'), '')
      .replace(/specifications?|compatibility|features?/gi, '')
      .trim();

    if (cleanAnswer.length > 50) {
      return cleanAnswer.substring(0, 500); // Limit length
    }

    return undefined;
  }

  /**
   * Check if Perplexica service is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });
      
      const isAvailable = response.ok;
      console.log(`🔍 Perplexica: Service availability check - ${isAvailable ? 'Available' : 'Unavailable'}`);
      return isAvailable;
    } catch (error) {
      console.warn('🔍 Perplexica: Service unavailable:', error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }
}

// Export singleton instance
export const perplexicaService = new PerplexicaService();

// Export types
export type { PerplexicaSearchResult, ProductSearchData };

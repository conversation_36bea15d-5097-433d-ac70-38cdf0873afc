/**
 * Enhanced Product Import Service with AI-Powered Web Search
 * 3-Step Flow: File Upload & Data Extraction → AI Web Search Enhancement → User Review & Completion
 */

import <PERSON> from 'papaparse';
import * as XLSX from 'xlsx';
import { aiWebSearchService, type ProductEnhancementRequest, type EnhancedProductData } from './aiWebSearchService';

export interface ImportStep {
  id: string;
  name: string;
  status: 'pending' | 'in-progress' | 'completed' | 'error';
  progress: number;
  message?: string;
}

export interface ExtractedProductData {
  originalRow: Record<string, any>;
  extractedFields: {
    partNumber?: string;
    productName?: string;
    manufacturer?: string;
    category?: string;
    stockQuantity?: number;
    retailPrice?: number;
  };
  rowIndex: number;
}

export interface ImportSession {
  id: string;
  fileName: string;
  totalRows: number;
  steps: ImportStep[];
  extractedData: ExtractedProductData[];
  enhancedData: EnhancedProductData[];
  createdAt: Date;
  status: 'extracting' | 'enhancing' | 'reviewing' | 'completed' | 'error';
}

export interface ImportProgress {
  sessionId: string;
  currentStep: string;
  progress: number;
  message: string;
  estimatedTimeRemaining?: number;
}

class EnhancedImportService {
  private activeSessions = new Map<string, ImportSession>();
  private progressCallbacks = new Map<string, (progress: ImportProgress) => void>();

  /**
   * Step 1: Upload file and extract basic product data
   */
  async startImportSession(
    file: File,
    onProgress?: (progress: ImportProgress) => void
  ): Promise<string> {
    const sessionId = `import_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    if (onProgress) {
      this.progressCallbacks.set(sessionId, onProgress);
    }

    const session: ImportSession = {
      id: sessionId,
      fileName: file.name,
      totalRows: 0,
      steps: [
        { id: 'extract', name: 'File Upload & Data Extraction', status: 'in-progress', progress: 0 },
        { id: 'enhance', name: 'AI Web Search Enhancement', status: 'pending', progress: 0 },
        { id: 'review', name: 'User Review & Completion', status: 'pending', progress: 0 }
      ],
      extractedData: [],
      enhancedData: [],
      createdAt: new Date(),
      status: 'extracting'
    };

    this.activeSessions.set(sessionId, session);
    this.updateProgress(sessionId, 'extract', 10, 'Reading file...');

    try {
      // Extract data from file
      const extractedData = await this.extractDataFromFile(file, sessionId);
      
      session.extractedData = extractedData;
      session.totalRows = extractedData.length;
      session.steps[0].status = 'completed';
      session.steps[0].progress = 100;
      session.status = 'enhancing';

      this.updateProgress(sessionId, 'extract', 100, `Extracted ${extractedData.length} products`);
      
      return sessionId;
    } catch (error) {
      session.steps[0].status = 'error';
      session.steps[0].message = error instanceof Error ? error.message : 'Unknown error';
      session.status = 'error';
      
      this.updateProgress(sessionId, 'extract', 0, 'Extraction failed');
      throw error;
    }
  }

  /**
   * Step 2: Enhance products using AI web search
   */
  async enhanceProducts(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Import session not found');
    }

    session.steps[1].status = 'in-progress';
    session.steps[1].progress = 0;
    this.updateProgress(sessionId, 'enhance', 0, 'Starting AI enhancement...');

    try {
      // Convert extracted data to enhancement requests
      const requests: ProductEnhancementRequest[] = session.extractedData.map(item => ({
        partNumber: item.extractedFields.partNumber,
        productName: item.extractedFields.productName,
        manufacturer: item.extractedFields.manufacturer,
        category: item.extractedFields.category,
        existingData: item.originalRow
      }));

      // Enhance products with AI web search
      const enhancedData = await aiWebSearchService.enhanceMultipleProducts(
        requests,
        (completed, total) => {
          const progress = Math.round((completed / total) * 100);
          session.steps[1].progress = progress;
          this.updateProgress(
            sessionId, 
            'enhance', 
            progress, 
            `Enhanced ${completed}/${total} products`
          );
        }
      );

      session.enhancedData = enhancedData;
      session.steps[1].status = 'completed';
      session.steps[1].progress = 100;
      session.steps[2].status = 'in-progress';
      session.status = 'reviewing';

      this.updateProgress(sessionId, 'review', 0, 'Ready for review');

    } catch (error) {
      session.steps[1].status = 'error';
      session.steps[1].message = error instanceof Error ? error.message : 'Enhancement failed';
      session.status = 'error';
      
      this.updateProgress(sessionId, 'enhance', 0, 'Enhancement failed');
      throw error;
    }
  }

  /**
   * Step 3: Get enhanced data for user review
   */
  getEnhancedDataForReview(sessionId: string): {
    session: ImportSession;
    reviewData: Array<{
      original: ExtractedProductData;
      enhanced: EnhancedProductData;
      status: 'pending' | 'approved' | 'rejected';
    }>;
  } {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Import session not found');
    }

    const reviewData = session.extractedData.map((original, index) => ({
      original,
      enhanced: session.enhancedData[index],
      status: 'pending' as const
    }));

    return { session, reviewData };
  }

  /**
   * Complete the import process
   */
  async completeImport(
    sessionId: string,
    approvedProducts: EnhancedProductData[]
  ): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Import session not found');
    }

    session.steps[2].status = 'completed';
    session.steps[2].progress = 100;
    session.status = 'completed';

    this.updateProgress(sessionId, 'review', 100, `Import completed: ${approvedProducts.length} products`);
    
    // Clean up session after completion
    setTimeout(() => {
      this.activeSessions.delete(sessionId);
      this.progressCallbacks.delete(sessionId);
    }, 300000); // Keep for 5 minutes
  }

  /**
   * Extract data from uploaded file (CSV/Excel)
   */
  private async extractDataFromFile(file: File, sessionId: string): Promise<ExtractedProductData[]> {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    
    if (fileExtension === 'csv') {
      return this.extractFromCSV(file, sessionId);
    } else if (['xlsx', 'xls'].includes(fileExtension || '')) {
      return this.extractFromExcel(file, sessionId);
    } else {
      throw new Error('Unsupported file format. Please use CSV or Excel files.');
    }
  }

  /**
   * Extract data from CSV file
   */
  private async extractFromCSV(file: File, sessionId: string): Promise<ExtractedProductData[]> {
    return new Promise((resolve, reject) => {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          try {
            const extractedData = this.processRawData(results.data as Record<string, any>[], sessionId);
            resolve(extractedData);
          } catch (error) {
            reject(error);
          }
        },
        error: (error) => {
          reject(new Error(`CSV parsing failed: ${error.message}`));
        }
      });
    });
  }

  /**
   * Extract data from Excel file
   */
  private async extractFromExcel(file: File, sessionId: string): Promise<ExtractedProductData[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
          const jsonData = XLSX.utils.sheet_to_json(firstSheet) as Record<string, any>[];
          
          const extractedData = this.processRawData(jsonData, sessionId);
          resolve(extractedData);
        } catch (error) {
          reject(new Error(`Excel parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read Excel file'));
      };
      
      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * Process raw data and extract relevant fields
   */
  private processRawData(rawData: Record<string, any>[], sessionId: string): ExtractedProductData[] {
    const extractedData: ExtractedProductData[] = [];
    
    rawData.forEach((row, index) => {
      this.updateProgress(
        sessionId, 
        'extract', 
        Math.round(((index + 1) / rawData.length) * 80) + 10, 
        `Processing row ${index + 1}/${rawData.length}`
      );

      const extractedFields = this.extractFieldsFromRow(row);
      
      extractedData.push({
        originalRow: row,
        extractedFields,
        rowIndex: index
      });
    });

    return extractedData;
  }

  /**
   * Extract relevant fields from a single row using intelligent field mapping
   */
  private extractFieldsFromRow(row: Record<string, any>): ExtractedProductData['extractedFields'] {
    const fields: ExtractedProductData['extractedFields'] = {};
    
    // Convert row keys to lowercase for case-insensitive matching
    const lowerCaseRow: Record<string, any> = {};
    Object.keys(row).forEach(key => {
      lowerCaseRow[key.toLowerCase()] = row[key];
    });

    // Part Number patterns
    const partNumberPatterns = [
      'part_number', 'partnumber', 'part number', 'article_number', 'articlenumber', 
      'article number', 'sku', 'item_number', 'itemnumber', 'item number', 'code'
    ];
    
    // Product Name patterns
    const namePatterns = [
      'product_name', 'productname', 'product name', 'name', 'title', 'description', 'item_name'
    ];
    
    // Manufacturer patterns
    const manufacturerPatterns = [
      'manufacturer', 'brand', 'make', 'supplier', 'vendor'
    ];
    
    // Price patterns
    const pricePatterns = [
      'price', 'retail_price', 'retailprice', 'retail price', 'cost', 'amount'
    ];
    
    // Stock patterns
    const stockPatterns = [
      'stock', 'quantity', 'qty', 'stock_quantity', 'stockquantity', 'stock quantity', 'inventory'
    ];

    // Extract fields using pattern matching
    fields.partNumber = this.findFieldByPatterns(lowerCaseRow, partNumberPatterns);
    fields.productName = this.findFieldByPatterns(lowerCaseRow, namePatterns);
    fields.manufacturer = this.findFieldByPatterns(lowerCaseRow, manufacturerPatterns);
    
    const priceValue = this.findFieldByPatterns(lowerCaseRow, pricePatterns);
    if (priceValue) {
      const numericPrice = parseFloat(String(priceValue).replace(/[^0-9.]/g, ''));
      if (!isNaN(numericPrice)) {
        fields.retailPrice = numericPrice;
      }
    }
    
    const stockValue = this.findFieldByPatterns(lowerCaseRow, stockPatterns);
    if (stockValue) {
      const numericStock = parseInt(String(stockValue).replace(/[^0-9]/g, ''));
      if (!isNaN(numericStock)) {
        fields.stockQuantity = numericStock;
      }
    }

    return fields;
  }

  /**
   * Find field value by matching patterns
   */
  private findFieldByPatterns(row: Record<string, any>, patterns: string[]): string | undefined {
    for (const pattern of patterns) {
      const value = row[pattern];
      if (value !== undefined && value !== null && String(value).trim() !== '') {
        return String(value).trim();
      }
    }
    return undefined;
  }

  /**
   * Update progress for a session
   */
  private updateProgress(sessionId: string, step: string, progress: number, message: string): void {
    const callback = this.progressCallbacks.get(sessionId);
    if (callback) {
      callback({
        sessionId,
        currentStep: step,
        progress,
        message
      });
    }
  }

  /**
   * Get session status
   */
  getSession(sessionId: string): ImportSession | undefined {
    return this.activeSessions.get(sessionId);
  }

  /**
   * Cancel import session
   */
  cancelSession(sessionId: string): void {
    this.activeSessions.delete(sessionId);
    this.progressCallbacks.delete(sessionId);
  }
}

export const enhancedImportService = new EnhancedImportService();

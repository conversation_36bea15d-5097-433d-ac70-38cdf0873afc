/**
 * AI-Powered Intelligent Column Mapping Service
 * Uses OpenAI GPT to analyze column headers and cell content for 100% accurate mapping
 */

import { CATEGORIES } from '@/data/categoryData';
import { analyzeColumnsWithProductionAI } from './aiMappingBackend';

export interface ColumnAnalysis {
  originalColumn: string;
  targetField: string;
  confidence: number;
  reasoning: string;
  sampleValues: string[];
}

export interface AIColumnMappingResult {
  mappings: ColumnAnalysis[];
  suggestedCategory: string;
  suggestedSubcategory: string;
  unmappedColumns: string[];
  excludedColumns: string[];
  consolidatedColumns: string[];
  confidence: number;
  retailFocusScore: number;
}

/**
 * Target fields in our product system - Enhanced for retail focus
 */
export const TARGET_FIELDS = {
  // Core identification (PRIORITY 1 - Essential for retail)
  partArticleNumber: 'Part Article Number - Primary product identifier',
  name: 'Product Name - Main product title/description',
  sku: 'SKU - Stock Keeping Unit identifier',

  // Business information (PRIORITY 2 - Important for retail)
  manufacturer: 'Manufacturer - Brand or company that makes the product',
  stockQuantity: 'Stock Quantity - Number of units available',
  retailPrice: 'Retail Price - Selling price for consumers/merchants',

  // Categorization (PRIORITY 3 - Required for organization)
  category: 'Category - Main product category',
  subcategory: 'Subcategory - Specific product subcategory (optional)',

  // Content and specifications (PRIORITY 4 - Enhances customer experience)
  descriptionAndSpecifications: 'Description - Detailed product description and specifications',

  // Vehicle compatibility (PRIORITY 5 - Important for automotive parts)
  vehicleCompatibility: 'Vehicle Compatibility - Compatible vehicle types (text input)',

  // Images (PRIORITY 6 - Visual representation)
  primaryImage: 'Primary Image - Main product image URL',
  additionalImages: 'Additional Images - Array of additional image URLs',

  // System fields (PRIORITY 7 - Administrative)
  status: 'Status - Product status (draft, active, out_of_stock, discontinued)',

  // Category-specific fields for tyres (PRIORITY 8 - Specialized)
  width: 'Tyre Width - Width measurement for tyres',
  aspectRatio: 'Aspect Ratio - Aspect ratio for tyres',
  rimDiameter: 'Rim Diameter - Rim diameter for tyres',
  loadIndex: 'Load Index - Load index for tyres',
  speedRating: 'Speed Rating - Speed rating for tyres',
  season: 'Season - Tyre season type (summer, winter, all-season)',

  // Optional business fields (PRIORITY 9 - Less critical)
  supplierName: 'Supplier Name - Company/vendor supplying the product',
  shippingOrigin: 'Shipping Origin - Location where product ships from'
};

/**
 * B2B/Wholesale columns that should be EXCLUDED from retail mapping
 */
export const EXCLUDED_COLUMN_PATTERNS = [
  // Wholesale pricing patterns
  /wholesale.*price/i,
  /bulk.*price/i,
  /dealer.*price/i,
  /trade.*price/i,
  /cost.*price/i,
  /supplier.*price/i,
  /purchase.*price/i,

  // Internal business codes
  /internal.*code/i,
  /supplier.*code/i,
  /vendor.*code/i,
  /purchase.*order/i,
  /po.*number/i,

  // B2B specific fields
  /minimum.*order/i,
  /moq/i,
  /lead.*time/i,
  /delivery.*time/i,
  /payment.*terms/i,
  /credit.*terms/i,

  // Internal logistics
  /warehouse.*location/i,
  /bin.*location/i,
  /shelf.*location/i,
  /storage.*location/i,

  // Supplier specific
  /supplier.*contact/i,
  /vendor.*contact/i,
  /account.*manager/i,

  // Financial/accounting
  /margin/i,
  /markup/i,
  /profit/i,
  /commission/i,
  /rebate/i
];

/**
 * Enhanced column analysis with content inspection
 */
export interface EnhancedColumnAnalysis {
  originalColumn: string;
  targetField: string;
  confidence: number;
  reasoning: string;
  sampleValues: string[];
  contentType: 'text' | 'numeric' | 'price' | 'code' | 'url' | 'mixed';
  isRetailRelevant: boolean;
  shouldExclude: boolean;
  priority: number;
}

/**
 * Analyze column content type and retail relevance
 */
export const analyzeColumnContent = (columnName: string, sampleValues: any[]): {
  contentType: 'text' | 'numeric' | 'price' | 'code' | 'url' | 'mixed';
  isRetailRelevant: boolean;
  shouldExclude: boolean;
  priority: number;
} => {
  const cleanValues = sampleValues
    .filter(val => val !== null && val !== undefined && String(val).trim() !== '')
    .map(val => String(val).trim())
    .slice(0, 10); // Analyze up to 10 sample values

  if (cleanValues.length === 0) {
    return { contentType: 'text', isRetailRelevant: false, shouldExclude: true, priority: 10 };
  }

  // Check if column should be excluded (B2B/wholesale patterns)
  const shouldExclude = EXCLUDED_COLUMN_PATTERNS.some(pattern => pattern.test(columnName));

  // Determine content type
  let contentType: 'text' | 'numeric' | 'price' | 'code' | 'url' | 'mixed' = 'text';

  const numericCount = cleanValues.filter(val => /^\d+(\.\d+)?$/.test(val)).length;
  const priceCount = cleanValues.filter(val => /^\$?\d+(\.\d{2})?$|^\d+(\.\d{2})?\s*(€|£|\$|USD|EUR|GBP|DZD)$/i.test(val)).length;
  const urlCount = cleanValues.filter(val => /^https?:\/\//.test(val)).length;
  const codeCount = cleanValues.filter(val => /^[A-Z0-9\-_]{3,}$/i.test(val) && val.length < 50).length;

  const total = cleanValues.length;

  if (priceCount / total > 0.7) contentType = 'price';
  else if (urlCount / total > 0.5) contentType = 'url';
  else if (codeCount / total > 0.6) contentType = 'code';
  else if (numericCount / total > 0.8) contentType = 'numeric';
  else if (numericCount > 0 || priceCount > 0) contentType = 'mixed';

  // Determine retail relevance and priority
  const columnLower = columnName.toLowerCase();
  let isRetailRelevant = true;
  let priority = 5;

  // High priority retail fields
  if (/^(name|title|product|part|article|sku|brand|manufacturer)$/i.test(columnLower)) {
    priority = 1;
  } else if (/^(price|cost|retail|stock|quantity|category)$/i.test(columnLower) && !shouldExclude) {
    priority = 2;
  } else if (/^(description|spec|compatibility|vehicle|application)$/i.test(columnLower)) {
    priority = 3;
  } else if (/^(image|photo|picture|url)$/i.test(columnLower)) {
    priority = 4;
  } else if (shouldExclude) {
    isRetailRelevant = false;
    priority = 10;
  }

  return { contentType, isRetailRelevant, shouldExclude, priority };
};

/**
 * Get available categories for AI analysis (simplified - no subcategories)
 */
export const getAvailableCategories = () => {
  return CATEGORIES.filter(cat => cat.id !== 'all').map(cat => ({
    id: cat.id,
    name: cat.name,
    displayName: cat.displayName,
    description: cat.description
    // Removed subcategories to simplify AI mapping
  }));
};

/**
 * Prepare optimized data for AI analysis with intelligent sampling for large multi-category datasets
 */
export const prepareDataForAI = (data: any[]): { headers: string[], sampleData: any[] } => {
  if (data.length === 0) return { headers: [], sampleData: [] };

  const headers = Object.keys(data[0]);

  // Enhanced sampling strategy for large multi-category datasets
  let sampleData: any[] = [];

  if (data.length <= 10) {
    // Small dataset: use all data
    sampleData = data;
  } else if (data.length <= 1000) {
    // Medium dataset: use first 8 rows for better pattern recognition
    sampleData = data.slice(0, 8);
  } else {
    // Large dataset: DIVERSE CATEGORY SAMPLING for multi-category files
    console.log('🔍 Large multi-category dataset detected, using diverse sampling...');

    // Strategy: Sample from different sections to capture category diversity
    const sampleSize = Math.min(20, Math.max(10, Math.floor(data.length / 5000))); // 10-20 samples
    const sectionSize = Math.floor(data.length / sampleSize);

    // Take samples from evenly distributed sections
    for (let i = 0; i < sampleSize; i++) {
      const sectionStart = i * sectionSize;
      const sectionEnd = Math.min(sectionStart + sectionSize, data.length);

      // Take 1-2 rows from each section to capture category diversity
      if (sectionEnd > sectionStart) {
        sampleData.push(data[sectionStart]);
        if (sectionEnd > sectionStart + 1) {
          const midPoint = Math.floor((sectionStart + sectionEnd) / 2);
          sampleData.push(data[midPoint]);
        }
      }
    }

    // Remove duplicates based on content similarity
    sampleData = sampleData.filter((row, index, arr) => {
      const rowString = JSON.stringify(row);
      return arr.findIndex(r => JSON.stringify(r) === rowString) === index;
    });

    // Ensure we have diverse category representation
    const categoryFields = ['category', 'type', 'product_type', 'item_type', 'name'];
    const categoryValues = new Set();
    const diverseSample: any[] = [];

    // Prioritize rows with different category indicators
    sampleData.forEach(row => {
      const categoryValue = categoryFields
        .map(field => String(row[field] || '').toLowerCase().trim())
        .find(val => val && val.length > 0);

      if (!categoryValue || !categoryValues.has(categoryValue) || diverseSample.length < 8) {
        diverseSample.push(row);
        if (categoryValue) categoryValues.add(categoryValue);
      }
    });

    sampleData = diverseSample.slice(0, 15); // Increased to 15 for better category detection
  }

  console.log(`📊 AI Sampling: ${data.length} total rows → ${sampleData.length} sample rows (multi-category optimized)`);
  return { headers, sampleData };
};

/**
 * Comprehensive column analysis with retail focus
 */
export const analyzeAllColumns = (headers: string[], sampleData: any[]): {
  retailColumns: EnhancedColumnAnalysis[];
  excludedColumns: string[];
  consolidatedColumns: string[];
  retailFocusScore: number;
} => {
  const retailColumns: EnhancedColumnAnalysis[] = [];
  const excludedColumns: string[] = [];
  const consolidatedColumns: string[] = [];

  headers.forEach(header => {
    // Get sample values for this column
    const sampleValues = sampleData.map(row => row[header]).filter(val =>
      val !== null && val !== undefined && String(val).trim() !== ''
    );

    // Analyze column content
    const analysis = analyzeColumnContent(header, sampleValues);

    if (analysis.shouldExclude) {
      excludedColumns.push(header);
    } else if (analysis.isRetailRelevant) {
      // This column is suitable for retail mapping
      retailColumns.push({
        originalColumn: header,
        targetField: '', // Will be determined by AI
        confidence: 0, // Will be set by AI
        reasoning: '', // Will be set by AI
        sampleValues: sampleValues.slice(0, 5).map(val => String(val)),
        contentType: analysis.contentType,
        isRetailRelevant: analysis.isRetailRelevant,
        shouldExclude: analysis.shouldExclude,
        priority: analysis.priority
      });
    } else {
      // Column has valuable data but not directly mappable - consolidate into description
      consolidatedColumns.push(header);
    }
  });

  // Calculate retail focus score
  const totalColumns = headers.length;
  const retailRelevantCount = retailColumns.length;
  const retailFocusScore = Math.round((retailRelevantCount / totalColumns) * 100);

  // Sort retail columns by priority
  retailColumns.sort((a, b) => a.priority - b.priority);

  return {
    retailColumns,
    excludedColumns,
    consolidatedColumns,
    retailFocusScore
  };
};

// Token-optimized mapping cache
export const mappingCache = new Map<string, AIColumnMappingResult>();

/**
 * Generate cache key from headers
 */
export const getCacheKey = (headers: string[]): string => {
  return headers.sort().join('|').toLowerCase();
};

/**
 * Estimate token count (rough approximation: 1 token ≈ 4 characters)
 */
export const estimateTokens = (text: string): number => {
  return Math.ceil(text.length / 4);
};

/**
 * Validate category and subcategory against available options
 */
export const validateCategoryMapping = (category: string, subcategory: string): { validCategory: string; validSubcategory: string } => {
  const availableCategories = getAvailableCategories();

  // Find exact match first
  let validCategory = availableCategories.find(cat => cat.id === category)?.id;

  // If no exact match, try fuzzy matching
  if (!validCategory) {
    validCategory = availableCategories.find(cat =>
      cat.name.toLowerCase().includes(category.toLowerCase()) ||
      category.toLowerCase().includes(cat.name.toLowerCase())
    )?.id;
  }

  // Default to brakes if no match found
  if (!validCategory) {
    validCategory = 'brakes';
  }

  // Validate subcategory
  const categoryData = availableCategories.find(cat => cat.id === validCategory);
  let validSubcategory = categoryData?.subcategories.find(sub => sub.id === subcategory)?.id;

  // If no exact subcategory match, try fuzzy matching
  if (!validSubcategory && categoryData) {
    validSubcategory = categoryData.subcategories.find(sub =>
      sub.name.toLowerCase().includes(subcategory.toLowerCase()) ||
      subcategory.toLowerCase().includes(sub.name.toLowerCase())
    )?.id;
  }

  // Default to first subcategory if no match
  if (!validSubcategory && categoryData?.subcategories.length > 0) {
    validSubcategory = categoryData.subcategories[0].id;
  }

  return { validCategory, validSubcategory: validSubcategory || '' };
};

/**
 * Generate intelligent product name from row data
 */
export const generateProductName = (rowData: any, mappings: ColumnAnalysis[]): string => {
  const getValue = (targetField: string): string => {
    const mapping = mappings.find(m => m.targetField === targetField);
    return mapping ? String(rowData[mapping.originalColumn] || '') : '';
  };

  const brand = getValue('manufacturer') || getValue('supplierName');
  const type = getValue('name') || getValue('category');
  const vehicle = getValue('vehicleCompatibility');
  const partNumber = getValue('partArticleNumber');

  // Build intelligent name
  let name = '';
  if (brand) name += brand + ' ';
  if (type) name += type;
  if (vehicle) name += ` for ${vehicle}`;
  if (!name && partNumber) name = `Part ${partNumber}`;
  if (!name) name = 'Automotive Part';

  return name.trim();
};

/**
 * Enhanced intelligent data consolidation for product descriptions
 */
export const generateEnhancedDescription = (
  rowData: any,
  mappings: ColumnAnalysis[],
  unmappedColumns: string[],
  consolidatedColumns: string[]
): string => {
  const sections: string[] = [];

  // 1. Core Product Information (from mapped fields)
  const coreInfo = mappings
    .filter(m => m.targetField === 'descriptionAndSpecifications' && rowData[m.originalColumn])
    .map(m => String(rowData[m.originalColumn]).trim())
    .filter(info => info.length > 0);

  if (coreInfo.length > 0) {
    sections.push(`Product Details: ${coreInfo.join('. ')}`);
  }

  // 2. Technical Specifications (consolidate technical data)
  const technicalColumns = consolidatedColumns.filter(col => {
    const colLower = col.toLowerCase();
    return colLower.includes('spec') || colLower.includes('dimension') ||
           colLower.includes('weight') || colLower.includes('size') ||
           colLower.includes('material') || colLower.includes('capacity');
  });

  const technicalSpecs = technicalColumns
    .map(col => ({ column: col, value: rowData[col] }))
    .filter(item => item.value && String(item.value).trim())
    .map(item => `${item.column.replace(/[_-]/g, ' ')}: ${item.value}`);

  if (technicalSpecs.length > 0) {
    sections.push(`Technical Specifications: ${technicalSpecs.join(', ')}`);
  }

  // 3. Vehicle Compatibility (consolidate compatibility data)
  const compatibilityColumns = consolidatedColumns.filter(col => {
    const colLower = col.toLowerCase();
    return colLower.includes('vehicle') || colLower.includes('car') ||
           colLower.includes('model') || colLower.includes('year') ||
           colLower.includes('engine') || colLower.includes('application');
  });

  const compatibilityInfo = compatibilityColumns
    .map(col => ({ column: col, value: rowData[col] }))
    .filter(item => item.value && String(item.value).trim())
    .map(item => String(item.value).trim());

  if (compatibilityInfo.length > 0) {
    sections.push(`Vehicle Compatibility: ${compatibilityInfo.join(', ')}`);
  }

  // 4. Additional Features (other valuable unmapped data)
  const featureColumns = unmappedColumns.filter(col => {
    const colLower = col.toLowerCase();
    return !colLower.includes('price') && !colLower.includes('cost') &&
           !colLower.includes('supplier') && !colLower.includes('vendor') &&
           !colLower.includes('internal') && !colLower.includes('code') &&
           col.length > 0;
  });

  const additionalFeatures = featureColumns
    .map(col => ({ column: col, value: rowData[col] }))
    .filter(item => item.value && String(item.value).trim() &&
                   String(item.value).trim().length > 2)
    .slice(0, 5) // Limit to 5 most relevant features
    .map(item => `${item.column.replace(/[_-]/g, ' ')}: ${item.value}`);

  if (additionalFeatures.length > 0) {
    sections.push(`Additional Features: ${additionalFeatures.join(', ')}`);
  }

  return sections.filter(section => section.length > 0).join('\n\n');
};

/**
 * Smart category detection based on product type analysis - Enhanced for multi-category datasets
 */
export const detectCategoryFromData = (headers: string[], sampleData: any[]): { category: string; subcategory: string; confidence: number } => {
  // Analyze each sample row to detect category diversity
  const categoryIndicators = new Map<string, number>();

  sampleData.forEach(row => {
    const rowText = headers.map(h => String(row[h] || '')).join(' ').toLowerCase();

    // Check for category keywords in each row
    if (rowText.includes('tyre') || rowText.includes('tire')) {
      categoryIndicators.set('tyres', (categoryIndicators.get('tyres') || 0) + 1);
    }
    if (rowText.includes('brake') || rowText.includes('pad') || rowText.includes('disc')) {
      categoryIndicators.set('brakes', (categoryIndicators.get('brakes') || 0) + 1);
    }
    if (rowText.includes('oil') || rowText.includes('lubricant') || rowText.includes('fluid')) {
      categoryIndicators.set('oils-lubricants', (categoryIndicators.get('oils-lubricants') || 0) + 1);
    }
    if (rowText.includes('filter') || rowText.includes('air filter') || rowText.includes('oil filter')) {
      categoryIndicators.set('filters', (categoryIndicators.get('filters') || 0) + 1);
    }
    if (rowText.includes('battery') || rowText.includes('electrical')) {
      categoryIndicators.set('electrical-components', (categoryIndicators.get('electrical-components') || 0) + 1);
    }
    if (rowText.includes('engine') || rowText.includes('motor')) {
      categoryIndicators.set('engine-components', (categoryIndicators.get('engine-components') || 0) + 1);
    }
  });

  // If multiple categories detected, this is a multi-category file
  if (categoryIndicators.size > 1) {
    console.log('🔍 Multi-category dataset detected:', Array.from(categoryIndicators.entries()));
    // Return 'all-other-categories' for mixed datasets
    return { category: 'all-other-categories', subcategory: '', confidence: 85 };
  }

  // Single category detection
  const allText = sampleData.map(row =>
    headers.map(h => String(row[h] || '')).join(' ')
  ).join(' ').toLowerCase();

  // Category detection patterns with confidence scoring
  const categoryPatterns = [
    { category: 'brakes', subcategory: 'brake-disc', patterns: ['brake disc', 'brake disk', 'rotor'], confidence: 95 },
    { category: 'brakes', subcategory: 'brake-pads', patterns: ['brake pad', 'brake pads', 'pad set'], confidence: 95 },
    { category: 'filters', subcategory: 'oil-filters', patterns: ['oil filter', 'filter oil'], confidence: 95 },
    { category: 'filters', subcategory: 'air-filters', patterns: ['air filter', 'filter air'], confidence: 95 },
    { category: 'filters', subcategory: 'fuel-filters', patterns: ['fuel filter', 'filter fuel'], confidence: 95 },
    { category: 'damping', subcategory: 'shock-absorber', patterns: ['shock absorber', 'damper', 'strut'], confidence: 90 },
    { category: 'engine', subcategory: 'spark-plugs', patterns: ['spark plug', 'ignition plug'], confidence: 95 },
    { category: 'tyres', subcategory: 'tyres', patterns: ['tyre', 'tire', 'wheel'], confidence: 90 },
    { category: 'oils-fluids', subcategory: 'engine-lubrication-oil', patterns: ['engine oil', 'motor oil', 'lubricant'], confidence: 90 }
  ];

  let bestMatch = { category: 'brakes', subcategory: 'brake-disc', confidence: 50 };

  for (const pattern of categoryPatterns) {
    for (const searchPattern of pattern.patterns) {
      if (allText.includes(searchPattern)) {
        if (pattern.confidence > bestMatch.confidence) {
          bestMatch = pattern;
        }
      }
    }
  }

  return bestMatch;
};

/**
 * SIMPLIFIED AI prompt for 100% accurate mapping
 */
export const createAIPrompt = (headers: string[], sampleData: any[]): string => {
  // Show actual sample data for better accuracy
  const samples = sampleData.slice(0, 3).map((row, i) => {
    const sampleValues = headers.map(h => {
      const value = String(row[h] || '').trim();
      return `"${h}": "${value}"`;
    }).join(', ');
    return `Sample ${i + 1}: {${sampleValues}}`;
  }).join('\n');

  // Simple category detection
  const detectedCategory = detectCategoryFromData(headers, sampleData);

  return `You are an expert at mapping CSV columns to product database fields. Analyze the CSV data and map each column to the correct target field.

CSV HEADERS: ${headers.join(', ')}

SAMPLE DATA:
${samples}

TARGET FIELDS (map to these exact field names):
- partArticleNumber: Part numbers, article numbers, SKUs, product codes
- name: Product names, titles, descriptions
- manufacturer: Brand names, manufacturers, suppliers
- category: Product categories
- retailPrice: Prices, costs (for retail/consumer)
- stockQuantity: Stock, quantity, inventory
- descriptionAndSpecifications: Detailed descriptions, specifications
- vehicleCompatibility: Vehicle compatibility, applications
- primaryImage: Image URLs, photo links

CRITICAL MAPPING RULES:
1. Part numbers/article numbers/SKUs → partArticleNumber
2. Product names/titles → name
3. Brand/manufacturer names → manufacturer
4. Prices → retailPrice
5. Stock/quantity → stockQuantity
6. If unsure, leave unmapped

DETECTED CATEGORY: ${detectedCategory.category}

Return ONLY this JSON format:
{
  "mappings": [{"originalColumn": "exact_column_name", "targetField": "target_field", "confidence": 98}],
  "suggestedCategory": "${detectedCategory.category}",
  "unmappedColumns": [],
  "confidence": 95
}`;
};

/**
 * Analyze columns using AI with production backend service
 */
export const analyzeColumnsWithAI = async (
  data: any[],
  apiKey?: string
): Promise<AIColumnMappingResult> => {
  try {
    console.log('🤖 Using production AI mapping service...');
    return await analyzeColumnsWithProductionAI(data);

  } catch (error) {
    console.error('❌ AI column mapping failed:', error);

    // Fallback to pattern matching
    console.log('🔄 Falling back to pattern matching...');
    const { headers, sampleData } = prepareDataForAI(data);
    const fallbackResult = fallbackColumnMapping(headers, sampleData);

    console.log('✅ Fallback mapping completed');
    return fallbackResult;
  }
};

/**
 * Legacy function for direct API calls (kept for compatibility)
 */
export const analyzeColumnsWithDirectAPI = async (
  headers: string[],
  sampleData: any[],
  apiKey: string
): Promise<AIColumnMappingResult> => {
  const prompt = createAIPrompt(headers, sampleData);

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert data analyst specializing in automotive parts data mapping. Always respond with valid JSON only.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    const aiResponse = result.choices[0]?.message?.content;

    if (!aiResponse) {
      throw new Error('No response from OpenAI API');
    }

    // Parse the JSON response
    const parsedResult: AIColumnMappingResult = JSON.parse(aiResponse);

    // Validate the response structure
    if (!parsedResult.mappings || !Array.isArray(parsedResult.mappings)) {
      throw new Error('Invalid AI response structure');
    }

    return parsedResult;

  } catch (error) {
    console.error('AI column mapping error:', error);
    throw new Error(`AI analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Enhanced fallback mapping with smart category detection
 */
export const fallbackColumnMapping = (headers: string[], sampleData: any[]): AIColumnMappingResult => {
  const mappings: ColumnAnalysis[] = [];
  const unmappedColumns: string[] = [];

  // Use smart category detection for fallback
  const detectedCategory = detectCategoryFromData(headers, sampleData);
  
  // Enhanced pattern matching rules
  const patterns = {
    partArticleNumber: [
      /^(vendor|supplier|item|part.*number|article.*number|sku|product.*code|reference|ref)$/i,
      /^(part.*article|article.*part|item.*code|product.*id)$/i
    ],
    manufacturer: [
      /^(brand|manufacturer|make|producer|company)$/i,
      /^(brand.*name|manufacturer.*name|make.*name)$/i
    ],
    name: [
      /^(name|title|product.*name|item.*name|description|product.*title)$/i,
      /^(product.*description|item.*description)$/i
    ],
    supplierName: [
      /^(supplier|vendor|distributor|wholesaler)$/i,
      /^(supplier.*name|vendor.*name)$/i
    ],
    retailPrice: [
      /^(price|retail.*price|unit.*price|selling.*price|list.*price)$/i,
      /^(netto.*price|net.*price|cost)$/i
    ],
    stockQuantity: [
      /^(stock|quantity|qty|inventory|available|on.*hand)$/i,
      /^(stock.*quantity|available.*quantity)$/i
    ],
    vehicleCompatibility: [
      /^(vehicle|car|auto|compatibility|application|fits)$/i,
      /^(vehicle.*type|car.*type|auto.*type)$/i
    ]
  };

  headers.forEach(header => {
    let mapped = false;
    const cleanHeader = header.toLowerCase().trim();
    
    for (const [targetField, regexList] of Object.entries(patterns)) {
      if (regexList.some(regex => regex.test(cleanHeader))) {
        const sampleValues = sampleData.slice(0, 3).map(row => row[header]?.toString() || '');
        mappings.push({
          originalColumn: header,
          targetField,
          confidence: 75,
          reasoning: `Pattern match for ${targetField}`,
          sampleValues
        });
        mapped = true;
        break;
      }
    }
    
    if (!mapped) {
      unmappedColumns.push(header);
    }
  });

  // Validate detected category
  const { validCategory, validSubcategory } = validateCategoryMapping(
    detectedCategory.category,
    detectedCategory.subcategory
  );

  return {
    mappings,
    suggestedCategory: validCategory,
    suggestedSubcategory: validSubcategory,
    unmappedColumns,
    confidence: Math.max(60, detectedCategory.confidence - 10) // Slightly lower confidence for fallback
  };
};

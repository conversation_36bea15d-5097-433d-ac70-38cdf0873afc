/**
 * AI-Powered Web Search Service for Product Data Enrichment
 * Uses Tavily API for real-time web search + OpenAI for intelligent processing
 */

import { CATEGORIES } from '@/data/categoryData';

export interface WebSearchResult {
  title: string;
  url: string;
  content: string;
  score: number;
}

export interface EnhancedProductData {
  // Core Product Information
  productName: string;
  partArticleNumber: string;
  manufacturer: string;
  category: string;
  stockQuantity?: number;
  retailPrice?: number;

  // Vehicle Compatibility
  vehicleCompatibility: string; // "Brand Model Engine (Year-Year), Brand Model Engine (Year-Year)"

  // Detailed Description (5 sections)
  description: {
    generalInformation: string;
    technicalInformation: string;
    applicability: string;
    originalNumbers: string;
    oemNumbers: string;
  };

  // Metadata
  confidence: number;
  searchSources: string[];
}

export interface ProductEnhancementRequest {
  partNumber?: string;
  productName?: string;
  manufacturer?: string;
  category?: string;
  existingData?: Record<string, any>;
}

export class AIWebSearchService {
  private openaiApiKey: string;

  constructor() {
    this.openaiApiKey = import.meta.env.VITE_OPENAI_API_KEY || '';

    if (!this.openaiApiKey) {
      console.warn('⚠️ Missing OpenAI API key for AI Web Search Service');
      console.log('Please add VITE_OPENAI_API_KEY to your .env file');
    } else {
      console.log('✅ AI Web Search Service initialized with OpenAI web search');
      console.log('🔑 API Key configured:', this.openaiApiKey.substring(0, 20) + '...');
    }
  }

  /**
   * Perform actual web search using OpenAI's web search tool
   * Try both Chat Completions API and Responses API
   */
  private async performRealWebSearch(partNumber: string): Promise<string> {
    try {
      console.log('🔍 Performing REAL web search for:', partNumber);

      // First try with Chat Completions API
      try {
        const result = await this.performWebSearchWithChatAPI(partNumber);
        if (result && result.trim() && !result.includes('Web search was initiated but results are not yet available')) {
          return result;
        }
      } catch (chatError) {
        console.warn('⚠️ Chat Completions API failed, trying Responses API:', chatError.message);
      }

      // Fallback to Responses API
      try {
        const result = await this.performWebSearchWithResponsesAPI(partNumber);
        if (result && result.trim()) {
          return result;
        }
      } catch (responsesError) {
        console.warn('⚠️ Responses API also failed:', responsesError.message);
      }

      throw new Error('Both Chat Completions and Responses APIs failed for web search');

    } catch (error) {
      console.error('❌ Real web search failed:', error);
      throw error;
    }
  }

  /**
   * Perform web search using Chat Completions API
   */
  private async performWebSearchWithChatAPI(partNumber: string): Promise<string> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.openaiApiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
          {
            role: 'user',
            content: `Search the web for comprehensive information about automotive part number "${partNumber}". I need detailed technical specifications, vehicle compatibility, manufacturer information, OEM cross-references, dimensions, installation requirements, and any other relevant automotive part data. Please provide a comprehensive summary of all findings.`
          }
        ],
        tools: [
          {
            type: "web_search"
          }
        ],
        tool_choice: "auto"
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Chat Completions API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('🔍 Chat Completions Response:', JSON.stringify(data, null, 2));

    const message = data.choices[0]?.message;
    if (!message) {
      throw new Error('No message in Chat Completions response');
    }

    if (message.content && message.content.trim()) {
      console.log('✅ Chat Completions web search completed with results');
      return message.content;
    }

    if (message.tool_calls && message.tool_calls.length > 0) {
      console.log('🔄 Chat Completions: Web search tool was called, but no final content yet');
      return 'Web search was initiated but results are not yet available. Please try again.';
    }

    throw new Error('No content or tool calls in Chat Completions response');
  }

  /**
   * Perform web search using Responses API
   */
  private async performWebSearchWithResponsesAPI(partNumber: string): Promise<string> {
    const response = await fetch('https://api.openai.com/v1/responses', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.openaiApiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        input: `Search the web for comprehensive information about automotive part number "${partNumber}". I need detailed technical specifications, vehicle compatibility, manufacturer information, OEM cross-references, dimensions, installation requirements, and any other relevant automotive part data. Please provide a comprehensive summary of all findings.`,
        tools: [
          {
            type: "web_search"
          }
        ]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Responses API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('🔍 Responses API Response:', JSON.stringify(data, null, 2));

    // Extract content from Responses API format
    const output = data.output;
    if (!output || !Array.isArray(output)) {
      throw new Error('Invalid output format in Responses API response');
    }

    // Find the message with content
    for (const item of output) {
      if (item.type === 'message' && item.content && Array.isArray(item.content)) {
        for (const contentItem of item.content) {
          if (contentItem.type === 'output_text' && contentItem.text) {
            console.log('✅ Responses API web search completed with results');
            return contentItem.text;
          }
        }
      }
    }

    throw new Error('No text content found in Responses API output');
  }



  /**
   * Process product data using REAL web search + OpenAI analysis
   */
  async processWithOpenAIWebSearch(basicData: BasicProductData): Promise<EnhancedProductData> {
    try {
      const categoryList = CATEGORIES.map(cat => cat.id).join(', ');

      console.log('🔍 Starting REAL web search for part:', basicData.partArticleNumber);

      // Perform actual web search using OpenAI's web search tool
      const webSearchResults = await this.performRealWebSearch(basicData.partArticleNumber);

      if (!webSearchResults || webSearchResults.trim().length === 0) {
        console.warn('⚠️ No web search results found, using fallback data');
        return this.createFallbackData({ partNumber: basicData.partArticleNumber });
      }

      if (webSearchResults.includes('Web search was initiated but results are not yet available')) {
        console.warn('⚠️ Web search still processing, using fallback data');
        return this.createFallbackData({ partNumber: basicData.partArticleNumber });
      }

      console.log('✅ REAL web search completed with', webSearchResults.length, 'characters of data');
      console.log('🔍 Analyzing web search results...');

      // Have OpenAI analyze the comprehensive search results
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.openaiApiKey}`
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content: `You are an expert automotive parts analyst with deep knowledge of automotive systems, part specifications, and vehicle compatibility. Analyze the provided REAL web search results and extract detailed product information.

CRITICAL REQUIREMENTS:
1. Extract ALL available information from the REAL web search results provided
2. Generate detailed, specific descriptions for each section (minimum 100 words per section)
3. Provide comprehensive vehicle compatibility with specific year ranges, makes, models, and engines
4. Include detailed technical specifications, dimensions, and performance data
5. Set confidence to 95 if comprehensive data is found, 25 if insufficient
6. Use ONLY information found in the REAL web search results - do not add external knowledge

Return ONLY valid JSON in this exact format:
{
  "productName": "Complete product name with brand and specifications from REAL web search results",
  "partArticleNumber": "${basicData.partArticleNumber}",
  "manufacturer": "Exact manufacturer/brand name from REAL web search results",
  "category": "exact category ID from list below",
  "stockQuantity": 0,
  "retailPrice": 0,
  "vehicleCompatibility": "Comprehensive vehicle compatibility with year ranges, makes, models, engines from REAL web search results",
  "description": {
    "generalInformation": "Detailed product description including features, benefits, construction, materials, and general specifications from REAL web search (minimum 100 words)",
    "technicalInformation": "Comprehensive technical specifications including dimensions, tolerances, operating conditions, performance data, and engineering details from REAL web search (minimum 100 words)",
    "applicability": "Complete vehicle compatibility information with specific makes, models, engine types, year ranges, VIN details, and fitment notes from REAL web search (minimum 100 words)",
    "originalNumbers": "All part numbers, cross-references, equivalent numbers, and alternative part numbers found in REAL web search results",
    "oemNumbers": "OEM manufacturer part numbers, original equipment numbers, and factory equivalents from REAL web search results"
  },
  "confidence": 95,
  "searchSources": ["actual URLs from web search results"]
}

Available categories: ${categoryList}

CRITICAL: Extract comprehensive, detailed information from the REAL web search results. Each description section must be thorough and informative. Use specific data from the REAL web search results to create detailed, professional descriptions.`
            },
            {
              role: 'user',
              content: `Analyze these REAL web search results for automotive part "${basicData.partArticleNumber}" and extract detailed product information. Create thorough, professional descriptions for each section:

WEB SEARCH RESULTS:
${webSearchResults}

ANALYSIS REQUIREMENTS:
1. Extract the complete product name including brand and specifications
2. Identify the exact manufacturer from the search results
3. Determine the correct category from the available list
4. Create detailed descriptions for each section:
   - General Information: Product features, construction, materials, benefits (minimum 100 words)
   - Technical Information: Specifications, dimensions, performance data, operating conditions (minimum 100 words)
   - Applicability: Vehicle compatibility with makes, models, engines, years, fitment details (minimum 100 words)
   - Original Numbers: All part numbers and cross-references found
   - OEM Numbers: Original equipment manufacturer numbers and equivalents

5. Provide comprehensive vehicle compatibility information
6. Set confidence level based on data quality (95 for comprehensive data, 25 for limited data)
7. Include all source URLs

Extract ALL available information from the search results and create detailed, professional descriptions for this automotive part.`
            }
          ],
          temperature: 0.1,
          max_tokens: 3000,
          response_format: { type: "json_object" }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ OpenAI API error:', response.status, errorText);
        throw new Error(`OpenAI API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('📊 Analysis response received:', data.usage || 'No usage data');

      const content = data.choices[0]?.message?.content;

      if (!content) {
        console.error('❌ No content in OpenAI analysis response:', data);
        throw new Error('No content in OpenAI response');
      }

      console.log('📝 Raw analysis content length:', content.length);
      console.log('📝 Analysis content preview:', content.substring(0, 200) + '...');

      // Parse the JSON response
      let enhancedData;
      try {
        enhancedData = JSON.parse(content);
      } catch (parseError) {
        console.error('❌ Failed to parse JSON response:', parseError);
        console.error('📝 Raw content that failed to parse:', content);
        throw new Error(`Failed to parse OpenAI response as JSON: ${parseError}`);
      }

      console.log('✅ Enhanced data extracted from REAL web search');
      console.log('📊 Product Name:', enhancedData.productName);
      console.log('📊 Manufacturer:', enhancedData.manufacturer);
      console.log('📊 Category:', enhancedData.category);
      console.log('📊 Confidence:', enhancedData.confidence);
      console.log('📊 Search Sources:', enhancedData.searchSources?.length || 0, 'sources');

      // Validate the enhanced data quality
      const hasRealData = enhancedData.productName &&
                         enhancedData.productName !== 'Unknown Product' &&
                         enhancedData.manufacturer &&
                         enhancedData.manufacturer !== 'Unknown' &&
                         enhancedData.description?.generalInformation &&
                         enhancedData.description.generalInformation.length > 50;

      if (!hasRealData) {
        console.warn('⚠️ Enhanced data appears to be low quality, using fallback');
        return this.createFallbackData({ partNumber: basicData.partArticleNumber });
      }

      // Validate and ensure required fields
      return {
        productName: enhancedData.productName || basicData.productName || 'Unknown Product',
        partArticleNumber: basicData.partArticleNumber,
        manufacturer: enhancedData.manufacturer || basicData.manufacturer || 'Unknown',
        category: enhancedData.category || 'other-automotive-parts',
        stockQuantity: basicData.stockQuantity || 0,
        retailPrice: basicData.retailPrice || 0,
        vehicleCompatibility: enhancedData.vehicleCompatibility || 'Universal Application - Please verify compatibility',
        description: enhancedData.description || {
          generalInformation: `${basicData.productName || 'Automotive Part'} - ${basicData.partArticleNumber}. This product requires manual verification of specifications and compatibility.`,
          technicalInformation: 'Technical specifications to be determined. Please consult manufacturer documentation.',
          applicability: 'Vehicle compatibility to be verified. Please check with manufacturer or parts catalog.',
          originalNumbers: basicData.partArticleNumber || 'N/A',
          oemNumbers: 'OEM numbers to be researched. Please consult manufacturer cross-reference.'
        },
        confidence: enhancedData.confidence || 25,
        searchSources: enhancedData.searchSources || []
      };

    } catch (error) {
      console.error('❌ OpenAI web search processing failed:', error);
      console.error('❌ Error details:', error.message);
      // Convert BasicProductData to ProductEnhancementRequest format for fallback
      const fallbackRequest: ProductEnhancementRequest = {
        partNumber: basicData.partArticleNumber,
        productName: basicData.productName,
        manufacturer: basicData.manufacturer,
        category: 'other-automotive-parts',
        existingData: {
          retailPrice: basicData.retailPrice,
          stockQuantity: basicData.stockQuantity
        }
      };
      return this.createFallbackData(fallbackRequest);
    }
  }

  /**
   * Use OpenAI to analyze search results and extract structured product data
   */
  async analyzeSearchResults(
    searchResults: WebSearchResult[],
    originalRequest: ProductEnhancementRequest
  ): Promise<EnhancedProductData> {
    
    const categoryList = CATEGORIES.map(cat => cat.id).join(', ');
    
    const prompt = `You are an automotive parts data expert. Analyze the following web search results and extract comprehensive product information.

ORIGINAL REQUEST:
Part Number: ${originalRequest.partNumber || 'Unknown'}
Product Name: ${originalRequest.productName || 'Unknown'}
Manufacturer: ${originalRequest.manufacturer || 'Unknown'}
Category: ${originalRequest.category || 'Unknown'}

SEARCH RESULTS:
${searchResults.map((result, index) => `
Result ${index + 1}:
Title: ${result.title}
URL: ${result.url}
Content: ${result.content.substring(0, 1000)}...
`).join('\n')}

AVAILABLE CATEGORIES (must match exactly):
${categoryList}

REQUIRED OUTPUT FORMAT (JSON):
{
  "productName": "Exact verified product name",
  "partArticleNumber": "Primary part/article number",
  "manufacturer": "Official brand/manufacturer name",
  "category": "Exact category from the list above",
  "vehicleCompatibility": "Brand Model Engine (Year-Year), Brand Model Engine (Year-Year)",
  "description": {
    "generalInformation": "Name, item number, installation side, brand, specifications",
    "technicalInformation": "Fitting position, dimensions, technical specifications",
    "applicability": "Complete vehicle compatibility with brands, models, engines, year ranges",
    "originalNumbers": "All part numbers associated with this component",
    "oemNumbers": "Official manufacturer part numbers only"
  },
  "confidence": 95,
  "searchSources": ["url1", "url2", "url3"]
}

CRITICAL REQUIREMENTS:
1. Category MUST match exactly from the provided list
2. Vehicle compatibility MUST include specific year ranges
3. All 5 description sections MUST be populated with web-sourced data
4. Use ONLY information found in the search results
5. If insufficient data, indicate with "Data not found in search results"

Return ONLY the JSON object, no additional text.`;

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.openaiApiKey}`
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content: 'You are an expert automotive parts data analyst. Extract accurate product information from web search results and format it according to the specified structure.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.1,
          max_tokens: 2000
        })
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices[0]?.message?.content;

      if (!content) {
        throw new Error('No response from OpenAI');
      }

      // Parse JSON response
      const enhancedData = JSON.parse(content) as EnhancedProductData;
      
      // Add search sources
      enhancedData.searchSources = searchResults.slice(0, 5).map(r => r.url);
      
      return enhancedData;

    } catch (error) {
      console.error('❌ AI analysis failed:', error);
      
      // Return fallback data
      return {
        productName: originalRequest.productName || 'Unknown Product',
        partArticleNumber: originalRequest.partNumber || 'Unknown',
        manufacturer: originalRequest.manufacturer || 'Unknown',
        category: originalRequest.category || 'other-parts',
        vehicleCompatibility: 'Data not found in search results',
        description: {
          generalInformation: 'Data not found in search results',
          technicalInformation: 'Data not found in search results',
          applicability: 'Data not found in search results',
          originalNumbers: 'Data not found in search results',
          oemNumbers: 'Data not found in search results'
        },
        confidence: 0,
        searchSources: []
      };
    }
  }

  /**
   * Main method: Enhance product data using OpenAI's integrated web search
   */
  async enhanceProductData(request: ProductEnhancementRequest): Promise<EnhancedProductData> {
    console.log('🔍 Starting OpenAI web search enhancement for:', request);

    try {
      // Convert request to BasicProductData format
      const basicData: BasicProductData = {
        partArticleNumber: request.partNumber || '',
        productName: request.productName || '',
        manufacturer: request.manufacturer || '',
        retailPrice: request.price || 0,
        stockQuantity: request.stockQuantity || 0
      };

      // Use OpenAI's integrated web search
      const enhancedData = await this.processWithOpenAIWebSearch(basicData);

      console.log('✅ OpenAI web search enhancement completed for:', request.partNumber);
      return enhancedData;

    } catch (error) {
      console.error('❌ OpenAI web search enhancement failed for:', request.partNumber, error);
      return this.createFallbackData(request);
    }
  }

  /**
   * Create fallback data when web search fails
   */
  private createFallbackData(request: { partNumber: string }): EnhancedProductData {
    return {
      productName: 'Unknown Product',
      partArticleNumber: request.partNumber,
      manufacturer: 'Unknown',
      category: 'other-automotive-parts',
      stockQuantity: 0,
      retailPrice: 0,
      vehicleCompatibility: 'Universal Application - Please verify compatibility',
      description: {
        generalInformation: `Automotive Part - ${request.partNumber}. This product requires manual verification of specifications and compatibility.`,
        technicalInformation: 'Technical specifications to be determined. Please consult manufacturer documentation.',
        applicability: 'Vehicle compatibility to be verified. Please check with manufacturer or parts catalog.',
        originalNumbers: request.partNumber,
        oemNumbers: 'OEM numbers to be researched. Please consult manufacturer cross-reference.'
      },
      confidence: 25,
      searchSources: []
    };
  }

  /**
   * Batch enhance multiple products
   */
  async enhanceMultipleProducts(
    requests: ProductEnhancementRequest[],
    onProgress?: (completed: number, total: number) => void
  ): Promise<EnhancedProductData[]> {
    const results: EnhancedProductData[] = [];
    
    for (let i = 0; i < requests.length; i++) {
      try {
        const enhanced = await this.enhanceProductData(requests[i]);
        results.push(enhanced);
        
        // Rate limiting - wait between requests
        if (i < requests.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        onProgress?.(i + 1, requests.length);
      } catch (error) {
        console.error(`❌ Failed to enhance product ${i + 1}:`, error);
        // Add fallback data for failed requests
        results.push({
          productName: requests[i].productName || 'Unknown Product',
          partArticleNumber: requests[i].partNumber || 'Unknown',
          manufacturer: requests[i].manufacturer || 'Unknown',
          category: requests[i].category || 'other-parts',
          vehicleCompatibility: 'Enhancement failed',
          description: {
            generalInformation: 'Enhancement failed',
            technicalInformation: 'Enhancement failed',
            applicability: 'Enhancement failed',
            originalNumbers: 'Enhancement failed',
            oemNumbers: 'Enhancement failed'
          },
          confidence: 0,
          searchSources: []
        });
      }
    }
    
    return results;
  }

  /**
   * Create fallback data when AI enhancement fails
   */
  private createFallbackData(request: ProductEnhancementRequest): EnhancedProductData {
    return {
      productName: request.productName || 'Unknown Product',
      partArticleNumber: request.partNumber || '',
      manufacturer: request.manufacturer || 'Unknown',
      category: request.category || 'other-automotive-parts',
      stockQuantity: request.stockQuantity || 0,
      retailPrice: request.price || 0,
      vehicleCompatibility: 'Universal Application - Please verify compatibility',
      description: {
        generalInformation: `${request.productName || 'Automotive Part'} - ${request.partNumber || 'N/A'}. This product requires manual verification of specifications and compatibility.`,
        technicalInformation: 'Technical specifications to be determined. Please consult manufacturer documentation.',
        applicability: 'Vehicle compatibility to be verified. Please check with manufacturer or parts catalog.',
        originalNumbers: request.partNumber || 'N/A',
        oemNumbers: 'OEM numbers to be researched. Please consult manufacturer cross-reference.'
      },
      confidence: 25,
      searchSources: []
    };
  }
}

export const aiWebSearchService = new AIWebSearchService();

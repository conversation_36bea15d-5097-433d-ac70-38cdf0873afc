/**
 * TecDoc API Service - RapidAPI Integration
 * 
 * This service integrates with TecDoc catalogue API via RapidAPI to fetch
 * product specifications using Part Article Numbers for the bulk import feature.
 */

// TecDoc API configuration from RapidAPI (Based on user's screenshots)
const RAPIDAPI_KEY = import.meta.env.VITE_RAPIDAPI_KEY || '**************************************************';
const TECDOC_API_HOST = 'ronhartman-tecdoc-catalog.p.rapidapi.com';
const TECDOC_API_BASE_URL = 'https://ronhartman-tecdoc-catalog.p.rapidapi.com';

// TecDoc API default parameters
const DEFAULT_LANG_ID = 4; // English
const DEFAULT_COUNTRY_FILTER_ID = 62; // Germany
const DEFAULT_TYPE_ID = 1; // Automobile

// Rate limiting configuration for FREE plan
const FREE_PLAN_LIMITS = {
  monthlyLimit: 100,
  hourlyLimit: 1000,
  planName: 'FREE'
};

// Types for TecDoc API responses
export interface TecDocProduct {
  articleNumber: string;
  brandName?: string;
  productName?: string;
  description?: string;
  specifications?: {
    [key: string]: string | number;
  };
  vehicleCompatibility?: TecDocVehicle[];
  images?: string[];
  price?: number;
  availability?: string;
}

export interface TecDocVehicle {
  make: string;
  model: string;
  year?: number;
  engine?: string;
  fuelType?: string;
  displacement?: string;
}

export interface TecDocSearchResult {
  success: boolean;
  data?: TecDocProduct;
  error?: string;
  source: 'tecdoc' | 'fallback';
  statusCode?: number;
  statusMessage?: string;
  notFound?: boolean;
  rateLimited?: boolean;
  userMessage?: string;
}

// Enhanced error types for better user feedback
enum TecDocErrorType {
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMITED = 'RATE_LIMITED',
  AUTH_FAILED = 'AUTH_FAILED',
  SERVICE_ERROR = 'SERVICE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR'
}

interface TecDocError {
  type: TecDocErrorType;
  message: string;
  userMessage: string;
  statusCode?: number;
  retryAfter?: number;
}

// Helper function to create user-friendly error messages
function createTecDocError(statusCode: number, statusText: string, articleNumber: string): TecDocError {
  switch (statusCode) {
    case 404:
      return {
        type: TecDocErrorType.NOT_FOUND,
        message: `TecDoc: Article ${articleNumber} not found (404)`,
        userMessage: `No TecDoc Data Found - Part number "${articleNumber}" not in TecDoc database`,
        statusCode: 404
      };
    case 401:
      return {
        type: TecDocErrorType.AUTH_FAILED,
        message: `TecDoc: Authentication failed (401) - Check API key`,
        userMessage: `TecDoc: Authentication failed - check API key`,
        statusCode: 401
      };
    case 429:
      return {
        type: TecDocErrorType.RATE_LIMITED,
        message: `TecDoc: Monthly rate limit exceeded (${FREE_PLAN_LIMITS.monthlyLimit}/month)`,
        userMessage: `TecDoc: Monthly rate limit exceeded (${FREE_PLAN_LIMITS.monthlyLimit}/month)`,
        statusCode: 429
      };
    case 500:
    case 502:
    case 503:
    case 504:
      return {
        type: TecDocErrorType.SERVICE_ERROR,
        message: `TecDoc: Service temporarily unavailable (${statusCode})`,
        userMessage: `TecDoc: Service temporarily unavailable`,
        statusCode: statusCode
      };
    default:
      return {
        type: TecDocErrorType.NETWORK_ERROR,
        message: `TecDoc: HTTP ${statusCode} - ${statusText}`,
        userMessage: `TecDoc: Request failed (${statusCode})`,
        statusCode: statusCode
      };
  }
}

/**
 * Search for product information using Part Article Number
 */
export async function searchByArticleNumber(articleNumber: string): Promise<TecDocSearchResult> {
  if (!articleNumber || articleNumber.trim().length === 0) {
    console.log(`❌ TecDoc: Empty article number provided`);
    return {
      success: false,
      error: 'Article number is required',
      source: 'tecdoc',
      userMessage: 'No article number provided'
    };
  }

  const trimmedArticleNumber = articleNumber.trim();

  try {
    console.log(`🔍 TecDoc API: Starting search for article: ${trimmedArticleNumber}`);
    console.log(`📊 TecDoc API: Using FREE plan (${FREE_PLAN_LIMITS.monthlyLimit} requests/month limit)`);

    // First attempt: TecDoc API via RapidAPI
    const tecdocResult = await searchTecDocAPI(trimmedArticleNumber);

    // Handle successful data retrieval
    if (tecdocResult.success && tecdocResult.data) {
      console.log(`✅ TecDoc: Successfully retrieved data for ${trimmedArticleNumber}`);
      return tecdocResult;
    }

    // Handle specific error cases with clear messaging
    if (tecdocResult.notFound) {
      console.log(`📭 TecDoc: Part number "${trimmedArticleNumber}" not found in database`);
      return {
        success: false,
        error: `No TecDoc Data Found - Part number "${trimmedArticleNumber}" not in TecDoc database`,
        source: 'tecdoc',
        statusCode: 404,
        statusMessage: 'Not Found',
        notFound: true,
        userMessage: `No TecDoc Data Found - Part number "${trimmedArticleNumber}" not in TecDoc database`
      };
    }

    if (tecdocResult.rateLimited) {
      console.log(`⏰ TecDoc: Monthly rate limit exceeded (${FREE_PLAN_LIMITS.monthlyLimit}/month)`);
      return {
        success: false,
        error: `TecDoc: Monthly rate limit exceeded (${FREE_PLAN_LIMITS.monthlyLimit}/month)`,
        source: 'tecdoc',
        statusCode: 429,
        statusMessage: 'Rate Limited',
        rateLimited: true,
        userMessage: `TecDoc: Monthly rate limit exceeded (${FREE_PLAN_LIMITS.monthlyLimit}/month)`
      };
    }

    // Handle other API errors
    if (tecdocResult.error) {
      console.log(`❌ TecDoc: API error - ${tecdocResult.error}`);
      return {
        success: false,
        error: tecdocResult.error,
        source: 'tecdoc',
        statusCode: tecdocResult.statusCode,
        statusMessage: tecdocResult.statusMessage,
        userMessage: tecdocResult.userMessage || tecdocResult.error
      };
    }

    // Fallback: No data found but no specific error
    console.log(`📭 TecDoc: No data found for ${trimmedArticleNumber} (empty response)`);
    return {
      success: false,
      error: `No TecDoc Data Found - Part number "${trimmedArticleNumber}" returned empty response`,
      source: 'tecdoc',
      notFound: true,
      userMessage: `No TecDoc Data Found - Part number "${trimmedArticleNumber}" returned empty response`
    };

  } catch (error) {
    console.error(`💥 TecDoc: Unexpected error for ${trimmedArticleNumber}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      source: 'tecdoc',
      userMessage: 'TecDoc: Unexpected error occurred'
    };
  }
}

/**
 * Search TecDoc API via RapidAPI using enhanced error handling
 */
async function searchTecDocAPI(articleNumber: string): Promise<TecDocSearchResult> {
  try {
    console.log(`🔍 TecDoc API: Starting search for article: ${articleNumber}`);
    console.log(`🔑 TecDoc API: Using host: ${TECDOC_API_HOST}`);

    // Try multiple TecDoc API endpoints based on RapidAPI documentation
    let response: Response | null = null;
    let lastError: TecDocError | null = null;

    // Endpoint 1: Search by Article Number (Primary endpoint)
    try {
      const endpoint1 = `${TECDOC_API_BASE_URL}/articles/search/lang-id/${DEFAULT_LANG_ID}/article-search/${encodeURIComponent(articleNumber)}`;
      console.log(`🔍 TecDoc: Trying search by article number endpoint: ${endpoint1}`);

      response = await fetch(endpoint1, {
        method: 'GET',
        headers: {
          'X-RapidAPI-Key': RAPIDAPI_KEY,
          'X-RapidAPI-Host': TECDOC_API_HOST,
          'Content-Type': 'application/json'
        }
      });

      // Handle specific HTTP status codes
      if (response.ok) {
        console.log(`✅ TecDoc: Search by article number succeeded (${response.status})`);
      } else {
        lastError = createTecDocError(response.status, response.statusText, articleNumber);
        console.log(`❌ TecDoc: ${lastError.message}`);

        // For specific errors, return immediately instead of trying other endpoints
        if (response.status === 401 || response.status === 429) {
          return {
            success: false,
            error: lastError.message,
            source: 'tecdoc',
            statusCode: response.status,
            statusMessage: response.statusText,
            rateLimited: response.status === 429,
            userMessage: lastError.userMessage
          };
        }

        response = null;
      }
    } catch (error) {
      const networkError = createTecDocError(0, 'Network Error', articleNumber);
      console.log(`❌ TecDoc: Network error - ${error instanceof Error ? error.message : 'Unknown error'}`);
      lastError = networkError;
      response = null;
    }

    // Endpoint 2: Quick Article Search (POST method) - Only try if first endpoint didn't fail with auth/rate limit
    if (!response && lastError && lastError.type !== TecDocErrorType.AUTH_FAILED && lastError.type !== TecDocErrorType.RATE_LIMITED) {
      try {
        const endpoint2 = `${TECDOC_API_BASE_URL}/articles/quick-article-search`;
        console.log(`🔍 TecDoc: Trying quick article search (POST) endpoint: ${endpoint2}`);

        response = await fetch(endpoint2, {
          method: 'POST',
          headers: {
            'X-RapidAPI-Key': RAPIDAPI_KEY,
            'X-RapidAPI-Host': TECDOC_API_HOST,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            articleNumber: articleNumber,
            langId: DEFAULT_LANG_ID
          })
        });

        if (response.ok) {
          console.log(`✅ TecDoc: Quick article search succeeded (${response.status})`);
        } else {
          lastError = createTecDocError(response.status, response.statusText, articleNumber);
          console.log(`❌ TecDoc: ${lastError.message}`);

          // For auth/rate limit errors, return immediately
          if (response.status === 401 || response.status === 429) {
            return {
              success: false,
              error: lastError.message,
              source: 'tecdoc',
              statusCode: response.status,
              statusMessage: response.statusText,
              rateLimited: response.status === 429,
              userMessage: lastError.userMessage
            };
          }

          response = null;
        }
      } catch (error) {
        console.log(`❌ TecDoc: Quick search network error - ${error instanceof Error ? error.message : 'Unknown error'}`);
        response = null;
      }
    }

    // Endpoint 3: Alternative search endpoint (fallback) - Only if no auth/rate limit issues
    if (!response && lastError && lastError.type !== TecDocErrorType.AUTH_FAILED && lastError.type !== TecDocErrorType.RATE_LIMITED) {
      try {
        const endpoint3 = `${TECDOC_API_BASE_URL}/search/articles/${encodeURIComponent(articleNumber)}`;
        console.log(`🔍 TecDoc: Trying alternative search endpoint: ${endpoint3}`);

        response = await fetch(endpoint3, {
          method: 'GET',
          headers: {
            'X-RapidAPI-Key': RAPIDAPI_KEY,
            'X-RapidAPI-Host': TECDOC_API_HOST,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          console.log(`✅ TecDoc: Alternative search succeeded (${response.status})`);
        } else {
          lastError = createTecDocError(response.status, response.statusText, articleNumber);
          console.log(`❌ TecDoc: ${lastError.message}`);
          response = null;
        }
      } catch (error) {
        console.log(`❌ TecDoc: Alternative search network error - ${error instanceof Error ? error.message : 'Unknown error'}`);
        response = null;
      }
    }

    // Handle case where all endpoints failed
    if (!response) {
      if (lastError) {
        console.log(`❌ TecDoc: All endpoints failed - ${lastError.message}`);
        return {
          success: false,
          error: lastError.message,
          source: 'tecdoc',
          statusCode: lastError.statusCode,
          statusMessage: lastError.statusCode === 404 ? 'Not Found' : 'Error',
          notFound: lastError.type === TecDocErrorType.NOT_FOUND,
          rateLimited: lastError.type === TecDocErrorType.RATE_LIMITED,
          userMessage: lastError.userMessage
        };
      } else {
        console.log(`❌ TecDoc: All endpoints failed with unknown error`);
        return {
          success: false,
          error: 'All TecDoc endpoints failed',
          source: 'tecdoc',
          userMessage: 'TecDoc: Service unavailable'
        };
      }
    }

    console.log(`📥 TecDoc API Response: Status ${response.status} (${response.statusText})`);

    // Handle non-OK responses (this shouldn't happen due to earlier checks, but safety net)
    if (!response.ok) {
      const error = createTecDocError(response.status, response.statusText, articleNumber);
      console.log(`❌ TecDoc: Response not OK - ${error.message}`);
      return {
        success: false,
        error: error.message,
        source: 'tecdoc',
        statusCode: response.status,
        statusMessage: response.statusText,
        notFound: response.status === 404,
        rateLimited: response.status === 429,
        userMessage: error.userMessage
      };
    }

    // Parse response data
    let data;
    try {
      data = await response.json();
    } catch (parseError) {
      console.log(`❌ TecDoc: Failed to parse JSON response for ${articleNumber}`);
      return {
        success: false,
        error: 'TecDoc: Invalid JSON response',
        source: 'tecdoc',
        userMessage: 'TecDoc: Invalid response format'
      };
    }

    console.log(`📥 TecDoc: Successfully retrieved response for ${articleNumber}:`, {
      hasData: !!data,
      dataKeys: Object.keys(data || {}),
      dataSize: JSON.stringify(data || {}).length
    });

    // Check if response contains actual product data
    if (!data || (Array.isArray(data) && data.length === 0) || (typeof data === 'object' && Object.keys(data).length === 0)) {
      console.log(`📭 TecDoc: Empty response for ${articleNumber} - part not found in database`);
      return {
        success: false,
        error: `No TecDoc Data Found - Part number "${articleNumber}" not in TecDoc database`,
        source: 'tecdoc',
        statusCode: 200,
        statusMessage: 'OK',
        notFound: true,
        userMessage: `No TecDoc Data Found - Part number "${articleNumber}" not in TecDoc database`
      };
    }

    // Transform TecDoc response to our format
    const transformedData = transformTecDocResponse(data, articleNumber);
    console.log(`🔄 TecDoc: Transformed data:`, {
      articleNumber: transformedData.articleNumber,
      brandName: transformedData.brandName,
      productName: transformedData.productName,
      hasImages: (transformedData.images?.length || 0) > 0,
      hasSpecs: Object.keys(transformedData.specifications || {}).length > 0
    });

    // Verify that transformed data contains meaningful information
    if (!transformedData.brandName && !transformedData.productName && !transformedData.description) {
      console.log(`📭 TecDoc: No meaningful data found for ${articleNumber} after transformation`);
      return {
        success: false,
        error: `No TecDoc Data Found - Part number "${articleNumber}" returned insufficient data`,
        source: 'tecdoc',
        statusCode: 200,
        statusMessage: 'OK',
        notFound: true,
        userMessage: `No TecDoc Data Found - Part number "${articleNumber}" returned insufficient data`
      };
    }

    console.log(`✅ TecDoc: Successfully processed data for ${articleNumber}`);
    return {
      success: true,
      data: transformedData,
      source: 'tecdoc',
      statusCode: 200,
      statusMessage: 'OK'
    };

  } catch (error) {
    console.error(`💥 TecDoc API: Unexpected error for ${articleNumber}:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    // Determine if this is a network error or other type
    const isNetworkError = error instanceof Error && (
      error.message.includes('fetch') ||
      error.message.includes('network') ||
      error.message.includes('ECONNREFUSED') ||
      error.message.includes('timeout')
    );

    return {
      success: false,
      error: error instanceof Error ? error.message : 'TecDoc API request failed',
      source: 'tecdoc',
      userMessage: isNetworkError
        ? 'TecDoc: Network connection failed'
        : 'TecDoc: Unexpected error occurred'
    };
  }
}

/**
 * Transform TecDoc API response to our product format
 */
function transformTecDocResponse(apiResponse: any, articleNumber: string): TecDocProduct {
  // This transformation will depend on the actual TecDoc API response structure
  // For now, we'll create a basic structure
  
  return {
    articleNumber: articleNumber,
    brandName: apiResponse.brand || '',
    productName: apiResponse.name || apiResponse.title || '',
    description: apiResponse.description || '',
    specifications: apiResponse.specifications || {},
    vehicleCompatibility: transformVehicleCompatibility(apiResponse.vehicles || []),
    images: apiResponse.images || [],
    price: apiResponse.price || undefined,
    availability: apiResponse.availability || 'unknown'
  };
}

/**
 * Transform vehicle compatibility data
 */
function transformVehicleCompatibility(vehicles: any[]): TecDocVehicle[] {
  return vehicles.map((vehicle: any) => ({
    make: vehicle.make || vehicle.brand || '',
    model: vehicle.model || '',
    year: vehicle.year || undefined,
    engine: vehicle.engine || vehicle.engineCode || '',
    fuelType: vehicle.fuelType || '',
    displacement: vehicle.displacement || ''
  }));
}

/**
 * Batch search for multiple article numbers
 */
export async function batchSearchByArticleNumbers(
  articleNumbers: string[],
  onProgress?: (completed: number, total: number) => void
): Promise<Map<string, TecDocSearchResult>> {
  const results = new Map<string, TecDocSearchResult>();
  const total = articleNumbers.length;
  
  console.log(`🔍 Starting batch search for ${total} article numbers`);

  for (let i = 0; i < articleNumbers.length; i++) {
    const articleNumber = articleNumbers[i];
    
    try {
      const result = await searchByArticleNumber(articleNumber);
      results.set(articleNumber, result);
      
      // Report progress
      if (onProgress) {
        onProgress(i + 1, total);
      }
      
      // Add small delay to avoid rate limiting
      if (i < articleNumbers.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
    } catch (error) {
      console.error(`Error searching for ${articleNumber}:`, error);
      results.set(articleNumber, {
        success: false,
        error: error instanceof Error ? error.message : 'Search failed',
        source: 'tecdoc'
      });
    }
  }

  console.log(`✅ Batch search completed: ${results.size} results`);
  return results;
}

/**
 * Validate article number format
 */
export function validateArticleNumber(articleNumber: string): boolean {
  if (!articleNumber || typeof articleNumber !== 'string') {
    return false;
  }
  
  const trimmed = articleNumber.trim();
  
  // Basic validation: should be alphanumeric with possible hyphens/dots
  const validPattern = /^[A-Za-z0-9\-\.]+$/;
  return validPattern.test(trimmed) && trimmed.length >= 3 && trimmed.length <= 50;
}

/**
 * Extract article numbers from imported data
 */
export function extractArticleNumbers(importedData: any[]): string[] {
  const articleNumbers: string[] = [];
  
  for (const item of importedData) {
    // Look for common column names that might contain article numbers
    const possibleFields = [
      'partArticleNumber',
      'part_article_number',
      'articleNumber',
      'article_number',
      'partNumber',
      'part_number',
      'sku',
      'SKU',
      'productCode',
      'product_code'
    ];
    
    for (const field of possibleFields) {
      const value = item[field];
      if (value && validateArticleNumber(value)) {
        articleNumbers.push(value.toString().trim());
        break; // Use first valid article number found
      }
    }
  }
  
  // Remove duplicates
  return [...new Set(articleNumbers)];
}

/**
 * Health check for TecDoc API
 */
export async function healthCheck(): Promise<{ available: boolean; error?: string }> {
  try {
    if (!RAPIDAPI_KEY) {
      return { available: false, error: 'RapidAPI key not configured' };
    }
    
    // Simple test request (this would need to be adjusted based on actual API)
    return { available: true };
    
  } catch (error) {
    return { 
      available: false, 
      error: error instanceof Error ? error.message : 'Health check failed' 
    };
  }
}

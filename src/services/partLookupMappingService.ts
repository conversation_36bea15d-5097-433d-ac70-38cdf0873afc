/**
 * Part Lookup Mapping Service
 * 
 * Maps part lookup results to the application's product data structure
 * Converts PartLookupResult to TyreProduct/BrakeProduct format
 */

import { TyreProduct, BrakeProduct, BaseProduct } from '@/features/products/types/product.types';
import { PartLookupResult } from './partLookupService';

// Import the OutputShape type from the service
type OutputShape = {
  productName: string;
  sku: string;
  partArticleNumber: string;
  manufacturer: string;
  brandOrManufacturer: string;
  category: string;
  subcategory: string;
  description: {
    generalInformation: string;
    technicalInformation: string;
    applicability: string;
    originalNumbers: string;
    oemNumbers: string;
  };
  vehicleCompatibility: string;
  primaryImageUrl: string | null;
  primaryImageSourcePage: string | null;
  primaryImageVerified: boolean;
  stockQuantity: number;
  retailPrice: number;
  confidence: number;
  searchSources: string[];
};

/**
 * Category mapping from part lookup categories to application categories
 */
const CATEGORY_MAPPING: Record<string, string> = {
  "Tyres & Related Products": "tyres",
  "Brake Parts & Systems": "brakes",
  "Filters": "filters",
  "Oils & Fluids": "oils-fluids",
  "Engine": "engine",
  "Window Cleaning": "window-cleaning",
  "Glow Plug & Ignition": "glow-plug-ignition",
  "Wishbones & Suspension": "wishbones-suspension",
  "Electrical Systems": "electrical-systems",
  "Damping": "damping",
  "Exhaust Gas Recirculation": "exhaust-gas-recirculation",
  "Gaskets Sealing Rings": "gaskets-sealing-rings",
  "Belts Chains Rollers": "belts-chains-rollers",
  "Forced Induction Components": "forced-induction-components",
  "Exhaust": "exhaust",
  "Engine Cooling System": "engine-cooling-system",
  "Interior": "interior",
  "Body": "body",
  "Fuel Supply System": "fuel-supply-system",
  "Steering": "steering",
  "Heating Ventilation": "heating-ventilation",
  "Clutch": "clutch",
  "Drive Shaft And Cv Joint": "drive-shaft-cv-joint",
  "Air Suspension": "air-suspension",
  "Towbar Parts": "towbar-parts",
  "Gearbox": "gearbox",
  "Air Conditioning": "air-conditioning",
  "Bearings": "bearings",
  "Propshafts And Differentials": "propshafts-differentials",
  "Sensors Relays Control Units": "sensors-relays-control-units",
  "Repair Kits": "repair-kits",
  "Lighting": "lighting",
  "Tuning": "tuning",
  "Fasteners": "fasteners",
};

/**
 * Generate a unique product ID based on category and part number
 */
function generateProductId(category: string, partNumber: string): string {
  const categoryPrefix = getCategoryPrefix(category);
  const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
  const partSuffix = partNumber.replace(/[^A-Za-z0-9]/g, '').slice(-4).toUpperCase();
  return `${categoryPrefix}-${timestamp}${partSuffix}`;
}

/**
 * Get category prefix for product ID generation
 */
function getCategoryPrefix(category: string): string {
  const prefixMap: Record<string, string> = {
    "tyres": "TYR",
    "brakes": "BRK",
    "filters": "FLT",
    "oils-fluids": "OIL",
    "engine": "ENG",
    default: "PROD"
  };
  return prefixMap[category] || prefixMap.default;
}

/**
 * Convert description object to a single description string
 */
function formatDescription(description: OutputShape['description']): string {
  const sections = [
    description.generalInformation && `General: ${description.generalInformation}`,
    description.technicalInformation && `Technical: ${description.technicalInformation}`,
    description.applicability && `Compatibility: ${description.applicability}`,
    description.originalNumbers && `Original Numbers: ${description.originalNumbers}`,
    description.oemNumbers && `OEM Numbers: ${description.oemNumbers}`,
  ].filter(Boolean);

  return sections.join('\n\n');
}

/**
 * Map OutputShape to BaseProduct structure with EXACT field mapping to table columns
 */
function mapToBaseProduct(
  lookupData: OutputShape,
  userId: string,
  supplierName?: string
): Omit<BaseProduct, 'id'> {
  const mappedCategory = CATEGORY_MAPPING[lookupData.category] || 'other';

  return {
    // EXACT table column mappings
    name: lookupData.productName,
    sku: lookupData.sku,
    partArticleNumber: lookupData.partArticleNumber,
    category: mappedCategory,
    subcategory: lookupData.subcategory,

    // CRITICAL: Map to BOTH fields since tables expect 'description' but BaseProduct has 'descriptionAndSpecifications'
    description: formatDescription(lookupData.description), // For table display
    descriptionAndSpecifications: formatDescription(lookupData.description), // For BaseProduct interface

    // Image fields - exact mapping
    primaryImage: lookupData.primaryImageUrl || undefined,
    additionalImages: [], // Empty array for manual addition

    // Manufacturer and supplier - exact mapping
    manufacturer: lookupData.manufacturer,
    supplierName: supplierName,
    supplierAccountId: userId,

    // Inventory fields - exact mapping
    stockQuantity: lookupData.stockQuantity,
    retailPrice: lookupData.retailPrice,

    // Business fields - defaults for manual completion
    wholesalePricingTiers: [], // Empty for manual entry
    minimumOrderQuantity: 1,
    quotationRequestEnabled: false,

    // Vehicle compatibility - exact mapping
    vehicleCompatibility: lookupData.vehicleCompatibility,

    // Shipping and admin fields - defaults for manual completion
    shippingOrigin: undefined, // Manual entry required
    inventoryUpdateDate: new Date(),
    status: 'draft' as const,

    // System fields
    adminNotes: `Imported via AI lookup. Confidence: ${lookupData.confidence}%. Sources: ${lookupData.searchSources.length}`,
    createdAt: new Date(),
    updatedAt: new Date(),
    marketplaceSection: 'retail' as const,

    // Legacy/optional fields
    certifications: [],
    estimatedLeadTime: undefined,
    availableShippingMethods: [],
    packagingDetails: undefined,
  };
}

/**
 * Map OutputShape to TyreProduct with ALL tyre-specific fields
 */
export function mapToTyreProduct(
  lookupData: OutputShape,
  userId: string,
  supplierName?: string
): Partial<TyreProduct> {
  const baseProduct = mapToBaseProduct(lookupData, userId, supplierName);
  const productId = generateProductId(baseProduct.category, lookupData.partArticleNumber);

  // Extract tyre specifications from AI data
  const tyreSpecs = extractTyreSpecifications(lookupData);

  return {
    ...baseProduct,
    id: productId,

    // EXACT tyre table column mappings
    ...tyreSpecs, // width, aspectRatio, rimDiameter, loadIndex, speedRating, season

    // Additional tyre-specific fields for manual completion
    treadLife: '', // Manual entry required
    tractionRating: '', // Manual entry required
    temperatureRating: '', // Manual entry required
  };
}

/**
 * Map OutputShape to BrakeProduct
 */
export function mapToBrakeProduct(
  lookupData: OutputShape,
  userId: string,
  supplierName?: string
): Partial<BrakeProduct> {
  const baseProduct = mapToBaseProduct(lookupData, userId, supplierName);
  const productId = generateProductId(baseProduct.category, lookupData.partArticleNumber);

  return {
    ...baseProduct,
    id: productId,
  };
}

/**
 * Map PartLookupResult to appropriate product type based on category
 * This function handles the PartLookupResult wrapper and extracts the data
 */
export function mapToProduct(
  lookupResult: PartLookupResult,
  userId: string,
  supplierName?: string
): Partial<TyreProduct | BrakeProduct> {
  // Check if the lookup was successful and has data
  if (!lookupResult.success || !lookupResult.data) {
    throw new Error('Invalid lookup result: ' + (lookupResult.error || 'No data available'));
  }

  const lookupData = lookupResult.data;
  const mappedCategory = CATEGORY_MAPPING[lookupData.category];

  if (mappedCategory === 'tyres') {
    return mapToTyreProduct(lookupData, userId, supplierName);
  } else if (mappedCategory === 'brakes') {
    return mapToBrakeProduct(lookupData, userId, supplierName);
  } else {
    // For other categories, use BrakeProduct as the base (it's essentially BaseProduct)
    return mapToBrakeProduct(lookupData, userId, supplierName);
  }
}

/**
 * Extract tyre specifications from lookup data
 */
function extractTyreSpecifications(lookupData: OutputShape): Partial<Pick<TyreProduct, 'width' | 'aspectRatio' | 'rimDiameter' | 'loadIndex' | 'speedRating' | 'season'>> {
  const specs: Partial<Pick<TyreProduct, 'width' | 'aspectRatio' | 'rimDiameter' | 'loadIndex' | 'speedRating' | 'season'>> = {};

  // Try to extract from product name (e.g., "205/55R16 91V")
  const tyrePattern = /(\d{3})\/(\d{2})R(\d{2})\s*(\d{2,3})([A-Z])/i;
  const match = lookupData.productName.match(tyrePattern);

  if (match) {
    specs.width = parseInt(match[1]);
    specs.aspectRatio = parseInt(match[2]);
    specs.rimDiameter = parseInt(match[3]);
    specs.loadIndex = parseInt(match[4]);
    specs.speedRating = match[5].toUpperCase();
  }

  // Try to extract season from product name or description
  const seasonPattern = /(summer|winter|all-season|all season)/i;
  const seasonMatch = (lookupData.productName + ' ' + lookupData.description.generalInformation).match(seasonPattern);

  if (seasonMatch) {
    const seasonText = seasonMatch[1].toLowerCase();
    if (seasonText.includes('summer')) {
      specs.season = 'Summer';
    } else if (seasonText.includes('winter')) {
      specs.season = 'Winter';
    } else if (seasonText.includes('all')) {
      specs.season = 'All-Season';
    }
  }

  return specs;
}

/**
 * Validate that the lookup result can be mapped to a product
 */
export function validateLookupResult(lookupResult: PartLookupResult): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // First check if the lookup was successful
  if (!lookupResult.success) {
    errors.push(lookupResult.error || 'Lookup failed');
    return { valid: false, errors };
  }

  // Check if data exists
  if (!lookupResult.data) {
    errors.push('No lookup data available');
    return { valid: false, errors };
  }

  const data = lookupResult.data;

  if (!data.productName || data.productName.trim() === '') {
    errors.push('Product name is required');
  }

  if (!data.partArticleNumber || data.partArticleNumber.trim() === '') {
    errors.push('Part article number is required');
  }

  if (!data.manufacturer || data.manufacturer.trim() === '') {
    errors.push('Manufacturer is required');
  }

  if (!CATEGORY_MAPPING[data.category]) {
    errors.push(`Unknown category: ${data.category}`);
  }

  if (data.confidence < 25) {
    errors.push('Confidence level too low for reliable import');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

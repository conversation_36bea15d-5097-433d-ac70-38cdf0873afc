/**
 * Image Proxy Service
 * 
 * Helper service to fetch and verify images from external sources
 * Adapted from part-lookup-playground fetch-image functionality
 */

export interface ImageFetchResult {
  success: boolean;
  blob?: Blob;
  contentType?: string;
  error?: string;
}

/**
 * Fetch an image from a URL with proper error handling
 * This replaces the /api/fetch-image route functionality
 */
export async function fetchImageFromUrl(url: string): Promise<ImageFetchResult> {
  try {
    // Validate URL
    if (!url || !url.startsWith("https://")) {
      return {
        success: false,
        error: "Missing or invalid URL. Only HTTPS URLs are allowed."
      };
    }

    // Fetch the image
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "User-Agent": "Mozilla/5.0 (compatible; PartLookupBot/1.2; +https://example.com)",
        Accept: "image/*",
      },
    });

    if (!response.ok) {
      return {
        success: false,
        error: `Failed to fetch image: ${response.status} ${response.statusText}`
      };
    }

    // Get content type
    const contentType = response.headers.get("content-type") || "application/octet-stream";
    
    // Verify it's actually an image
    if (!contentType.startsWith("image/")) {
      return {
        success: false,
        error: `Invalid content type: ${contentType}. Expected image/*`
      };
    }

    // Get the blob
    const blob = await response.blob();

    return {
      success: true,
      blob,
      contentType
    };

  } catch (error) {
    console.error("Error fetching image:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

/**
 * Download an image as a file
 * This creates a download link for the user
 */
export function downloadImageBlob(blob: Blob, filename: string = "part-image"): void {
  try {
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error downloading image:", error);
    throw new Error("Failed to download image");
  }
}

/**
 * Verify if an image URL is accessible and returns valid image content
 */
export async function verifyImageUrl(url: string): Promise<boolean> {
  try {
    if (!url || !url.startsWith("https://")) {
      return false;
    }

    // Try HEAD request first (faster)
    const headResponse = await fetch(url, { 
      method: "HEAD",
      headers: {
        "User-Agent": "Mozilla/5.0 (compatible; PartLookupBot/1.2; +https://example.com)",
      },
    });

    if (headResponse.ok) {
      const contentType = headResponse.headers.get("content-type");
      return contentType ? contentType.startsWith("image/") : false;
    }

    // If HEAD fails, try GET with range request
    const getResponse = await fetch(url, { 
      method: "GET", 
      headers: { 
        Range: "bytes=0-0",
        "User-Agent": "Mozilla/5.0 (compatible; PartLookupBot/1.2; +https://example.com)",
      } 
    });

    if (getResponse.ok) {
      const contentType = getResponse.headers.get("content-type");
      return contentType ? contentType.startsWith("image/") : false;
    }

    return false;
  } catch {
    return false;
  }
}

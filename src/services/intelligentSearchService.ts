/**
 * Intelligent Product Search Service
 * 
 * Provides comprehensive search functionality for the marketplace with:
 * - 8 search pattern types from UI mockup
 * - Fuzzy matching and typo tolerance
 * - Multilingual support (French/Arabic)
 * - Real-time suggestions
 * - Performance optimization with caching
 */

import { createClient } from '@supabase/supabase-js';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { searchAnalyticsService } from './searchAnalyticsService';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export type AnyProduct = TyreProduct | BrakeProduct;

// Search pattern types from UI mockup
export type SearchPatternType = 
  | 'car_part'                    // "Wiper blades"
  | 'car_part_manufacturer'       // "Wiper blades CHAMPION"
  | 'car_part_vehicle_brand'      // "Wiper blades LEXUS"
  | 'car_part_item_number'        // "Wiper blades AHR55/B01"
  | 'item_number'                 // "39-8550"
  | 'item_number_manufacturer'    // "39-8650 MAXGEAR"
  | 'oem_number'                  // "8521271010"
  | 'oem_manufacturer';           // "8522248160 MAXGEAR"

export interface SearchQuery {
  text: string;
  pattern: SearchPatternType;
  confidence: number;
  extractedTerms: {
    partName?: string;
    manufacturer?: string;
    vehicleBrand?: string;
    itemNumber?: string;
    oemNumber?: string;
  };
}

export interface SearchResult {
  products: AnyProduct[];
  totalCount: number;
  searchTime: number;
  suggestions: string[];
  appliedFilters: SearchFilters;
}

export interface SearchFilters {
  category?: string;
  subcategory?: string;
  manufacturer?: string;
  priceRange?: { min: number; max: number };
  inStock?: boolean;
  vehicleCompatible?: boolean;
}

export interface SearchSuggestion {
  text: string;
  type: 'product' | 'manufacturer' | 'category' | 'part_number';
  count: number;
}

// French automotive terminology mapping
const FRENCH_AUTOMOTIVE_TERMS: Record<string, string[]> = {
  'plaquettes de frein': ['brake pads', 'brake pad', 'plaquette frein'],
  'disques de frein': ['brake discs', 'brake disc', 'disque frein'],
  'amortisseurs': ['shock absorbers', 'shocks', 'amortisseur'],
  'pneus': ['tyres', 'tires', 'pneu'],
  'filtres': ['filters', 'filter', 'filtre'],
  'huile moteur': ['engine oil', 'motor oil', 'huile'],
  'bougies': ['spark plugs', 'spark plug', 'bougie'],
  'courroies': ['belts', 'belt', 'courroie'],
  'roulements': ['bearings', 'bearing', 'roulement'],
  'essuie-glaces': ['wiper blades', 'wipers', 'essuie glace']
};

// Arabic automotive terminology mapping
const ARABIC_AUTOMOTIVE_TERMS: Record<string, string[]> = {
  'فرامل': ['brake', 'brakes', 'brake pads', 'brake disc'],
  'إطارات': ['tyres', 'tires', 'tyre', 'tire'],
  'زيت': ['oil', 'engine oil', 'motor oil'],
  'فلتر': ['filter', 'filters', 'air filter', 'oil filter'],
  'محرك': ['engine', 'motor'],
  'بطارية': ['battery'],
  'مصابيح': ['lights', 'headlights', 'bulbs'],
  'ماسحات': ['wipers', 'wiper blades'],
  'حزام': ['belt', 'timing belt'],
  'شمعات': ['spark plugs', 'plugs'],
  'رادياتير': ['radiator'],
  'كلتش': ['clutch'],
  'جنوط': ['wheels', 'rims'],
  'عادم': ['exhaust'],
  'تكييف': ['air conditioning', 'ac'],
  'فرش': ['brake pads'],
  'ديسك': ['brake disc', 'disc'],
  'امتصاص': ['shock absorber', 'shocks'],
  'توجيه': ['steering'],
  'تعليق': ['suspension']
};

class IntelligentSearchService {
  private searchCache = new Map<string, SearchResult>();
  private suggestionCache = new Map<string, SearchSuggestion[]>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Main search method - analyzes query and returns results
   */
  async search(
    query: string, 
    filters: SearchFilters = {},
    limit: number = 50,
    offset: number = 0
  ): Promise<SearchResult> {
    const startTime = Date.now();
    
    // Check cache first
    const cacheKey = this.generateCacheKey(query, filters, limit, offset);
    const cached = this.searchCache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      return cached;
    }

    try {
      // 1. Analyze search query to determine pattern and extract terms
      const searchQuery = this.analyzeSearchQuery(query);
      
      // 2. Build optimized database query based on pattern
      const dbQuery = this.buildDatabaseQuery(searchQuery, filters, limit, offset);
      
      // 3. Execute search with performance monitoring
      const { data: products, count } = await dbQuery;

      // Debug logging
      console.log('🔍 Search Debug:', {
        query: query,
        pattern: searchQuery.pattern,
        extractedTerms: searchQuery.extractedTerms,
        rawProductsCount: products?.length || 0,
        totalCount: count || 0
      });

      // 4. Transform and rank results
      const transformedProducts = this.transformAndRankResults(products || [], searchQuery);
      
      // 5. Generate search suggestions
      const suggestions = await this.generateSuggestions(query, searchQuery);
      
      const searchTime = Date.now() - startTime;

      const result: SearchResult = {
        products: transformedProducts,
        totalCount: count || 0,
        searchTime,
        suggestions,
        appliedFilters: filters
      };

      // Track search analytics
      await searchAnalyticsService.trackSearch({
        query,
        search_type: 'text',
        results_count: count || 0,
        search_time_ms: searchTime,
        filters_applied: filters,
        language: this.getCurrentLanguage()
      });

      // Cache the result
      this.searchCache.set(cacheKey, result);

      return result;
      
    } catch (error) {
      console.error('Search error:', error);
      return {
        products: [],
        totalCount: 0,
        searchTime: Date.now() - startTime,
        suggestions: [],
        appliedFilters: filters
      };
    }
  }

  /**
   * Get real-time search suggestions as user types
   */
  async getSuggestions(query: string, limit: number = 10): Promise<SearchSuggestion[]> {
    if (query.length < 2) return [];
    
    const cacheKey = `suggestions_${query}_${limit}`;
    const cached = this.suggestionCache.get(cacheKey);
    if (cached) return cached;

    try {
      const suggestions: SearchSuggestion[] = [];
      
      // Get product name suggestions
      const { data: productSuggestions } = await supabase
        .from('products')
        .select('name, manufacturer, category')
        .or(`name.ilike.%${query}%,manufacturer.ilike.%${query}%`)
        .eq('status', 'active')
        .limit(limit);

      if (productSuggestions) {
        // Add unique product names
        const uniqueNames = new Set<string>();
        productSuggestions.forEach(p => {
          if (p.name && !uniqueNames.has(p.name.toLowerCase())) {
            uniqueNames.add(p.name.toLowerCase());
            suggestions.push({
              text: p.name,
              type: 'product',
              count: 1
            });
          }
        });

        // Add unique manufacturers
        const uniqueManufacturers = new Set<string>();
        productSuggestions.forEach(p => {
          if (p.manufacturer && !uniqueManufacturers.has(p.manufacturer.toLowerCase())) {
            uniqueManufacturers.add(p.manufacturer.toLowerCase());
            suggestions.push({
              text: p.manufacturer,
              type: 'manufacturer',
              count: 1
            });
          }
        });
      }

      // Cache suggestions
      this.suggestionCache.set(cacheKey, suggestions);
      
      return suggestions.slice(0, limit);
      
    } catch (error) {
      console.error('Suggestions error:', error);
      return [];
    }
  }

  /**
   * Analyze search query to determine pattern and extract terms
   */
  private analyzeSearchQuery(query: string): SearchQuery {
    const normalizedQuery = this.normalizeQuery(query);
    
    // Check for OEM numbers (typically 8-12 digits)
    const oemPattern = /^\d{8,12}$/;
    if (oemPattern.test(normalizedQuery.replace(/\s+/g, ''))) {
      return {
        text: query,
        pattern: 'oem_number',
        confidence: 0.9,
        extractedTerms: { oemNumber: normalizedQuery }
      };
    }

    // Check for item numbers (alphanumeric with dashes/slashes)
    const itemNumberPattern = /^[A-Z0-9\-\/]{3,20}$/i;
    if (itemNumberPattern.test(normalizedQuery.replace(/\s+/g, ''))) {
      return {
        text: query,
        pattern: 'item_number',
        confidence: 0.85,
        extractedTerms: { itemNumber: normalizedQuery }
      };
    }

    // Check for manufacturer + item number
    const manufacturerItemPattern = /^(.+?)\s+([A-Z0-9\-\/]{3,20})$/i;
    const manufacturerItemMatch = normalizedQuery.match(manufacturerItemPattern);
    if (manufacturerItemMatch) {
      return {
        text: query,
        pattern: 'item_number_manufacturer',
        confidence: 0.8,
        extractedTerms: {
          manufacturer: manufacturerItemMatch[1].trim(),
          itemNumber: manufacturerItemMatch[2].trim()
        }
      };
    }

    // Check for car part + manufacturer
    const partManufacturerPattern = /^(.+?)\s+(CHAMPION|MAXGEAR|BOSCH|VALEO|CONTINENTAL|MICHELIN|BRIDGESTONE)$/i;
    const partManufacturerMatch = normalizedQuery.match(partManufacturerPattern);
    if (partManufacturerMatch) {
      return {
        text: query,
        pattern: 'car_part_manufacturer',
        confidence: 0.75,
        extractedTerms: {
          partName: partManufacturerMatch[1].trim(),
          manufacturer: partManufacturerMatch[2].trim()
        }
      };
    }

    // Check for car part + vehicle brand
    const partVehiclePattern = /^(.+?)\s+(LEXUS|TOYOTA|RENAULT|PEUGEOT|CITROEN|BMW|MERCEDES|AUDI|VOLKSWAGEN)$/i;
    const partVehicleMatch = normalizedQuery.match(partVehiclePattern);
    if (partVehicleMatch) {
      return {
        text: query,
        pattern: 'car_part_vehicle_brand',
        confidence: 0.7,
        extractedTerms: {
          partName: partVehicleMatch[1].trim(),
          vehicleBrand: partVehicleMatch[2].trim()
        }
      };
    }

    // Default to car part search
    return {
      text: query,
      pattern: 'car_part',
      confidence: 0.6,
      extractedTerms: { partName: normalizedQuery }
    };
  }

  /**
   * Normalize search query for better matching
   */
  private normalizeQuery(query: string): string {
    let normalized = query.trim().toLowerCase();

    // Detect if query contains Arabic text
    const hasArabic = /[\u0600-\u06FF]/.test(normalized);

    // Handle Arabic automotive terms
    if (hasArabic) {
      Object.entries(ARABIC_AUTOMOTIVE_TERMS).forEach(([arabic, english]) => {
        if (normalized.includes(arabic)) {
          // Replace Arabic term with English equivalents for database search
          english.forEach(eng => {
            normalized = normalized.replace(arabic, eng);
          });
        }
      });
    }

    // Handle French automotive terms
    Object.entries(FRENCH_AUTOMOTIVE_TERMS).forEach(([french, english]) => {
      english.forEach(eng => {
        if (normalized.includes(french)) {
          normalized = normalized.replace(french, eng);
        }
      });
    });

    // Remove diacritics and normalize Unicode
    normalized = normalized.normalize('NFD').replace(/[\u0300-\u036f]/g, '');

    // Handle common abbreviations and variations
    normalized = this.handleCommonAbbreviations(normalized);

    return normalized;
  }

  /**
   * Handle common automotive abbreviations and variations
   */
  private handleCommonAbbreviations(query: string): string {
    const abbreviations: Record<string, string> = {
      'ac': 'air conditioning',
      'abs': 'anti lock braking system',
      'esp': 'electronic stability program',
      'ecu': 'electronic control unit',
      'egr': 'exhaust gas recirculation',
      'dpf': 'diesel particulate filter',
      'cat': 'catalytic converter',
      'turbo': 'turbocharger',
      'cv': 'constant velocity',
      'ps': 'power steering',
      'atf': 'automatic transmission fluid'
    };

    let normalized = query;
    Object.entries(abbreviations).forEach(([abbr, full]) => {
      const regex = new RegExp(`\\b${abbr}\\b`, 'gi');
      normalized = normalized.replace(regex, full);
    });

    return normalized;
  }

  /**
   * Build optimized Supabase query based on search pattern
   */
  private buildDatabaseQuery(searchQuery: SearchQuery, filters: SearchFilters, limit: number, offset: number) {
    let query = supabase
      .from('products')
      .select(`
        id, name, description_and_specifications, manufacturer, category, subcategory,
        part_article_number, retail_price, stock_quantity, status, primary_image,
        additional_images, compatible_vehicles, certifications, created_at, supplier_name,
        wholesale_pricing_tiers:wholesale_pricing_tiers(min_quantity, max_quantity, price)
      `, { count: 'exact' })
      .in('status', ['active', 'out_of_stock']); // Include both active and out of stock products

    // Apply search pattern specific filters
    switch (searchQuery.pattern) {
      case 'oem_number':
        if (searchQuery.extractedTerms.oemNumber) {
          query = query.or(`part_article_number.ilike.%${searchQuery.extractedTerms.oemNumber}%,description_and_specifications.ilike.%${searchQuery.extractedTerms.oemNumber}%`);
        }
        break;
        
      case 'item_number':
        if (searchQuery.extractedTerms.itemNumber) {
          query = query.or(`part_article_number.ilike.%${searchQuery.extractedTerms.itemNumber}%,name.ilike.%${searchQuery.extractedTerms.itemNumber}%`);
        }
        break;
        
      case 'item_number_manufacturer':
        if (searchQuery.extractedTerms.itemNumber && searchQuery.extractedTerms.manufacturer) {
          query = query
            .or(`part_article_number.ilike.%${searchQuery.extractedTerms.itemNumber}%,name.ilike.%${searchQuery.extractedTerms.itemNumber}%`)
            .ilike('manufacturer', `%${searchQuery.extractedTerms.manufacturer}%`);
        }
        break;
        
      case 'car_part_manufacturer':
        if (searchQuery.extractedTerms.partName && searchQuery.extractedTerms.manufacturer) {
          query = query
            .or(`name.ilike.%${searchQuery.extractedTerms.partName}%,description_and_specifications.ilike.%${searchQuery.extractedTerms.partName}%`)
            .ilike('manufacturer', `%${searchQuery.extractedTerms.manufacturer}%`);
        }
        break;
        
      case 'car_part_vehicle_brand':
        if (searchQuery.extractedTerms.partName && searchQuery.extractedTerms.vehicleBrand) {
          query = query
            .or(`name.ilike.%${searchQuery.extractedTerms.partName}%,description_and_specifications.ilike.%${searchQuery.extractedTerms.partName}%`)
            .or(`compatible_vehicles.cs.{${searchQuery.extractedTerms.vehicleBrand}},description_and_specifications.ilike.%${searchQuery.extractedTerms.vehicleBrand}%`);
        }
        break;
        
      default: // car_part
        if (searchQuery.extractedTerms.partName) {
          query = query.or(`name.ilike.%${searchQuery.extractedTerms.partName}%,description_and_specifications.ilike.%${searchQuery.extractedTerms.partName}%,manufacturer.ilike.%${searchQuery.extractedTerms.partName}%`);
        } else {
          // Fallback: search in all text fields if no specific pattern matched
          const searchTerm = searchQuery.text;
          query = query.or(`name.ilike.%${searchTerm}%,description_and_specifications.ilike.%${searchTerm}%,manufacturer.ilike.%${searchTerm}%,part_article_number.ilike.%${searchTerm}%`);
        }
    }

    // Apply additional filters
    if (filters.category) {
      query = query.eq('category', filters.category);
    }
    if (filters.subcategory) {
      query = query.eq('subcategory', filters.subcategory);
    }
    if (filters.manufacturer) {
      query = query.ilike('manufacturer', `%${filters.manufacturer}%`);
    }
    if (filters.priceRange) {
      query = query.gte('retail_price', filters.priceRange.min).lte('retail_price', filters.priceRange.max);
    }
    if (filters.inStock) {
      query = query.gt('stock_quantity', 0);
    }

    return query.range(offset, offset + limit - 1);
  }

  /**
   * Transform database results and apply relevance ranking
   */
  private transformAndRankResults(products: any[], searchQuery: SearchQuery): AnyProduct[] {
    return products
      .map(product => this.transformDatabaseProduct(product))
      .sort((a, b) => this.calculateRelevanceScore(b, searchQuery) - this.calculateRelevanceScore(a, searchQuery));
  }

  /**
   * Transform database product to frontend format
   */
  private transformDatabaseProduct(dbProduct: any): AnyProduct {
    return {
      id: dbProduct.id,
      name: dbProduct.name,
      sku: dbProduct.id, // Use ID as SKU for now
      partArticleNumber: dbProduct.part_article_number,
      category: dbProduct.category,
      subcategory: dbProduct.subcategory,
      descriptionAndSpecifications: dbProduct.description_and_specifications || '',
      primaryImage: dbProduct.primary_image,
      additionalImages: dbProduct.additional_images || [],
      manufacturer: dbProduct.manufacturer,
      supplierName: dbProduct.supplier_name,
      stockQuantity: dbProduct.stock_quantity || 0,
      retailPrice: dbProduct.retail_price,
      wholesalePricingTiers: dbProduct.wholesale_pricing_tiers || [],
      certifications: dbProduct.certifications || [],
      status: dbProduct.status,
      inventoryUpdateDate: new Date(dbProduct.created_at),
      createdAt: new Date(dbProduct.created_at),
      updatedAt: new Date(dbProduct.created_at),
      marketplaceSection: 'retail' as const,
      compatibleVehicles: dbProduct.compatible_vehicles || [],
      // Add missing fields that AirbnbStyleProductCard might expect
      brand: dbProduct.manufacturer, // Use manufacturer as brand
      images: dbProduct.additional_images || [], // Legacy compatibility
      specifications: {}, // Empty object for legacy compatibility
    } as AnyProduct;
  }

  /**
   * Calculate relevance score for ranking
   */
  private calculateRelevanceScore(product: AnyProduct, searchQuery: SearchQuery): number {
    let score = 0;
    const query = searchQuery.text.toLowerCase();
    
    // Exact name match gets highest score
    if (product.name.toLowerCase().includes(query)) {
      score += 100;
    }
    
    // Manufacturer match
    if (product.manufacturer.toLowerCase().includes(query)) {
      score += 80;
    }
    
    // Part number match
    if (product.partArticleNumber?.toLowerCase().includes(query)) {
      score += 90;
    }
    
    // Description match
    if (product.descriptionAndSpecifications.toLowerCase().includes(query)) {
      score += 60;
    }
    
    // Stock availability bonus
    if (product.stockQuantity > 0) {
      score += 10;
    }
    
    return score;
  }

  /**
   * Generate search suggestions based on query and results
   */
  private async generateSuggestions(originalQuery: string, searchQuery: SearchQuery): Promise<string[]> {
    const suggestions: string[] = [];
    
    // Add related search terms based on pattern
    switch (searchQuery.pattern) {
      case 'car_part':
        suggestions.push(
          `${originalQuery} CHAMPION`,
          `${originalQuery} BOSCH`,
          `${originalQuery} VALEO`
        );
        break;
      case 'car_part_manufacturer':
        if (searchQuery.extractedTerms.partName) {
          suggestions.push(
            `${searchQuery.extractedTerms.partName} TOYOTA`,
            `${searchQuery.extractedTerms.partName} RENAULT`
          );
        }
        break;
    }
    
    return suggestions.slice(0, 5);
  }

  /**
   * Generate cache key for search results
   */
  private generateCacheKey(query: string, filters: SearchFilters, limit: number, offset: number): string {
    return `search_${query}_${JSON.stringify(filters)}_${limit}_${offset}`;
  }

  /**
   * Check if cached result is still valid
   */
  private isCacheValid(result: SearchResult): boolean {
    // For now, always return false to disable caching during development
    return false;
  }

  /**
   * Clear search cache
   */
  clearCache(): void {
    this.searchCache.clear();
    this.suggestionCache.clear();
  }

  /**
   * Search for products by category with text filter
   */
  async searchByCategory(
    category: string,
    textQuery?: string,
    filters: SearchFilters = {},
    limit: number = 50
  ): Promise<SearchResult> {
    const startTime = Date.now();

    try {
      let query = supabase
        .from('products')
        .select(`
          id, name, description_and_specifications, manufacturer, category, subcategory,
          part_article_number, retail_price, stock_quantity, status, primary_image,
          additional_images, compatible_vehicles, certifications, created_at
        `, { count: 'exact' })
        .eq('status', 'active')
        .eq('category', category);

      // Add text search if provided
      if (textQuery && textQuery.trim()) {
        const normalizedQuery = this.normalizeQuery(textQuery);
        query = query.or(`name.ilike.%${normalizedQuery}%,description_and_specifications.ilike.%${normalizedQuery}%,manufacturer.ilike.%${normalizedQuery}%`);
      }

      // Apply additional filters
      if (filters.subcategory) {
        query = query.eq('subcategory', filters.subcategory);
      }
      if (filters.manufacturer) {
        query = query.ilike('manufacturer', `%${filters.manufacturer}%`);
      }
      if (filters.priceRange) {
        query = query.gte('retail_price', filters.priceRange.min).lte('retail_price', filters.priceRange.max);
      }
      if (filters.inStock) {
        query = query.gt('stock_quantity', 0);
      }

      const { data: products, count } = await query.limit(limit);

      const transformedProducts = (products || []).map(product => this.transformDatabaseProduct(product));

      return {
        products: transformedProducts,
        totalCount: count || 0,
        searchTime: Date.now() - startTime,
        suggestions: [],
        appliedFilters: { ...filters, category }
      };

    } catch (error) {
      console.error('Category search error:', error);
      return {
        products: [],
        totalCount: 0,
        searchTime: Date.now() - startTime,
        suggestions: [],
        appliedFilters: { ...filters, category }
      };
    }
  }

  /**
   * Get popular search terms for suggestions
   */
  async getPopularSearchTerms(limit: number = 10): Promise<string[]> {
    try {
      // Get most common product names and manufacturers
      const { data: products } = await supabase
        .from('products')
        .select('name, manufacturer')
        .eq('status', 'active')
        .limit(100);

      if (!products) return [];

      // Extract common terms
      const termCounts = new Map<string, number>();

      products.forEach(product => {
        // Split product names into words
        const words = product.name.toLowerCase().split(/\s+/);
        words.forEach(word => {
          if (word.length > 3) { // Only consider words longer than 3 characters
            termCounts.set(word, (termCounts.get(word) || 0) + 1);
          }
        });

        // Add manufacturer
        if (product.manufacturer) {
          const manufacturer = product.manufacturer.toLowerCase();
          termCounts.set(manufacturer, (termCounts.get(manufacturer) || 0) + 1);
        }
      });

      // Sort by frequency and return top terms
      return Array.from(termCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, limit)
        .map(([term]) => term);

    } catch (error) {
      console.error('Popular terms error:', error);
      return [];
    }
  }

  /**
   * Detect if search query requires vehicle information (for tyres)
   */
  requiresVehicleInfo(query: string): boolean {
    const normalizedQuery = this.normalizeQuery(query);

    // Tyre-related terms that require vehicle info
    const tyreTerms = ['pneu', 'tyre', 'tire', 'pneumatique'];
    const tyrePatterns = [
      /\d{3}\/\d{2}R\d{2}/, // Tyre size pattern like 205/55R16
      /\d{3}\s*\/\s*\d{2}\s*R\s*\d{2}/ // Tyre size with spaces
    ];

    // Check for tyre terms
    const containsTyreTerms = tyreTerms.some(term => normalizedQuery.includes(term));

    // Check for tyre size patterns
    const containsTyrePattern = tyrePatterns.some(pattern => pattern.test(normalizedQuery));

    return containsTyreTerms || containsTyrePattern;
  }

  /**
   * Get search analytics data
   */
  getSearchAnalytics(): {
    cacheSize: number;
    suggestionCacheSize: number;
    averageSearchTime: number;
  } {
    return {
      cacheSize: this.searchCache.size,
      suggestionCacheSize: this.suggestionCache.size,
      averageSearchTime: 0 // TODO: Implement search time tracking
    };
  }

  /**
   * Get current language from localStorage
   */
  private getCurrentLanguage(): string {
    return localStorage.getItem('i18nextLng') || 'en';
  }

  /**
   * Track suggestion click for analytics
   */
  async trackSuggestionClick(suggestion: string, type: 'suggestion' | 'recent' | 'popular'): Promise<void> {
    await searchAnalyticsService.trackSuggestionClick(suggestion, type);
  }

  /**
   * Track result click for analytics
   */
  async trackResultClick(query: string, resultId: string, position: number, searchTimeMs: number): Promise<void> {
    await searchAnalyticsService.trackResultClick(query, resultId, position, searchTimeMs);
  }
}

// Export singleton instance
export const intelligentSearchService = new IntelligentSearchService();

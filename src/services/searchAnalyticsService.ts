/**
 * Search Analytics Service
 * 
 * Tracks search performance, user behavior, and provides insights
 * for optimizing the intelligent search functionality.
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export interface SearchAnalyticsEvent {
  id?: string;
  session_id: string;
  user_id?: string;
  query: string;
  search_type: 'text' | 'suggestion' | 'recent' | 'popular';
  results_count: number;
  search_time_ms: number;
  filters_applied: Record<string, any>;
  clicked_result_id?: string;
  clicked_result_position?: number;
  timestamp: Date;
  user_agent?: string;
  language: string;
}

export interface SearchPerformanceMetrics {
  averageSearchTime: number;
  totalSearches: number;
  successRate: number;
  popularQueries: Array<{ query: string; count: number }>;
  slowQueries: Array<{ query: string; avgTime: number }>;
  zeroResultQueries: Array<{ query: string; count: number }>;
}

export interface SearchSuggestionMetrics {
  suggestionClickRate: number;
  popularSuggestions: Array<{ suggestion: string; clicks: number }>;
  recentSearchUsage: number;
}

class SearchAnalyticsService {
  private sessionId: string;
  private analyticsQueue: SearchAnalyticsEvent[] = [];
  private flushInterval: number = 30000; // 30 seconds
  private maxQueueSize: number = 50;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.startPeriodicFlush();
  }

  /**
   * Track a search event
   */
  async trackSearch(event: Omit<SearchAnalyticsEvent, 'id' | 'session_id' | 'timestamp'>): Promise<void> {
    const analyticsEvent: SearchAnalyticsEvent = {
      ...event,
      session_id: this.sessionId,
      timestamp: new Date(),
      user_agent: navigator.userAgent,
    };

    // Add to queue for batch processing
    this.analyticsQueue.push(analyticsEvent);

    // Flush if queue is full
    if (this.analyticsQueue.length >= this.maxQueueSize) {
      await this.flushAnalytics();
    }

    // Also log to console for development
    console.log('🔍 Search Analytics:', {
      query: event.query,
      results: event.results_count,
      time: `${event.search_time_ms}ms`,
      type: event.search_type
    });
  }

  /**
   * Track when user clicks on a search result
   */
  async trackResultClick(
    query: string,
    resultId: string,
    position: number,
    searchTimeMs: number
  ): Promise<void> {
    await this.trackSearch({
      query,
      search_type: 'text',
      results_count: 0, // Not relevant for click tracking
      search_time_ms: searchTimeMs,
      filters_applied: {},
      clicked_result_id: resultId,
      clicked_result_position: position,
      language: this.getCurrentLanguage()
    });
  }

  /**
   * Track suggestion usage
   */
  async trackSuggestionClick(suggestion: string, type: 'suggestion' | 'recent' | 'popular'): Promise<void> {
    await this.trackSearch({
      query: suggestion,
      search_type: type,
      results_count: 0,
      search_time_ms: 0,
      filters_applied: {},
      language: this.getCurrentLanguage()
    });
  }

  /**
   * Get search performance metrics
   */
  async getPerformanceMetrics(days: number = 7): Promise<SearchPerformanceMetrics> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data: searchEvents, error } = await supabase
        .from('search_analytics')
        .select('*')
        .gte('timestamp', startDate.toISOString())
        .order('timestamp', { ascending: false });

      if (error) {
        console.error('Error fetching search analytics:', error);
        return this.getDefaultMetrics();
      }

      if (!searchEvents || searchEvents.length === 0) {
        return this.getDefaultMetrics();
      }

      // Calculate metrics
      const totalSearches = searchEvents.length;
      const totalSearchTime = searchEvents.reduce((sum, event) => sum + (event.search_time_ms || 0), 0);
      const averageSearchTime = totalSearchTime / totalSearches;
      const successfulSearches = searchEvents.filter(event => event.results_count > 0).length;
      const successRate = (successfulSearches / totalSearches) * 100;

      // Popular queries
      const queryCount = new Map<string, number>();
      searchEvents.forEach(event => {
        const count = queryCount.get(event.query) || 0;
        queryCount.set(event.query, count + 1);
      });

      const popularQueries = Array.from(queryCount.entries())
        .map(([query, count]) => ({ query, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // Slow queries
      const queryTimes = new Map<string, number[]>();
      searchEvents.forEach(event => {
        if (event.search_time_ms > 0) {
          const times = queryTimes.get(event.query) || [];
          times.push(event.search_time_ms);
          queryTimes.set(event.query, times);
        }
      });

      const slowQueries = Array.from(queryTimes.entries())
        .map(([query, times]) => ({
          query,
          avgTime: times.reduce((sum, time) => sum + time, 0) / times.length
        }))
        .filter(item => item.avgTime > 1000) // Queries taking more than 1 second
        .sort((a, b) => b.avgTime - a.avgTime)
        .slice(0, 10);

      // Zero result queries
      const zeroResultQueries = Array.from(queryCount.entries())
        .filter(([query]) => {
          const queryEvents = searchEvents.filter(event => event.query === query);
          return queryEvents.every(event => event.results_count === 0);
        })
        .map(([query, count]) => ({ query, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        averageSearchTime,
        totalSearches,
        successRate,
        popularQueries,
        slowQueries,
        zeroResultQueries
      };

    } catch (error) {
      console.error('Error calculating performance metrics:', error);
      return this.getDefaultMetrics();
    }
  }

  /**
   * Get suggestion metrics
   */
  async getSuggestionMetrics(days: number = 7): Promise<SearchSuggestionMetrics> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data: suggestionEvents, error } = await supabase
        .from('search_analytics')
        .select('*')
        .gte('timestamp', startDate.toISOString())
        .in('search_type', ['suggestion', 'recent', 'popular']);

      if (error || !suggestionEvents) {
        return {
          suggestionClickRate: 0,
          popularSuggestions: [],
          recentSearchUsage: 0
        };
      }

      const totalSuggestionClicks = suggestionEvents.length;
      const suggestionClicks = suggestionEvents.filter(event => event.search_type === 'suggestion').length;
      const recentSearchClicks = suggestionEvents.filter(event => event.search_type === 'recent').length;

      // Calculate suggestion click rate (would need total search sessions for accurate rate)
      const suggestionClickRate = totalSuggestionClicks > 0 ? (suggestionClicks / totalSuggestionClicks) * 100 : 0;

      // Popular suggestions
      const suggestionCount = new Map<string, number>();
      suggestionEvents.forEach(event => {
        const count = suggestionCount.get(event.query) || 0;
        suggestionCount.set(event.query, count + 1);
      });

      const popularSuggestions = Array.from(suggestionCount.entries())
        .map(([suggestion, clicks]) => ({ suggestion, clicks }))
        .sort((a, b) => b.clicks - a.clicks)
        .slice(0, 10);

      const recentSearchUsage = recentSearchClicks;

      return {
        suggestionClickRate,
        popularSuggestions,
        recentSearchUsage
      };

    } catch (error) {
      console.error('Error calculating suggestion metrics:', error);
      return {
        suggestionClickRate: 0,
        popularSuggestions: [],
        recentSearchUsage: 0
      };
    }
  }

  /**
   * Flush analytics queue to database
   */
  private async flushAnalytics(): Promise<void> {
    if (this.analyticsQueue.length === 0) return;

    try {
      const events = [...this.analyticsQueue];
      this.analyticsQueue = [];

      const { error } = await supabase
        .from('search_analytics')
        .insert(events);

      if (error) {
        console.error('Error flushing search analytics:', error);
        // Re-add events to queue for retry
        this.analyticsQueue.unshift(...events);
      } else {
        console.log(`✅ Flushed ${events.length} search analytics events`);
      }

    } catch (error) {
      console.error('Error in flushAnalytics:', error);
    }
  }

  /**
   * Start periodic flush of analytics
   */
  private startPeriodicFlush(): void {
    setInterval(() => {
      this.flushAnalytics();
    }, this.flushInterval);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flushAnalytics();
    });
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current language
   */
  private getCurrentLanguage(): string {
    return localStorage.getItem('i18nextLng') || 'en';
  }

  /**
   * Get default metrics when data is unavailable
   */
  private getDefaultMetrics(): SearchPerformanceMetrics {
    return {
      averageSearchTime: 0,
      totalSearches: 0,
      successRate: 0,
      popularQueries: [],
      slowQueries: [],
      zeroResultQueries: []
    };
  }

  /**
   * Clear analytics queue (for testing)
   */
  clearQueue(): void {
    this.analyticsQueue = [];
  }

  /**
   * Get current queue size (for monitoring)
   */
  getQueueSize(): number {
    return this.analyticsQueue.length;
  }
}

// Export singleton instance
export const searchAnalyticsService = new SearchAnalyticsService();

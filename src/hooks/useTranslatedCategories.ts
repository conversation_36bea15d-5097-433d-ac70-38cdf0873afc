/**
 * React Hook for Translated Categories and Subcategories
 * 
 * This hook provides translated category and subcategory names
 * for the AROUZ MARKET consumer marketplace, ensuring 100%
 * translation coverage without modifying the database.
 */

import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CATEGORIES, CategoryData, SubcategoryData } from '@/data/categoryData';
import {
  translateCategory,
  translateSubcategory,
  useCategoryTranslation
} from '@/services/translationService';
import { useLegacyCategories } from '@/data/categoryDataSupabase';

export interface TranslatedCategoryData extends Omit<CategoryData, 'displayName' | 'subcategories'> {
  displayName: string;
  originalDisplayName: string;
  subcategories: TranslatedSubcategoryData[];
}

export interface TranslatedSubcategoryData extends Omit<SubcategoryData, 'displayName'> {
  displayName: string;
  originalDisplayName: string;
}

/**
 * Hook to get all categories with translated names
 */
export function useTranslatedCategories(): TranslatedCategoryData[] {
  const { i18n } = useTranslation();
  const { translateCategory: translateCat, translateSubcategory: translateSub } = useCategoryTranslation();
  
  return useMemo(() => {
    return CATEGORIES.map(category => ({
      ...category,
      originalDisplayName: category.displayName,
      displayName: translateCat(category.id),
      subcategories: category.subcategories.map(subcategory => ({
        ...subcategory,
        originalDisplayName: subcategory.displayName,
        displayName: translateSub(subcategory.id)
      }))
    }));
  }, [i18n.language, translateCat, translateSub]);
}

/**
 * Hook to get a specific category with translated names
 */
export function useTranslatedCategory(categoryId: string): TranslatedCategoryData | undefined {
  const translatedCategories = useTranslatedCategories();
  
  return useMemo(() => {
    return translatedCategories.find(category => category.id === categoryId);
  }, [translatedCategories, categoryId]);
}

/**
 * Hook to get subcategories for a category with translated names
 */
export function useTranslatedSubcategories(categoryId: string): TranslatedSubcategoryData[] {
  const translatedCategory = useTranslatedCategory(categoryId);
  
  return useMemo(() => {
    return translatedCategory?.subcategories || [];
  }, [translatedCategory]);
}

/**
 * Hook to get a specific subcategory with translated name
 */
export function useTranslatedSubcategory(
  categoryId: string, 
  subcategoryId: string
): TranslatedSubcategoryData | undefined {
  const translatedSubcategories = useTranslatedSubcategories(categoryId);
  
  return useMemo(() => {
    return translatedSubcategories.find(subcategory => subcategory.id === subcategoryId);
  }, [translatedSubcategories, subcategoryId]);
}

/**
 * Hook to translate category display name
 */
export function useCategoryDisplayName(categoryId: string): string {
  const { translateCategory } = useCategoryTranslation();
  
  return useMemo(() => {
    return translateCategory(categoryId);
  }, [categoryId, translateCategory]);
}

/**
 * Hook to translate subcategory display name
 */
export function useSubcategoryDisplayName(subcategoryId: string): string {
  const { translateSubcategory } = useCategoryTranslation();
  
  return useMemo(() => {
    return translateSubcategory(subcategoryId);
  }, [subcategoryId, translateSubcategory]);
}

/**
 * Hook for marketplace subcategory navigation with translations
 * Uses database data with translation overlay
 */
export function useMarketplaceSubcategoryNavigation(categoryId: string) {
  const { i18n } = useTranslation();
  const { translateSubcategory } = useCategoryTranslation();
  const { categories: supabaseCategories, isLoading } = useLegacyCategories();

  return useMemo(() => {
    // Get subcategories from database
    const category = supabaseCategories.find(cat => cat.id === categoryId);
    const subcategories = category?.subcategories || [];

    // Apply translations to database data
    return subcategories.map(subcategory => {
      const translatedName = translateSubcategory(subcategory.id);

      return {
        id: subcategory.id,
        name: subcategory.name,
        displayName: translatedName || subcategory.displayName,
        originalDisplayName: subcategory.displayName,
        categoryId: categoryId,
        imageUrl: subcategory.imageUrl
      };
    });
  }, [categoryId, supabaseCategories, translateSubcategory, i18n.language]);
}

/**
 * Hook for marketplace category navigation with translations
 * Uses database data with translation overlay
 */
export function useMarketplaceCategoryNavigation() {
  const { i18n } = useTranslation();
  const { translateCategory } = useCategoryTranslation();
  const { categories: supabaseCategories, isLoading } = useLegacyCategories();

  return useMemo(() => {
    // Apply translations to database data
    return supabaseCategories.map(category => {
      const translatedName = translateCategory(category.id);

      return {
        id: category.id,
        displayName: translatedName || category.displayName,
        originalDisplayName: category.displayName
      };
    });
  }, [supabaseCategories, translateCategory, i18n.language]);
}

/**
 * Utility function to get translated category name (non-hook)
 * Useful for components that can't use hooks
 */
export function getCategoryTranslatedName(categoryId: string, language: 'ar' | 'fr' | 'en'): string {
  return translateCategory(categoryId, language);
}

/**
 * Utility function to get translated subcategory name (non-hook)
 * Useful for components that can't use hooks
 */
export function getSubcategoryTranslatedName(subcategoryId: string, language: 'ar' | 'fr' | 'en'): string {
  return translateSubcategory(subcategoryId, language);
}

/**
 * Hook to check if current language needs translation
 */
export function useNeedsTranslation(): boolean {
  const { i18n } = useTranslation();
  
  return useMemo(() => {
    return i18n.language !== 'en';
  }, [i18n.language]);
}

/**
 * Hook to get current language code
 */
export function useCurrentLanguage(): 'ar' | 'fr' | 'en' {
  const { i18n } = useTranslation();
  
  return useMemo(() => {
    return i18n.language as 'ar' | 'fr' | 'en';
  }, [i18n.language]);
}

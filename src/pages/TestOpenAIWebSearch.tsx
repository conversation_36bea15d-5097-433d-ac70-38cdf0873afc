import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, CheckCircle, XCircle } from 'lucide-react';

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  duration?: number;
}

export default function TestOpenAIWebSearch() {
  const [partNumber, setPartNumber] = useState('11-28001-SX');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<TestResult | null>(null);

  const testWebSearch = async () => {
    setIsLoading(true);
    setResult(null);

    const startTime = Date.now();

    try {
      // Dynamic import to avoid initial loading issues
      const { AIWebSearchService } = await import('@/services/aiWebSearchService');
      const service = new AIWebSearchService();

      const enhancedData = await service.enhanceProductData({
        partNumber,
        productName: '', // Let AI find the real product name
        manufacturer: '', // Let AI find the real manufacturer
        category: 'other-automotive-parts',
        price: 0,
        stockQuantity: 0
      });

      const duration = Date.now() - startTime;

      setResult({
        success: true,
        data: enhancedData,
        duration
      });

    } catch (error: any) {
      const duration = Date.now() - startTime;

      setResult({
        success: false,
        error: error.message || 'Unknown error occurred',
        duration
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">OpenAI Web Search Test</h1>
        <p className="text-gray-600">
          Test the AI-powered product enhancement using OpenAI's integrated web search
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Test Product Data
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="max-w-md">
            <label className="block text-sm font-medium mb-1">Part Number</label>
            <Input
              value={partNumber}
              onChange={(e) => setPartNumber(e.target.value)}
              placeholder="Enter automotive part number"
              className="text-lg"
            />
            <p className="text-sm text-gray-500 mt-1">
              Enter only the part number - AI will search the web for all other details
            </p>
          </div>
          
          <Button
            onClick={testWebSearch}
            disabled={isLoading || !partNumber}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Searching Web for Part Data...
              </>
            ) : (
              <>
                <Search className="mr-2 h-4 w-4" />
                Search Web for Part Information
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              Test Results
              <Badge variant={result.success ? "default" : "destructive"}>
                {result.duration}ms
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {result.success ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold mb-2">Enhanced Product Info</h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Product Name:</strong> {result.data.productName}</p>
                      <p><strong>Part Number:</strong> {result.data.partArticleNumber}</p>
                      <p><strong>Manufacturer:</strong> {result.data.manufacturer}</p>
                      <p><strong>Category:</strong> {result.data.category}</p>
                      <p><strong>Confidence:</strong> {result.data.confidence}%</p>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold mb-2">Vehicle Compatibility</h3>
                    <p className="text-sm">{result.data.vehicleCompatibility}</p>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Enhanced Descriptions</h3>
                  <div className="space-y-2">
                    <div>
                      <h4 className="font-medium text-sm">General Information</h4>
                      <p className="text-sm text-gray-600">{result.data.description.generalInformation}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Technical Information</h4>
                      <p className="text-sm text-gray-600">{result.data.description.technicalInformation}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Vehicle Applicability</h4>
                      <p className="text-sm text-gray-600">{result.data.description.applicability}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Original Numbers</h4>
                      <p className="text-sm text-gray-600">{result.data.description.originalNumbers}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">OEM Numbers</h4>
                      <p className="text-sm text-gray-600">{result.data.description.oemNumbers}</p>
                    </div>
                  </div>
                </div>
                
                {result.data.searchSources && result.data.searchSources.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Search Sources</h3>
                    <div className="flex flex-wrap gap-2">
                      {result.data.searchSources.map((source: string, index: number) => (
                        <Badge key={index} variant="outline">{source}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-red-600">
                <h3 className="font-semibold mb-2">Error Details</h3>
                <p>{result.error}</p>
                
                {result.error?.includes('API key') && (
                  <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
                    <h4 className="font-medium text-yellow-800">Setup Required</h4>
                    <p className="text-yellow-700 text-sm mt-1">
                      Please add your OpenAI API key to the .env file:
                    </p>
                    <code className="block mt-2 p-2 bg-yellow-100 text-yellow-800 text-xs rounded">
                      VITE_OPENAI_API_KEY=your-openai-api-key-here
                    </code>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
      
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded">
        <h3 className="font-medium text-blue-800 mb-2">How it works</h3>
        <ul className="text-blue-700 text-sm space-y-1">
          <li>• <strong>Part Number Only:</strong> Enter just the part number - no other info needed</li>
          <li>• <strong>Web Search:</strong> OpenAI searches automotive websites for real data</li>
          <li>• <strong>Real Information:</strong> Finds actual product names, manufacturers, and specs</li>
          <li>• <strong>Vehicle Compatibility:</strong> Discovers specific vehicle applications</li>
          <li>• <strong>Comprehensive Data:</strong> Returns 5-section descriptions with OEM numbers</li>
        </ul>
      </div>
    </div>
  );
}

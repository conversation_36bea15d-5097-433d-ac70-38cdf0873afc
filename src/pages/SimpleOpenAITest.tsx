import React from 'react';

export default function SimpleOpenAITest() {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-4">Simple OpenAI Test</h1>
      <p className="text-gray-600 mb-4">
        This is a simple test page to verify routing is working.
      </p>
      
      <div className="bg-blue-50 border border-blue-200 rounded p-4">
        <h2 className="font-semibold text-blue-800 mb-2">Page Status</h2>
        <p className="text-blue-700">✅ Page is loading correctly!</p>
        <p className="text-blue-700">✅ React components are rendering!</p>
        <p className="text-blue-700">✅ Routing is working!</p>
      </div>
      
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded">
        <h3 className="font-medium text-green-800 mb-2">Next Steps</h3>
        <p className="text-green-700 text-sm">
          If you can see this page, the routing is working correctly. 
          The issue with the main test page might be related to the AI service imports.
        </p>
      </div>
    </div>
  );
}

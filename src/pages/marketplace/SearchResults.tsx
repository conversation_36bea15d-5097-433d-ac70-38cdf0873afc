import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Filter,
  Grid3X3,
  List,
  Search,
  Clock,
  X,
  ChevronDown,
  ChevronUp,
  ArrowLeft,
  Loader2
} from 'lucide-react';
import { AirbnbStyleProductCard } from '@/components/marketplace/AirbnbStyleProductCard';
import { AutoZoneStyleProductListItem } from '@/components/marketplace/AutoZoneStyleProductListItem';
import { MarketplaceHeader } from '@/components/marketplace/MarketplaceHeader';
import { FilterProvider } from '@/contexts/FilterContext';
import { intelligentSearchService, SearchResult, SearchFilters, AnyProduct } from '@/services/intelligentSearchService';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

function SearchResultsContent() {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Error boundary state
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Wrap everything in try-catch
  try {
    console.log('🔍 SearchResults component starting...');

  const [searchParams, setSearchParams] = useSearchParams();
  
  // Get search query from URL
  const query = searchParams.get('q') || '';
  
  // State
  const [searchResult, setSearchResult] = useState<SearchResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [sortBy, setSortBy] = useState('relevance');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage] = useState(50);

  // Dynamic filter states
  const [tyreFilters, setTyreFilters] = useState({
    width: '',
    aspectRatio: '',
    rimDiameter: '',
    loadIndex: ''
  });
  const [vehicleSearch, setVehicleSearch] = useState('');

  // Perform search when query or filters change
  useEffect(() => {
    if (query.trim()) {
      performSearch();
    }
  }, [query, filters, currentPage]);

  const performSearch = async () => {
    if (!query.trim()) return;

    setIsLoading(true);
    try {
      const offset = (currentPage - 1) * productsPerPage;
      const result = await intelligentSearchService.search(
        query, 
        filters, 
        productsPerPage, 
        offset
      );
      setSearchResult(result);

      // Show search analytics
      console.log(`🔍 Search completed in ${result.searchTime}ms for "${query}"`, {
        totalResults: result.totalCount,
        productsReturned: result.products.length,
        searchResult: result
      });
      
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: 'Search Error',
        description: 'Failed to search products. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({});
    setCurrentPage(1);
  };

  // Detect dominant product type from search results
  const detectDominantProductType = React.useMemo(() => {
    if (!searchResult?.products || searchResult.products.length === 0) {
      return 'mixed';
    }

    const products = searchResult.products;
    const categoryCount = products.reduce((acc, product) => {
      acc[product.category] = (acc[product.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalProducts = products.length;
    const dominantCategory = Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)[0];

    // If a category represents more than 60% of results, consider it dominant
    if (dominantCategory && dominantCategory[1] / totalProducts > 0.6) {
      return dominantCategory[0];
    }

    return 'mixed';
  }, [searchResult?.products]);

  // Sort products
  const sortedProducts = React.useMemo(() => {
    if (!searchResult?.products) {
      console.log('🔍 No search result or products available');
      return [];
    }

    const products = [...searchResult.products];
    console.log('🔍 Sorting products:', {
      totalProducts: products.length,
      sortBy,
      sampleProduct: products[0],
      dominantProductType: detectDominantProductType
    });

    switch (sortBy) {
      case 'price_low':
        return products.sort((a, b) => (a.retailPrice || 0) - (b.retailPrice || 0));
      case 'price_high':
        return products.sort((a, b) => (b.retailPrice || 0) - (a.retailPrice || 0));
      case 'name':
        return products.sort((a, b) => a.name.localeCompare(b.name));
      case 'newest':
        return products.sort((a, b) =>
          new Date(b.inventoryUpdateDate).getTime() - new Date(a.inventoryUpdateDate).getTime()
        );
      case 'relevance':
      default:
        return products; // Already sorted by relevance from search service
    }
  }, [searchResult?.products, sortBy, detectDominantProductType]);

  // Debug logging
  console.log('🔍 SearchResults Debug:', {
    query,
    isLoading,
    searchResult: searchResult ? {
      totalCount: searchResult.totalCount,
      productsLength: searchResult.products.length,
      searchTime: searchResult.searchTime
    } : null,
    sortedProductsLength: sortedProducts.length,
    sampleProduct: sortedProducts[0]
  });

  // Calculate pagination
  const totalPages = Math.ceil((searchResult?.totalCount || 0) / productsPerPage);

  // Handle back navigation
  const handleBack = () => {
    navigate(-1);
  };

  // Handle new search
  const handleNewSearch = (newQuery: string) => {
    setSearchParams({ q: newQuery });
  };

  if (!query.trim()) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              No Search Query
            </h1>
            <p className="text-gray-600 mb-6">
              Please enter a search term to find products
            </p>
            <Button onClick={() => navigate('/my-vehicle-parts')}>
              Back to Marketplace
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Marketplace Header */}
      <MarketplaceHeader />

      {/* DEBUG SECTION - Remove in production */}
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-2">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <h3 className="text-sm font-bold text-yellow-800 mb-2">🔍 SEARCH DEBUG INFORMATION</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
            <div>
              <h4 className="font-semibold text-yellow-700 mb-1">Search State:</h4>
              <ul className="space-y-1 text-yellow-600">
                <li><strong>Query:</strong> "{query}"</li>
                <li><strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}</li>
                <li><strong>Current Page:</strong> {currentPage}</li>
                <li><strong>Sort By:</strong> {sortBy}</li>
                <li><strong>View Mode:</strong> {viewMode}</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-yellow-700 mb-1">Search Results:</h4>
              <ul className="space-y-1 text-yellow-600">
                <li><strong>Total Count:</strong> {searchResult?.totalCount || 'N/A'}</li>
                <li><strong>Products Returned:</strong> {searchResult?.products?.length || 0}</li>
                <li><strong>Search Time:</strong> {searchResult?.searchTime || 'N/A'}ms</li>
                <li><strong>Sorted Products:</strong> {sortedProducts.length}</li>
                <li><strong>Has Error:</strong> {hasError ? 'Yes' : 'No'}</li>
              </ul>
            </div>
          </div>
          {searchResult && (
            <div className="mt-3">
              <h4 className="font-semibold text-yellow-700 mb-1">Sample Product:</h4>
              <pre className="bg-yellow-100 p-2 rounded text-xs overflow-auto max-h-32">
                {JSON.stringify(sortedProducts[0] || 'No products', null, 2)}
              </pre>
            </div>
          )}
          {errorMessage && (
            <div className="mt-3">
              <h4 className="font-semibold text-red-700 mb-1">Error Message:</h4>
              <p className="text-red-600 text-xs">{errorMessage}</p>
            </div>
          )}
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-8">
        {/* Mobile-optimized header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4 sm:mb-6">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 p-2 sm:px-3 flex-shrink-0"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Back</span>
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-2xl font-bold text-gray-900 truncate">
                Search Results
              </h1>
              <p className="text-xs sm:text-sm text-gray-600 mt-1 truncate">
                {isLoading ? (
                  'Searching...'
                ) : searchResult ? (
                  <>
                    <span>{searchResult.totalCount} results for "{query}"</span>
                    <span className="hidden sm:inline"> ({searchResult.searchTime}ms)</span>
                  </>
                ) : (
                  `Search for "${query}"`
                )}
              </p>
            </div>
          </div>

          {/* Mobile-optimized view mode toggle */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <div className="flex items-center bg-white rounded-lg border shadow-sm">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className={cn(
                  "px-3 py-2 rounded-l-lg",
                  viewMode === 'grid' ? 'bg-[#DC2626] text-white' : 'hover:bg-gray-50'
                )}
              >
                <Grid3X3 className="h-4 w-4" />
                <span className="hidden sm:inline ml-2">Grid</span>
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className={cn(
                  "px-3 py-2 rounded-r-lg",
                  viewMode === 'list' ? 'bg-[#DC2626] text-white' : 'hover:bg-gray-50'
                )}
              >
                <List className="h-4 w-4" />
                <span className="hidden sm:inline ml-2">List</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Search suggestions */}
        {searchResult?.suggestions && searchResult.suggestions.length > 0 && (
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-sm font-medium text-blue-900 mb-2">
              Related Searches
            </h3>
            <div className="flex flex-wrap gap-2">
              {searchResult.suggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleNewSearch(suggestion)}
                  className="text-blue-700 border-blue-200 hover:bg-blue-100"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Mobile filters toggle button */}
        <div className="lg:hidden mb-4">
          <Button
            variant="outline"
            onClick={() => setShowMobileFilters(!showMobileFilters)}
            className="w-full flex items-center justify-between bg-white border-gray-300 hover:bg-gray-50 p-3"
          >
            <span className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <span className="font-medium">Filters</span>
            </span>
            <ChevronDown className={cn(
              "h-4 w-4 transition-transform",
              showMobileFilters && "rotate-180"
            )} />
          </Button>
        </div>

        <div className="flex flex-col lg:flex-row gap-4 lg:gap-8">
          {/* Sidebar Filters - Mobile collapsible, Desktop sticky */}
          <div className={cn(
            "lg:w-80 lg:flex-shrink-0",
            showMobileFilters ? "block" : "hidden lg:block"
          )}>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 lg:p-6 lg:sticky lg:top-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Filters
                </h3>
                {Object.keys(filters).length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="text-[#DC2626] hover:text-[#B91C1C]"
                  >
                    Clear All
                  </Button>
                )}
              </div>

              {/* Active filters */}
              {Object.entries(filters).length > 0 && (
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    Active Filters
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(filters).map(([key, value]) => {
                      if (!value) return null;
                      return (
                        <Badge
                          key={key}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {String(value)}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => handleFilterChange({ [key]: undefined })}
                          />
                        </Badge>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Dynamic Filter Section - Tyre Specifications */}
              {detectDominantProductType === 'tyres' && (
                <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="text-md font-semibold text-blue-900 mb-4">🔧 Tyre Specifications</h4>

                  {/* Width */}
                  <div className="mb-3">
                    <label className="text-sm font-medium text-gray-700 mb-1 block">Width (mm)</label>
                    <input
                      type="number"
                      placeholder="e.g. 205"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                      value={tyreFilters.width}
                      onChange={(e) => setTyreFilters(prev => ({ ...prev, width: e.target.value }))}
                    />
                  </div>

                  {/* Aspect Ratio */}
                  <div className="mb-3">
                    <label className="text-sm font-medium text-gray-700 mb-1 block">Aspect Ratio (%)</label>
                    <input
                      type="number"
                      placeholder="e.g. 55"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                      value={tyreFilters.aspectRatio}
                      onChange={(e) => setTyreFilters(prev => ({ ...prev, aspectRatio: e.target.value }))}
                    />
                  </div>

                  {/* Rim Diameter */}
                  <div className="mb-3">
                    <label className="text-sm font-medium text-gray-700 mb-1 block">Rim Diameter (inches)</label>
                    <input
                      type="number"
                      placeholder="e.g. 16"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                      value={tyreFilters.rimDiameter}
                      onChange={(e) => setTyreFilters(prev => ({ ...prev, rimDiameter: e.target.value }))}
                    />
                  </div>

                  {/* Load Index */}
                  <div className="mb-3">
                    <label className="text-sm font-medium text-gray-700 mb-1 block">Load Index</label>
                    <input
                      type="number"
                      placeholder="e.g. 91"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                      value={tyreFilters.loadIndex}
                      onChange={(e) => setTyreFilters(prev => ({ ...prev, loadIndex: e.target.value }))}
                    />
                  </div>


                </div>
              )}

              {/* Dynamic Filter Section - Vehicle Applicability */}
              {(detectDominantProductType !== 'tyres' && detectDominantProductType !== 'mixed') && (
                <div className="mb-6 p-4 bg-green-50 rounded-lg border border-green-200">
                  <h4 className="text-md font-semibold text-green-900 mb-4">🚗 Vehicle Applicability</h4>

                  <div className="mb-3">
                    <label className="text-sm font-medium text-gray-700 mb-1 block">Search Vehicle</label>
                    <input
                      type="text"
                      placeholder="e.g. Toyota Corolla 2020, BMW E46, Renault Clio"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                      value={vehicleSearch}
                      onChange={(e) => setVehicleSearch(e.target.value)}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter vehicle brand, model, year, or generation
                    </p>
                  </div>
                </div>
              )}



              {/* Brand filter */}
              <div className="mb-6">
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Brand
                </label>
                <input
                  type="text"
                  placeholder="Enter brand name (e.g. Hankook, Michelin)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  value={filters.manufacturer || ''}
                  onChange={(e) => handleFilterChange({ manufacturer: e.target.value || undefined })}
                />
              </div>

              {/* Price range filter */}
              <div className="mb-6">
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Price Range
                </label>
                <div className="flex gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    value={filters.priceRange?.min || ''}
                    onChange={(e) => {
                      const min = e.target.value ? Number(e.target.value) : undefined;
                      handleFilterChange({
                        priceRange: { ...filters.priceRange, min } as any
                      });
                    }}
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    value={filters.priceRange?.max || ''}
                    onChange={(e) => {
                      const max = e.target.value ? Number(e.target.value) : undefined;
                      handleFilterChange({
                        priceRange: { ...filters.priceRange, max } as any
                      });
                    }}
                  />
                </div>
              </div>

              {/* Availability Status filter */}
              <div className="mb-6">
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Availability Status
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.inStock || false}
                      onChange={(e) => handleFilterChange({ inStock: e.target.checked || undefined })}
                      className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">
                      In Stock Only
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Sort and results info */}
            <div className="flex items-center justify-between mb-6">
              <div className="text-sm text-gray-600">
                {searchResult && (
                  <>
                    Showing {(currentPage - 1) * productsPerPage + 1}-{Math.min(currentPage * productsPerPage, searchResult.totalCount)} of {searchResult.totalCount} results
                  </>
                )}
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-700">Sort by</span>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="relevance">Relevance</SelectItem>
                      <SelectItem value="price_low">Price: Low to High</SelectItem>
                      <SelectItem value="price_high">Price: High to Low</SelectItem>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="newest">Newest</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Loading state */}
            {isLoading && (
              <div className="flex flex-col items-center justify-center py-12">
                <Loader2 className="h-8 w-8 text-[#DC2626] animate-spin mb-4" />
                <h3 className="text-lg font-medium">Searching...</h3>
                <p className="text-gray-600 mt-1">
                  Finding the best products for you
                </p>
              </div>
            )}



            {/* No results */}
            {!isLoading && searchResult && sortedProducts.length === 0 && (
              <div className="text-center py-12">
                <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Results Found
                </h3>
                <p className="text-gray-600 mb-6">
                  No products found for "{query}". Try adjusting your search.
                </p>
                <div className="space-y-2">
                  <p className="text-sm text-gray-500">Try these suggestions:</p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Check your spelling</li>
                    <li>• Use fewer words</li>
                    <li>• Try different search terms</li>
                    <li>• Remove filters to see more results</li>
                  </ul>
                </div>
              </div>
            )}

            {/* Products Display */}
            {!isLoading && sortedProducts.length > 0 && (
              <>
                {viewMode === 'grid' ? (
                  <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 sm:gap-6">
                    {sortedProducts.map((product) => (
                      <AirbnbStyleProductCard
                        key={product.id}
                        product={product}
                        section="retail"
                      />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-3 sm:space-y-4">
                    {sortedProducts.map((product) => (
                      <AutoZoneStyleProductListItem
                        key={product.id}
                        product={product}
                        section="retail"
                      />
                    ))}
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-center mt-8 gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>

                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setCurrentPage(page)}
                          className={currentPage === page ? 'bg-[#DC2626] text-white' : ''}
                        >
                          {page}
                        </Button>
                      );
                    })}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  } catch (error) {
    console.error('🚨 SearchResults Component Error:', error);
    return (
      <div className="min-h-screen bg-red-100 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-red-800 mb-4">🚨 CRITICAL ERROR IN SEARCH RESULTS</h1>
          <div className="bg-white p-6 rounded border-2 border-red-300">
            <p className="text-red-700 mb-4">The SearchResults component crashed with an error:</p>
            <pre className="bg-red-50 p-4 rounded text-sm overflow-auto">
              {error instanceof Error ? error.message : String(error)}
            </pre>
            <div className="mt-4">
              <button
                onClick={() => window.location.reload()}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                Reload Page
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

// Main component wrapped with FilterProvider
export default function SearchResults() {
  return (
    <FilterProvider>
      <SearchResultsContent />
    </FilterProvider>
  );
}

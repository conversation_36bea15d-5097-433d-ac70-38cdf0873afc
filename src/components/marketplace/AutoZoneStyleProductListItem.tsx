import React, { useState, useEffect } from 'react';
import { Heart, ShoppingCart, Star, Package, Truck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { OptimizedImage } from '@/components/ui/OptimizedImage';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useCart } from '@/contexts/CartContext';
import { useToast } from '@/components/ui/use-toast';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { toggleWishlist, isProductInWishlist } from '@/services/wishlistService';
import { getProductReviewStats } from '@/services/reviewsService';

interface AutoZoneStyleProductListItemProps {
  product: TyreProduct | BrakeProduct;
  section: 'retail' | 'wholesale';
}

export function AutoZoneStyleProductListItem({ product, section }: AutoZoneStyleProductListItemProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isFavorite, setIsFavorite] = useState(false);
  const [isWishlistLoading, setIsWishlistLoading] = useState(false);
  const [reviewStats, setReviewStats] = useState({ averageRating: 0, totalReviews: 0 });
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);
  const { addItem } = useCart();

  // Check consumer authentication and load wishlist status
  useEffect(() => {
    const checkAuthAndWishlist = async () => {
      try {
        const phoneSession = localStorage.getItem('phone_auth_session');
        const isAuth = phoneSession && JSON.parse(phoneSession).profile?.role === 'consumer';
        setIsConsumerAuthenticated(!!isAuth);

        if (isAuth) {
          const result = await isProductInWishlist(product.id);
          if (result.success) {
            setIsFavorite(result.isInWishlist || false);
          }
        }
      } catch (error) {
        console.error('Error checking auth/wishlist:', error);
      }
    };

    checkAuthAndWishlist();
  }, [product.id]);

  // Load review statistics
  useEffect(() => {
    const loadReviewStats = async () => {
      try {
        const result = await getProductReviewStats(product.id);
        if (result.success && result.stats) {
          setReviewStats({
            averageRating: result.stats.average_rating,
            totalReviews: result.stats.total_reviews
          });
        }
      } catch (error) {
        console.error('Error loading review stats:', error);
      }
    };

    loadReviewStats();
  }, [product.id]);

  // Helper function to convert product ID string to number for cart
  const getCartItemId = (productId: string): number => {
    let hash = 0;
    for (let i = 0; i < productId.length; i++) {
      const char = productId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  };

  // Format price with Algerian Dinar
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Toggle favorite status
  const toggleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isConsumerAuthenticated) {
      const authModal = document.getElementById('auth-modal-trigger');
      if (authModal) {
        authModal.click();
      }
      return;
    }

    setIsWishlistLoading(true);
    try {
      const result = await toggleWishlist({
        product_id: product.id,
        product_name: product.name,
        product_image: product.primaryImage,
        product_price: getProductPrice(),
        product_manufacturer: product.manufacturer || (product as any).brand
      });

      if (result.success) {
        const newFavoriteState = result.action === 'added';
        setIsFavorite(newFavoriteState);
        toast.success(
          newFavoriteState ? t('marketplace.addedToWishlist') : t('marketplace.removedFromWishlist')
        );
        window.dispatchEvent(new CustomEvent('wishlist:updated'));
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      toast.error(t('marketplace.wishlistError'));
    } finally {
      setIsWishlistLoading(false);
    }
  };

  // Navigate to product page
  const handleProductClick = () => {
    navigate(`/${product.id}`);
  };

  // Add to cart
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const effectiveQuantity = section === 'wholesale' ? (getMinOrderQuantity() || 1) : 1;

    const cartItem = {
      id: getCartItemId(product.id),
      name: product.name,
      price: section === 'retail' ? (product.retailPrice || 0) : (product.wholesalePricingTiers?.[0]?.price || 0),
      quantity: effectiveQuantity,
      image: product.primaryImage || '/placeholder.svg',
      category: product.category,
      subcategory: product.subcategory,
      manufacturer: product.manufacturer,
      brand: (product as any).brand,
      partArticleNumber: product.partArticleNumber,
      supplierName: product.supplierName,
      supplierAccountId: product.supplierAccountId,
      originalProductId: product.id,
      shippingOrigin: product.shippingOrigin || 'Location TBD',
      marketplaceSection: section,
      wholesalePricingTiers: product.wholesalePricingTiers,
      retailPrice: product.retailPrice
    };

    addItem(cartItem);
  };

  // Determine if product is out of stock
  const isOutOfStock = product.status === 'out_of_stock' || product.stockQuantity <= 0;

  // Get product specifications
  const getProductSpecs = () => {
    if (product.category === 'tyres') {
      const tyreProduct = product as TyreProduct;
      if (tyreProduct.width && tyreProduct.aspectRatio && tyreProduct.rimDiameter) {
        return `${tyreProduct.width}/${tyreProduct.aspectRatio}R${tyreProduct.rimDiameter}`;
      }
    }
    return product.subcategory || '';
  };

  // Get product price
  const getProductPrice = () => {
    if (section === 'retail') {
      return product.retailPrice || 0;
    } else {
      return product.wholesalePricingTiers?.[0]?.price || 0;
    }
  };

  // Get minimum order quantity for wholesale
  const getMinOrderQuantity = () => {
    if (section === 'wholesale') {
      return product.minimumOrderQuantity ||
        (product.wholesalePricingTiers?.[0]?.minQuantity) ||
        1;
    }
    return null;
  };

  return (
    <div
      className="group cursor-pointer bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:border-gray-300"
      onClick={handleProductClick}
    >
      <div className="flex items-start gap-4">
        {/* Product Image - Left Side */}
        <div className="flex-shrink-0 w-24 h-24 relative">
          <OptimizedImage
            src={product.primaryImage || '/placeholder.svg'}
            alt={product.name}
            className="w-full h-full object-cover rounded-lg"
            lazy={true}
            priority={false}
            quality={80}
            width={96}
            height={96}
          />
          {isOutOfStock && (
            <div className="absolute inset-0 bg-black/60 flex items-center justify-center rounded-lg">
              <Badge variant="destructive" className="text-xs px-2 py-1">
                {t('marketplace.outOfStock')}
              </Badge>
            </div>
          )}
        </div>

        {/* Product Details - Middle Section */}
        <div className="flex-1 min-w-0">
          {/* Product Name and Brand */}
          <div className="mb-2">
            <h3 className="text-lg font-semibold text-gray-900 line-clamp-1 mb-1">
              {product.name}
            </h3>
            <p className="text-sm text-gray-600">
              {product.manufacturer || (product as any).brand || 'Premium Brand'}
            </p>
          </div>

          {/* Part Number and Specifications */}
          <div className="space-y-1 mb-3">
            {product.partArticleNumber && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Package className="h-4 w-4" />
                <span>Part #: {product.partArticleNumber}</span>
              </div>
            )}
            {getProductSpecs() && (
              <div className="text-sm text-gray-600">
                Specifications: {getProductSpecs()}
              </div>
            )}
            {product.supplierName && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Truck className="h-4 w-4" />
                <span>Supplier: {product.supplierName}</span>
              </div>
            )}
          </div>

          {/* Reviews and Rating */}
          <div className="flex items-center gap-4 mb-2">
            <button
              className="flex items-center gap-1 hover:opacity-75 transition-opacity"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(`/${product.id}#reviews-section`);
              }}
            >
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={cn(
                      "h-4 w-4",
                      i < Math.floor(reviewStats.averageRating)
                        ? "fill-yellow-400 text-yellow-400"
                        : "fill-gray-200 text-gray-200"
                    )}
                  />
                ))}
              </div>
              <span className="text-sm font-medium ml-1">
                {reviewStats.totalReviews > 0
                  ? `${reviewStats.averageRating.toFixed(1)} (${reviewStats.totalReviews} reviews)`
                  : 'No reviews yet'
                }
              </span>
            </button>
          </div>

          {/* Availability Status */}
          <div className="text-sm">
            {isOutOfStock ? (
              <span className="text-red-600 font-medium">{t('marketplace.outOfStock')}</span>
            ) : (
              <span className="text-green-600 font-medium">✓ {t('marketplace.inStock')}</span>
            )}
          </div>
        </div>

        {/* Price and Actions - Right Side */}
        <div className="flex-shrink-0 text-right">
          {/* Price */}
          <div className="mb-4">
            <div className="text-2xl font-bold text-gray-900">
              {formatPrice(getProductPrice())}
            </div>
            {section === 'retail' && (
              <div className="text-sm text-gray-600">/ {t('marketplace.piece')}</div>
            )}
            {section === 'wholesale' && getMinOrderQuantity() && (
              <div className="text-xs text-gray-500 mt-1">
                {t('marketplace.minOrder')}: {getMinOrderQuantity()} {t('marketplace.units')}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Button
              size="sm"
              className={cn(
                "w-full transition-all duration-200",
                "bg-[#DC2626] hover:bg-[#B91C1C] text-white",
                "hover:shadow-md active:scale-95"
              )}
              onClick={handleAddToCart}
              disabled={isOutOfStock}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {isOutOfStock ? t('marketplace.outOfStock') : t('marketplace.addToCart')}
            </Button>

            <Button
              variant="outline"
              size="sm"
              className={cn(
                "w-full transition-all duration-200",
                isFavorite
                  ? "border-red-500 text-red-500 hover:bg-red-50"
                  : "border-gray-300 text-gray-600 hover:border-red-500 hover:text-red-500",
                isWishlistLoading && "opacity-50 cursor-not-allowed"
              )}
              onClick={toggleFavorite}
              disabled={isWishlistLoading}
            >
              <Heart className={cn(
                "h-4 w-4 mr-2 transition-all duration-200",
                isFavorite && "fill-current",
                isWishlistLoading && "animate-pulse"
              )} />
              {isFavorite ? t('marketplace.removeFromWishlist') : t('marketplace.addToWishlist')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

-- =============================================================================
-- AROUZ MARKET - Check Search Tables Before Migration
-- =============================================================================
-- Run this BEFORE the search analytics migration to check for conflicts
-- =============================================================================

-- Check if search_analytics table already exists
SELECT 
    'SEARCH_ANALYTICS_EXISTS' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'search_analytics' AND table_schema = 'public'
        ) THEN 'YES - TABLE EXISTS'
        ELSE 'NO - TABLE DOES NOT EXIST'
    END as result;

-- Check if search_suggestions_cache table already exists
SELECT 
    'SEARCH_SUGGESTIONS_CACHE_EXISTS' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'search_suggestions_cache' AND table_schema = 'public'
        ) THEN 'YES - TABLE EXISTS'
        ELSE 'NO - TABLE DOES NOT EXIST'
    END as result;

-- Check if any search-related views exist
SELECT 
    'SEARCH_VIEWS_EXIST' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.views 
            WHERE view_name IN ('search_performance_summary', 'popular_search_queries', 'zero_results_queries')
            AND table_schema = 'public'
        ) THEN 'YES - VIEWS EXIST'
        ELSE 'NO - VIEWS DO NOT EXIST'
    END as result;

-- List all existing tables that contain 'search' in the name
SELECT 
    'EXISTING_SEARCH_TABLES' as check_type,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_name LIKE '%search%' 
    AND table_schema = 'public'
ORDER BY table_name;

-- List all existing views that contain 'search' in the name
SELECT 
    'EXISTING_SEARCH_VIEWS' as check_type,
    view_name
FROM information_schema.views 
WHERE view_name LIKE '%search%' 
    AND table_schema = 'public'
ORDER BY view_name;

-- Check products table structure to ensure compatibility
SELECT 
    'PRODUCTS_TABLE_STRUCTURE' as check_type,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'products' 
    AND table_schema = 'public'
    AND column_name IN ('id', 'name', 'manufacturer', 'category', 'subcategory', 'part_article_number', 'status', 'created_at', 'updated_at')
ORDER BY ordinal_position;

-- Check if we have any products to search
SELECT 
    'PRODUCTS_COUNT' as check_type,
    COUNT(*) as total_products,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_products
FROM products;

-- Show sample products to verify data structure
SELECT 
    'SAMPLE_PRODUCTS' as check_type,
    id,
    name,
    manufacturer,
    category,
    status
FROM products 
WHERE status = 'active'
LIMIT 3;

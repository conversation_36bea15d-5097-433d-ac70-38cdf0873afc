/**
 * TecDoc API Test Script
 * Tests different endpoint configurations to identify the correct API format
 */

// Test configuration
const RAPIDAPI_KEY = '**************************************************';
const TECDOC_API_HOST = 'ronhartman-tecdoc-catalog.p.rapidapi.com';

// Test part numbers from our sample data
const TEST_PART_NUMBERS = [
  '34116792217',  // BMW brake disc
  'A0001802609',  // Mercedes part
  'BP1234',       // Generic brake pad
  '1K0615301AA'   // VW/Audi part
];

/**
 * Test different API endpoint configurations
 */
async function testTecDocEndpoints() {
  console.log('🔍 Testing TecDoc API Endpoints...\n');

  // Configuration 1: Current implementation
  console.log('📡 Test 1: Current endpoint format');
  await testEndpoint(
    `https://${TECDOC_API_HOST}/search/article/34116792217`,
    'Current Implementation'
  );

  // Configuration 2: RapidAPI standard format
  console.log('\n📡 Test 2: RapidAPI standard format');
  await testEndpoint(
    `https://${TECDOC_API_HOST}/search`,
    'RapidAPI Standard',
    { articleNumber: '34116792217' }
  );

  // Configuration 3: Search by article number endpoint
  console.log('\n📡 Test 3: Search by article number');
  await testEndpoint(
    `https://${TECDOC_API_HOST}/searchByArticleNumber`,
    'Search By Article Number',
    { articleNumber: '34116792217' }
  );

  // Configuration 4: Alternative search format
  console.log('\n📡 Test 4: Alternative search format');
  await testEndpoint(
    `https://${TECDOC_API_HOST}/articles/search`,
    'Alternative Search',
    { query: '34116792217' }
  );

  // Configuration 5: Direct article lookup
  console.log('\n📡 Test 5: Direct article lookup');
  await testEndpoint(
    `https://${TECDOC_API_HOST}/articles/34116792217`,
    'Direct Article Lookup'
  );
}

/**
 * Test a specific endpoint configuration
 */
async function testEndpoint(url, description, params = null) {
  try {
    console.log(`🔍 Testing: ${description}`);
    console.log(`📡 URL: ${url}`);
    
    const options = {
      method: params ? 'POST' : 'GET',
      headers: {
        'X-RapidAPI-Key': RAPIDAPI_KEY,
        'X-RapidAPI-Host': TECDOC_API_HOST,
        'Content-Type': 'application/json'
      }
    };

    if (params) {
      options.body = JSON.stringify(params);
      console.log(`📤 Body: ${JSON.stringify(params)}`);
    }

    const response = await fetch(url, options);
    
    console.log(`📥 Status: ${response.status} (${response.statusText})`);
    console.log(`📋 Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Success! Data:`, JSON.stringify(data, null, 2));
      return { success: true, data };
    } else {
      const errorText = await response.text();
      console.log(`❌ Error Response: ${errorText}`);
      return { success: false, error: errorText, status: response.status };
    }

  } catch (error) {
    console.log(`❌ Request Failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test authentication and API key validity
 */
async function testAuthentication() {
  console.log('\n🔐 Testing API Authentication...\n');

  try {
    // Test with a simple endpoint to verify authentication
    const response = await fetch(`https://${TECDOC_API_HOST}/`, {
      method: 'GET',
      headers: {
        'X-RapidAPI-Key': RAPIDAPI_KEY,
        'X-RapidAPI-Host': TECDOC_API_HOST
      }
    });

    console.log(`📥 Auth Test Status: ${response.status} (${response.statusText})`);
    
    if (response.status === 401) {
      console.log('❌ Authentication Failed - Invalid API Key');
      return false;
    } else if (response.status === 403) {
      console.log('❌ Access Forbidden - Check subscription or permissions');
      return false;
    } else if (response.status === 429) {
      console.log('⏰ Rate Limit Exceeded');
      return false;
    } else {
      console.log('✅ Authentication appears valid');
      return true;
    }

  } catch (error) {
    console.log(`❌ Auth Test Failed: ${error.message}`);
    return false;
  }
}

/**
 * Check RapidAPI subscription status
 */
async function checkSubscriptionStatus() {
  console.log('\n📊 Checking RapidAPI Subscription Status...\n');

  try {
    // This endpoint might provide subscription info
    const response = await fetch(`https://${TECDOC_API_HOST}/status`, {
      method: 'GET',
      headers: {
        'X-RapidAPI-Key': RAPIDAPI_KEY,
        'X-RapidAPI-Host': TECDOC_API_HOST
      }
    });

    console.log(`📥 Subscription Status: ${response.status} (${response.statusText})`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`📊 Subscription Data:`, JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log(`📊 Subscription Response: ${errorText}`);
    }

  } catch (error) {
    console.log(`❌ Subscription Check Failed: ${error.message}`);
  }
}

/**
 * Main test execution
 */
async function runTests() {
  console.log('🚀 TecDoc API Diagnostic Test Suite\n');
  console.log('=' * 50);

  // Test authentication first
  const authValid = await testAuthentication();
  
  if (!authValid) {
    console.log('\n❌ Authentication failed. Please check:');
    console.log('1. RapidAPI key is valid');
    console.log('2. Subscription is active');
    console.log('3. API permissions are correct');
    return;
  }

  // Check subscription
  await checkSubscriptionStatus();

  // Test different endpoints
  await testTecDocEndpoints();

  console.log('\n🎯 Test Summary:');
  console.log('- Check console output above for successful endpoint');
  console.log('- Look for ✅ Success indicators');
  console.log('- Note any authentication or subscription issues');
}

// Run the tests
runTests().catch(console.error);

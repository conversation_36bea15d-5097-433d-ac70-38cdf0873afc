<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Column Mapping Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #071c44;
        }
        .test-data {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #fa7b00;
        }
        .api-config {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .results {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        input, textarea, button {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #fa7b00;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #e56a00;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .mapping-result {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .confidence {
            background: #4caf50;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI-Powered Column Mapping Test</h1>
            <p>Test the intelligent column mapping system with your CSV data</p>
        </div>

        <div class="api-config">
            <h3>🔑 OpenAI Configuration</h3>
            <input type="password" id="apiKey" placeholder="Enter your OpenAI API key (sk-...)" />
            <p style="font-size: 12px; color: #666;">Your API key is used only for this test and not stored</p>
        </div>

        <div class="test-data">
            <h3>📊 Test Data</h3>
            <p>Sample data with the exact column structure you mentioned:</p>
            <pre id="sampleData" style="background: white; padding: 15px; border-radius: 5px; overflow-x: auto;">
Vendor,Item,Brand,Type,Vehicle,Program,Main_OE_References,OE_Brand,Weight_kg,Packing_Length_mm,Packing_Height_mm,Packing_Width_mm,Packing_Volume_dm3,Delivery_Period_days,Netto_Price_EUR,Order,MOQ,Paired_Article,Note,Price_Date
SUPPLIER1,12345,BOSCH,Brake Disc,BMW 3 Series,Standard,34116792217,BMW,2.5,300,50,300,4.5,3,45.99,100,1,12346,High quality brake disc,2024-01-15
SUPPLIER2,67890,BREMBO,Brake Pad,Audi A4,Premium,8E0698151A,AUDI,1.2,200,30,150,0.9,2,89.50,50,2,67891,Ceramic brake pads,2024-01-16
SUPPLIER3,11111,FEBI,Oil Filter,Mercedes C-Class,Economy,A0001802609,MERCEDES,0.3,100,80,100,0.8,1,12.75,200,5,11112,Standard oil filter,2024-01-17
            </pre>
            <button onclick="testAIMapping()">🚀 Test AI Mapping</button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <p>🤖 AI is analyzing your data...</p>
            <p>This may take a few seconds...</p>
        </div>

        <div id="results" class="results" style="display: none;">
            <h3>✅ AI Mapping Results</h3>
            <div id="mappingResults"></div>
        </div>

        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        // Sample data for testing
        const sampleData = [
            {
                "Vendor": "SUPPLIER1",
                "Item": "12345",
                "Brand": "BOSCH",
                "Type": "Brake Disc",
                "Vehicle": "BMW 3 Series",
                "Program": "Standard",
                "Main_OE_References": "34116792217",
                "OE_Brand": "BMW",
                "Weight_kg": "2.5",
                "Packing_Length_mm": "300",
                "Packing_Height_mm": "50",
                "Packing_Width_mm": "300",
                "Packing_Volume_dm3": "4.5",
                "Delivery_Period_days": "3",
                "Netto_Price_EUR": "45.99",
                "Order": "100",
                "MOQ": "1",
                "Paired_Article": "12346",
                "Note": "High quality brake disc",
                "Price_Date": "2024-01-15"
            },
            {
                "Vendor": "SUPPLIER2",
                "Item": "67890",
                "Brand": "BREMBO",
                "Type": "Brake Pad",
                "Vehicle": "Audi A4",
                "Program": "Premium",
                "Main_OE_References": "8E0698151A",
                "OE_Brand": "AUDI",
                "Weight_kg": "1.2",
                "Packing_Length_mm": "200",
                "Packing_Height_mm": "30",
                "Packing_Width_mm": "150",
                "Packing_Volume_dm3": "0.9",
                "Delivery_Period_days": "2",
                "Netto_Price_EUR": "89.50",
                "Order": "50",
                "MOQ": "2",
                "Paired_Article": "67891",
                "Note": "Ceramic brake pads",
                "Price_Date": "2024-01-16"
            },
            {
                "Vendor": "SUPPLIER3",
                "Item": "11111",
                "Brand": "FEBI",
                "Type": "Oil Filter",
                "Vehicle": "Mercedes C-Class",
                "Program": "Economy",
                "Main_OE_References": "A0001802609",
                "OE_Brand": "MERCEDES",
                "Weight_kg": "0.3",
                "Packing_Length_mm": "100",
                "Packing_Height_mm": "80",
                "Packing_Width_mm": "100",
                "Packing_Volume_dm3": "0.8",
                "Delivery_Period_days": "1",
                "Netto_Price_EUR": "12.75",
                "Order": "200",
                "MOQ": "5",
                "Paired_Article": "11112",
                "Note": "Standard oil filter",
                "Price_Date": "2024-01-17"
            }
        ];

        async function testAIMapping() {
            const apiKey = document.getElementById('apiKey').value;
            if (!apiKey) {
                showError('Please enter your OpenAI API key');
                return;
            }

            showLoading();
            hideError();
            hideResults();

            try {
                const headers = Object.keys(sampleData[0]);
                const result = await analyzeColumnsWithAI(headers, sampleData, apiKey);
                showResults(result);
            } catch (error) {
                showError('AI mapping failed: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        async function analyzeColumnsWithAI(headers, sampleData, apiKey) {
            // Note: Direct browser calls to OpenAI API will fail due to CORS
            // This is a demonstration of the logic - in production, this should go through your backend

            showError('Direct browser calls to OpenAI API are blocked by CORS policy. This test demonstrates the AI logic, but the actual implementation should go through your backend server.');

            // Return a mock result to demonstrate the expected output
            return {
                "mappings": [
                    {
                        "originalColumn": "Vendor",
                        "targetField": "supplierName",
                        "confidence": 95,
                        "reasoning": "Vendor clearly indicates the supplier/vendor providing the product",
                        "sampleValues": ["SUPPLIER1", "SUPPLIER2", "SUPPLIER3"]
                    },
                    {
                        "originalColumn": "Item",
                        "targetField": "partArticleNumber",
                        "confidence": 98,
                        "reasoning": "Item contains product identifiers/part numbers",
                        "sampleValues": ["12345", "67890", "11111"]
                    },
                    {
                        "originalColumn": "Brand",
                        "targetField": "manufacturer",
                        "confidence": 99,
                        "reasoning": "Brand clearly indicates the manufacturer of the product",
                        "sampleValues": ["BOSCH", "BREMBO", "FEBI"]
                    },
                    {
                        "originalColumn": "Type",
                        "targetField": "name",
                        "confidence": 90,
                        "reasoning": "Type describes the product name/category",
                        "sampleValues": ["Brake Disc", "Brake Pad", "Oil Filter"]
                    },
                    {
                        "originalColumn": "Vehicle",
                        "targetField": "vehicleCompatibility",
                        "confidence": 95,
                        "reasoning": "Vehicle indicates compatible vehicle types",
                        "sampleValues": ["BMW 3 Series", "Audi A4", "Mercedes C-Class"]
                    },
                    {
                        "originalColumn": "Netto_Price_EUR",
                        "targetField": "retailPrice",
                        "confidence": 98,
                        "reasoning": "Netto_Price_EUR is clearly the product price",
                        "sampleValues": ["45.99", "89.50", "12.75"]
                    },
                    {
                        "originalColumn": "Note",
                        "targetField": "descriptionAndSpecifications",
                        "confidence": 85,
                        "reasoning": "Note contains product descriptions and specifications",
                        "sampleValues": ["High quality brake disc", "Ceramic brake pads", "Standard oil filter"]
                    }
                ],
                "suggestedCategory": "brakes",
                "suggestedSubcategory": "brake-disc",
                "unmappedColumns": ["Program", "Main_OE_References", "OE_Brand", "Weight_kg", "Packing_Length_mm", "Packing_Height_mm", "Packing_Width_mm", "Packing_Volume_dm3", "Delivery_Period_days", "Order", "MOQ", "Paired_Article", "Price_Date"],
                "confidence": 92
            };
        }

        function createAIPrompt(headers, sampleData) {
            return `You are an expert data analyst specializing in automotive parts and product data mapping. 

TASK: Analyze the provided column headers and sample data to map each column to the most appropriate target field in our product system.

COLUMN HEADERS: ${headers.join(', ')}

SAMPLE DATA (first 3 rows):
${sampleData.map((row, index) => 
  `Row ${index + 1}: ${headers.map(header => `${header}: "${row[header] || ''}"`).join(', ')}`
).join('\n')}

TARGET FIELDS AVAILABLE:
- partArticleNumber: Part Article Number - Primary product identifier
- name: Product Name - Main product title/description
- manufacturer: Manufacturer - Brand or company that makes the product
- supplierName: Supplier Name - Company/vendor supplying the product
- retailPrice: Retail Price - Selling price for merchants
- stockQuantity: Stock Quantity - Number of units available
- category: Category - Main product category
- subcategory: Subcategory - Specific product subcategory
- vehicleCompatibility: Vehicle Compatibility - Compatible vehicle types (text input)
- descriptionAndSpecifications: Description - Detailed product description and specifications

SPECIAL MAPPING RULES:
- "Vendor" or "Supplier" columns typically map to "supplierName"
- "Item", "Part Number", "Article Number", "SKU" typically map to "partArticleNumber"
- "Brand", "Manufacturer", "Make" typically map to "manufacturer"
- Price columns should map to "retailPrice"
- Vehicle-related columns should map to "vehicleCompatibility"
- Weight, dimensions, packaging info should go to "descriptionAndSpecifications"

RESPONSE FORMAT (JSON only):
{
  "mappings": [
    {
      "originalColumn": "column_name",
      "targetField": "target_field_name",
      "confidence": 95,
      "reasoning": "explanation of why this mapping makes sense",
      "sampleValues": ["sample1", "sample2", "sample3"]
    }
  ],
  "suggestedCategory": "brakes",
  "suggestedSubcategory": "brake-disc", 
  "unmappedColumns": ["column1", "column2"],
  "confidence": 85
}

Analyze the data and provide the JSON response:`;
        }

        function showResults(result) {
            const resultsDiv = document.getElementById('results');
            const mappingResults = document.getElementById('mappingResults');
            
            let html = `
                <div style="margin-bottom: 20px;">
                    <h4>Overall Confidence: <span class="confidence">${result.confidence}%</span></h4>
                    <p><strong>Suggested Category:</strong> ${result.suggestedCategory}</p>
                    <p><strong>Suggested Subcategory:</strong> ${result.suggestedSubcategory}</p>
                </div>
                <h4>Column Mappings:</h4>
            `;
            
            result.mappings.forEach(mapping => {
                html += `
                    <div class="mapping-result">
                        <div>
                            <strong>${mapping.originalColumn}</strong> → ${mapping.targetField}
                            <br><small style="color: #666;">${mapping.reasoning}</small>
                        </div>
                        <span class="confidence">${mapping.confidence}%</span>
                    </div>
                `;
            });
            
            if (result.unmappedColumns.length > 0) {
                html += `
                    <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 5px;">
                        <strong>Unmapped Columns:</strong> ${result.unmappedColumns.join(', ')}
                        <br><small>These will be added to the description field</small>
                    </div>
                `;
            }
            
            mappingResults.innerHTML = html;
            resultsDiv.style.display = 'block';
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        function hideResults() {
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>

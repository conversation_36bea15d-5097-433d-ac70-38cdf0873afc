<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TecDoc API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .log {
            max-height: 300px;
            overflow-y: auto;
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 TecDoc API Diagnostic Test</h1>
        
        <div class="test-section info">
            <h3>📋 Test Configuration</h3>
            <p><strong>API Host:</strong> ronhartman-tecdoc-catalog.p.rapidapi.com</p>
            <p><strong>API Key:</strong> ************************************************** (from code)</p>
            <p><strong>Test Part Numbers:</strong> 34116792217, A0001802609, BP1234, 1K0615301AA</p>
        </div>

        <div class="test-section">
            <h3>🔐 Authentication Test</h3>
            <button onclick="testAuthentication()">Test API Authentication</button>
            <div id="auth-result"></div>
        </div>

        <div class="test-section">
            <h3>📡 Endpoint Configuration Tests</h3>
            <button onclick="testAllEndpoints()">Test All Endpoint Configurations</button>
            <div id="endpoint-results"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Individual Part Number Tests</h3>
            <button onclick="testPartNumbers()">Test Sample Part Numbers</button>
            <div id="part-results"></div>
        </div>

        <div class="test-section">
            <h3>📊 RapidAPI Documentation Analysis</h3>
            <button onclick="analyzeRapidAPIDoc()">Analyze Available Endpoints</button>
            <div id="doc-analysis"></div>
        </div>

        <div class="test-section">
            <h3>📝 Console Log</h3>
            <div id="console-log" class="log"></div>
        </div>
    </div>

    <script>
        // Configuration
        const RAPIDAPI_KEY = '**************************************************';
        const TECDOC_API_HOST = 'ronhartman-tecdoc-catalog.p.rapidapi.com';
        const TEST_PART_NUMBERS = ['34116792217', 'A0001802609', 'BP1234', '1K0615301AA'];

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('console-log');
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : type === 'warning' ? '#ffd43b' : '#00ff00';
            logElement.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // Test authentication
        async function testAuthentication() {
            log('🔐 Testing API Authentication...', 'info');
            const resultDiv = document.getElementById('auth-result');
            
            try {
                // Test basic endpoint access
                const response = await fetch(`https://${TECDOC_API_HOST}/`, {
                    method: 'GET',
                    headers: {
                        'X-RapidAPI-Key': RAPIDAPI_KEY,
                        'X-RapidAPI-Host': TECDOC_API_HOST
                    }
                });

                log(`📥 Auth Response: ${response.status} (${response.statusText})`, 'info');

                if (response.status === 401) {
                    resultDiv.innerHTML = '<div class="error">❌ Authentication Failed - Invalid API Key</div>';
                    log('❌ Authentication Failed - Invalid API Key', 'error');
                } else if (response.status === 403) {
                    resultDiv.innerHTML = '<div class="error">❌ Access Forbidden - Check subscription or permissions</div>';
                    log('❌ Access Forbidden - Check subscription', 'error');
                } else if (response.status === 429) {
                    resultDiv.innerHTML = '<div class="warning">⏰ Rate Limit Exceeded</div>';
                    log('⏰ Rate Limit Exceeded', 'warning');
                } else {
                    resultDiv.innerHTML = '<div class="success">✅ Authentication appears valid</div>';
                    log('✅ Authentication appears valid', 'success');
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Auth Test Failed: ${error.message}</div>`;
                log(`❌ Auth Test Failed: ${error.message}`, 'error');
            }
        }

        // Test different endpoint configurations
        async function testAllEndpoints() {
            log('📡 Testing different endpoint configurations...', 'info');
            const resultDiv = document.getElementById('endpoint-results');
            resultDiv.innerHTML = '<div class="info">Testing endpoints...</div>';

            const endpoints = [
                {
                    name: 'Current Implementation',
                    url: `https://${TECDOC_API_HOST}/search/article/34116792217`,
                    method: 'GET'
                },
                {
                    name: 'RapidAPI Standard Search',
                    url: `https://${TECDOC_API_HOST}/search`,
                    method: 'GET',
                    params: '?articleNumber=34116792217'
                },
                {
                    name: 'Search by Article Number',
                    url: `https://${TECDOC_API_HOST}/searchByArticleNumber`,
                    method: 'POST',
                    body: { articleNumber: '34116792217' }
                },
                {
                    name: 'Articles Search',
                    url: `https://${TECDOC_API_HOST}/articles/search`,
                    method: 'GET',
                    params: '?query=34116792217'
                },
                {
                    name: 'Direct Article Lookup',
                    url: `https://${TECDOC_API_HOST}/articles/34116792217`,
                    method: 'GET'
                }
            ];

            let results = '<h4>Endpoint Test Results:</h4>';

            for (const endpoint of endpoints) {
                try {
                    log(`🔍 Testing: ${endpoint.name}`, 'info');
                    
                    const url = endpoint.url + (endpoint.params || '');
                    const options = {
                        method: endpoint.method,
                        headers: {
                            'X-RapidAPI-Key': RAPIDAPI_KEY,
                            'X-RapidAPI-Host': TECDOC_API_HOST,
                            'Content-Type': 'application/json'
                        }
                    };

                    if (endpoint.body) {
                        options.body = JSON.stringify(endpoint.body);
                    }

                    const response = await fetch(url, options);
                    
                    log(`📥 ${endpoint.name}: ${response.status} (${response.statusText})`, 
                        response.ok ? 'success' : 'error');

                    if (response.ok) {
                        const data = await response.json();
                        results += `<div class="success">✅ <strong>${endpoint.name}</strong>: Success (${response.status})<br>
                                   <pre>${JSON.stringify(data, null, 2).substring(0, 200)}...</pre></div>`;
                    } else {
                        const errorText = await response.text();
                        results += `<div class="error">❌ <strong>${endpoint.name}</strong>: Failed (${response.status})<br>
                                   <pre>${errorText.substring(0, 200)}...</pre></div>`;
                    }

                } catch (error) {
                    log(`❌ ${endpoint.name}: ${error.message}`, 'error');
                    results += `<div class="error">❌ <strong>${endpoint.name}</strong>: Error<br>
                               <pre>${error.message}</pre></div>`;
                }
            }

            resultDiv.innerHTML = results;
        }

        // Test individual part numbers
        async function testPartNumbers() {
            log('🧪 Testing individual part numbers...', 'info');
            const resultDiv = document.getElementById('part-results');
            resultDiv.innerHTML = '<div class="info">Testing part numbers...</div>';

            let results = '<h4>Part Number Test Results:</h4>';

            for (const partNumber of TEST_PART_NUMBERS) {
                try {
                    log(`🔍 Testing part number: ${partNumber}`, 'info');
                    
                    // Use the most likely correct endpoint format
                    const response = await fetch(`https://${TECDOC_API_HOST}/search/article/${partNumber}`, {
                        method: 'GET',
                        headers: {
                            'X-RapidAPI-Key': RAPIDAPI_KEY,
                            'X-RapidAPI-Host': TECDOC_API_HOST
                        }
                    });

                    log(`📥 Part ${partNumber}: ${response.status} (${response.statusText})`, 
                        response.ok ? 'success' : 'warning');

                    if (response.ok) {
                        const data = await response.json();
                        results += `<div class="success">✅ <strong>${partNumber}</strong>: Data found<br>
                                   <pre>${JSON.stringify(data, null, 2).substring(0, 300)}...</pre></div>`;
                    } else {
                        const errorText = await response.text();
                        results += `<div class="warning">⚠️ <strong>${partNumber}</strong>: No data (${response.status})<br>
                                   <pre>${errorText.substring(0, 200)}...</pre></div>`;
                    }

                } catch (error) {
                    log(`❌ Part ${partNumber}: ${error.message}`, 'error');
                    results += `<div class="error">❌ <strong>${partNumber}</strong>: Error<br>
                               <pre>${error.message}</pre></div>`;
                }
            }

            resultDiv.innerHTML = results;
        }

        // Analyze RapidAPI documentation
        function analyzeRapidAPIDoc() {
            log('📊 Analyzing RapidAPI documentation...', 'info');
            const resultDiv = document.getElementById('doc-analysis');
            
            resultDiv.innerHTML = `
                <h4>RapidAPI Documentation Analysis:</h4>
                <div class="info">
                    <p><strong>Based on the RapidAPI interface screenshot:</strong></p>
                    <ul>
                        <li>✅ API Host: ronhartman-tecdoc-catalog.p.rapidapi.com (Correct)</li>
                        <li>✅ Available endpoints visible in left panel</li>
                        <li>🔍 "Search by Article Number & Supplier ID" endpoint available</li>
                        <li>📋 Request URL shows: rapidapi.com (proxy URL)</li>
                        <li>🔑 X-RapidAPI-Key header required</li>
                    </ul>
                    
                    <p><strong>Recommended Actions:</strong></p>
                    <ol>
                        <li>Check RapidAPI subscription status for this API</li>
                        <li>Verify the exact endpoint path from the API documentation</li>
                        <li>Test with the "Search by Article Number" endpoint specifically</li>
                        <li>Check if additional parameters are required (Supplier ID, etc.)</li>
                    </ol>
                    
                    <p><strong>Next Steps:</strong></p>
                    <ul>
                        <li>🔗 Visit: <a href="https://rapidapi.com/ronhartman/api/tecdoc-catalog" target="_blank">TecDoc Catalog API on RapidAPI</a></li>
                        <li>📖 Review the API documentation and endpoint specifications</li>
                        <li>🔑 Verify subscription and API key permissions</li>
                        <li>🧪 Test with the exact endpoint format from documentation</li>
                    </ul>
                </div>
            `;
        }

        // Initialize
        log('🚀 TecDoc API Diagnostic Test Suite Initialized', 'success');
        log('Click the buttons above to run different tests', 'info');
    </script>
</body>
</html>

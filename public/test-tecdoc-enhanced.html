<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced TecDoc API Test - Clear "Not Found" Indicators</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #071c44;
            text-align: center;
            margin-bottom: 10px;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-style: italic;
        }
        .rate-limit-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .rate-limit-info h4 {
            color: #856404;
            margin-top: 0;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #fa7b00;
            margin-top: 0;
        }
        button {
            background-color: #fa7b00;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #e66a00;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .test-result-item {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-family: 'Courier New', monospace;
        }
        .test-result-success {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .test-result-error {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .test-result-not-found {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .test-result-rate-limited {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .console-output {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .part-number-input {
            width: 200px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Enhanced TecDoc API Test Interface</h1>
        <div class="subtitle">Testing Clear "Not Found" Indicators & Rate Limit Management</div>
        
        <div class="rate-limit-info">
            <h4>⚠️ FREE Plan Rate Limits</h4>
            <p><strong>Monthly Limit:</strong> 100 requests per month (hard limit)</p>
            <p><strong>Hourly Limit:</strong> 1000 requests per hour</p>
            <p><strong>Current Plan:</strong> FREE (as shown in RapidAPI screenshots)</p>
            <p><strong>Expected Behavior:</strong> Clear messaging when limits are exceeded</p>
        </div>

        <div class="test-section">
            <h3>🔐 API Configuration Test</h3>
            <p><strong>API Host:</strong> ronhartman-tecdoc-catalog.p.rapidapi.com</p>
            <p><strong>API Key:</strong> **************************************************</p>
            <button onclick="testAPIConfiguration()">Test API Configuration</button>
            <div id="config-result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Enhanced Part Number Tests</h3>
            <p>Testing with sample part numbers to verify clear "Not Found" indicators:</p>
            <button onclick="testEnhancedPartNumbers()">Test All Sample Part Numbers</button>
            <div id="enhanced-results"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Custom Part Number Test</h3>
            <input type="text" id="custom-part" class="part-number-input" placeholder="Enter part number" value="34116792217">
            <button onclick="testCustomPartNumber()">Test Custom Part Number</button>
            <div id="custom-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 Rate Limit Simulation</h3>
            <p>Simulate multiple requests to test rate limit handling:</p>
            <button onclick="simulateRateLimit()">Simulate Multiple Requests</button>
            <div id="rate-limit-result"></div>
        </div>

        <div class="test-section">
            <h3>📝 Console Output</h3>
            <div id="console-output" class="console-output">Console output will appear here...</div>
        </div>
    </div>

    <script>
        // Enhanced console logging
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function logToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            consoleOutput.textContent += logEntry;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole('ERROR: ' + args.join(' '), 'error');
        };

        // TecDoc API configuration
        const RAPIDAPI_KEY = '**************************************************';
        const TECDOC_API_HOST = 'ronhartman-tecdoc-catalog.p.rapidapi.com';
        const TECDOC_API_BASE_URL = 'https://ronhartman-tecdoc-catalog.p.rapidapi.com';
        const DEFAULT_LANG_ID = 4;

        // Sample part numbers for testing
        const SAMPLE_PARTS = [
            { number: '34116792217', description: 'BMW brake disc' },
            { number: 'A0001802609', description: 'Mercedes part' },
            { number: 'BP1234', description: 'Generic brake pad (likely not found)' },
            { number: '1K0615301AA', description: 'VW/Audi part' },
            { number: 'INVALID123', description: 'Invalid part number (should not be found)' }
        ];

        async function testAPIConfiguration() {
            const resultDiv = document.getElementById('config-result');
            resultDiv.innerHTML = '<p>Testing API configuration...</p>';
            
            console.log('🔍 Testing TecDoc API configuration...');
            
            try {
                // Test basic connectivity
                const response = await fetch(`${TECDOC_API_BASE_URL}/articles/search/lang-id/${DEFAULT_LANG_ID}/article-search/TEST123`, {
                    method: 'GET',
                    headers: {
                        'X-RapidAPI-Key': RAPIDAPI_KEY,
                        'X-RapidAPI-Host': TECDOC_API_HOST,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log(`📡 TecDoc API Response: ${response.status} ${response.statusText}`);
                
                if (response.status === 401) {
                    resultDiv.innerHTML = `
                        <div class="test-result-error">
                            <span>❌ Authentication Failed (401)</span>
                            <span>Check API key validity</span>
                        </div>
                    `;
                } else if (response.status === 429) {
                    resultDiv.innerHTML = `
                        <div class="test-result-rate-limited">
                            <span>⏰ Rate Limited (429)</span>
                            <span>Monthly limit exceeded (100/month)</span>
                        </div>
                    `;
                } else if (response.status === 404) {
                    resultDiv.innerHTML = `
                        <div class="test-result-success">
                            <span>✅ API Configuration Valid</span>
                            <span>Test part returned 404 (expected)</span>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result-success">
                            <span>✅ API Accessible</span>
                            <span>Status: ${response.status}</span>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('API configuration test failed:', error);
                resultDiv.innerHTML = `
                    <div class="test-result-error">
                        <span>❌ Network Error</span>
                        <span>${error.message}</span>
                    </div>
                `;
            }
        }

        async function testEnhancedPartNumbers() {
            const resultDiv = document.getElementById('enhanced-results');
            resultDiv.innerHTML = '<p>Testing sample part numbers...</p>';
            
            console.log('🧪 Testing enhanced part number search...');
            
            let results = '';
            
            for (const part of SAMPLE_PARTS) {
                console.log(`🔍 Testing part number: ${part.number} (${part.description})`);
                
                try {
                    const response = await fetch(`${TECDOC_API_BASE_URL}/articles/search/lang-id/${DEFAULT_LANG_ID}/article-search/${encodeURIComponent(part.number)}`, {
                        method: 'GET',
                        headers: {
                            'X-RapidAPI-Key': RAPIDAPI_KEY,
                            'X-RapidAPI-Host': TECDOC_API_HOST,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    console.log(`📡 ${part.number}: Status ${response.status} ${response.statusText}`);
                    
                    if (response.status === 200) {
                        const data = await response.json();
                        if (data && Object.keys(data).length > 0) {
                            results += `
                                <div class="test-result-success">
                                    <span>✅ ${part.number}</span>
                                    <span>Data Found</span>
                                </div>
                            `;
                            console.log(`✅ ${part.number}: Data found`);
                        } else {
                            results += `
                                <div class="test-result-not-found">
                                    <span>📭 ${part.number}</span>
                                    <span>No TecDoc Data Found - Empty response</span>
                                </div>
                            `;
                            console.log(`📭 ${part.number}: No TecDoc Data Found - Empty response`);
                        }
                    } else if (response.status === 404) {
                        results += `
                            <div class="test-result-not-found">
                                <span>📭 ${part.number}</span>
                                <span>No TecDoc Data Found - Part not in database</span>
                            </div>
                        `;
                        console.log(`📭 ${part.number}: No TecDoc Data Found - Part not in database (404)`);
                    } else if (response.status === 429) {
                        results += `
                            <div class="test-result-rate-limited">
                                <span>⏰ ${part.number}</span>
                                <span>Rate Limited - Monthly limit exceeded (100/month)</span>
                            </div>
                        `;
                        console.log(`⏰ ${part.number}: Rate Limited - Monthly limit exceeded (100/month)`);
                        break; // Stop testing if rate limited
                    } else if (response.status === 401) {
                        results += `
                            <div class="test-result-error">
                                <span>❌ ${part.number}</span>
                                <span>Authentication Failed - Check API key</span>
                            </div>
                        `;
                        console.log(`❌ ${part.number}: Authentication Failed - Check API key`);
                        break; // Stop testing if auth failed
                    } else {
                        results += `
                            <div class="test-result-error">
                                <span>❌ ${part.number}</span>
                                <span>Error ${response.status}: ${response.statusText}</span>
                            </div>
                        `;
                        console.log(`❌ ${part.number}: Error ${response.status}: ${response.statusText}`);
                    }
                    
                    // Small delay between requests to avoid overwhelming the API
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                } catch (error) {
                    results += `
                        <div class="test-result-error">
                            <span>💥 ${part.number}</span>
                            <span>Network Error: ${error.message}</span>
                        </div>
                    `;
                    console.error(`💥 ${part.number}: Network Error:`, error);
                }
            }
            
            resultDiv.innerHTML = results;
            console.log('🏁 Enhanced part number testing completed');
        }

        async function testCustomPartNumber() {
            const partNumber = document.getElementById('custom-part').value.trim();
            const resultDiv = document.getElementById('custom-result');
            
            if (!partNumber) {
                resultDiv.innerHTML = `
                    <div class="test-result-error">
                        <span>❌ Invalid Input</span>
                        <span>Please enter a part number</span>
                    </div>
                `;
                return;
            }
            
            resultDiv.innerHTML = '<p>Testing custom part number...</p>';
            console.log(`🔍 Testing custom part number: ${partNumber}`);
            
            try {
                const response = await fetch(`${TECDOC_API_BASE_URL}/articles/search/lang-id/${DEFAULT_LANG_ID}/article-search/${encodeURIComponent(partNumber)}`, {
                    method: 'GET',
                    headers: {
                        'X-RapidAPI-Key': RAPIDAPI_KEY,
                        'X-RapidAPI-Host': TECDOC_API_HOST,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log(`📡 ${partNumber}: Status ${response.status} ${response.statusText}`);
                
                if (response.status === 200) {
                    const data = await response.json();
                    if (data && Object.keys(data).length > 0) {
                        resultDiv.innerHTML = `
                            <div class="test-result-success">
                                <span>✅ ${partNumber}</span>
                                <span>TecDoc Data Found</span>
                            </div>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        `;
                        console.log(`✅ ${partNumber}: TecDoc Data Found`);
                    } else {
                        resultDiv.innerHTML = `
                            <div class="test-result-not-found">
                                <span>📭 ${partNumber}</span>
                                <span>No TecDoc Data Found - Part number not in TecDoc database</span>
                            </div>
                        `;
                        console.log(`📭 ${partNumber}: No TecDoc Data Found - Part number not in TecDoc database`);
                    }
                } else if (response.status === 404) {
                    resultDiv.innerHTML = `
                        <div class="test-result-not-found">
                            <span>📭 ${partNumber}</span>
                            <span>No TecDoc Data Found - Part number not in TecDoc database</span>
                        </div>
                    `;
                    console.log(`📭 ${partNumber}: No TecDoc Data Found - Part number not in TecDoc database (404)`);
                } else if (response.status === 429) {
                    resultDiv.innerHTML = `
                        <div class="test-result-rate-limited">
                            <span>⏰ ${partNumber}</span>
                            <span>TecDoc: Monthly rate limit exceeded (100/month)</span>
                        </div>
                    `;
                    console.log(`⏰ ${partNumber}: TecDoc: Monthly rate limit exceeded (100/month)`);
                } else if (response.status === 401) {
                    resultDiv.innerHTML = `
                        <div class="test-result-error">
                            <span>❌ ${partNumber}</span>
                            <span>TecDoc: Authentication failed - check API key</span>
                        </div>
                    `;
                    console.log(`❌ ${partNumber}: TecDoc: Authentication failed - check API key`);
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result-error">
                            <span>❌ ${partNumber}</span>
                            <span>TecDoc: Service error (${response.status})</span>
                        </div>
                    `;
                    console.log(`❌ ${partNumber}: TecDoc: Service error (${response.status})`);
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result-error">
                        <span>💥 ${partNumber}</span>
                        <span>TecDoc: Network connection failed</span>
                    </div>
                `;
                console.error(`💥 ${partNumber}: TecDoc: Network connection failed:`, error);
            }
        }

        async function simulateRateLimit() {
            const resultDiv = document.getElementById('rate-limit-result');
            resultDiv.innerHTML = '<p>Simulating multiple requests to test rate limit handling...</p>';
            
            console.log('⏰ Simulating rate limit scenario...');
            
            let results = '';
            const testPart = 'TEST123';
            
            for (let i = 1; i <= 5; i++) {
                console.log(`🔍 Request ${i}/5: Testing ${testPart}`);
                
                try {
                    const response = await fetch(`${TECDOC_API_BASE_URL}/articles/search/lang-id/${DEFAULT_LANG_ID}/article-search/${testPart}`, {
                        method: 'GET',
                        headers: {
                            'X-RapidAPI-Key': RAPIDAPI_KEY,
                            'X-RapidAPI-Host': TECDOC_API_HOST,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    console.log(`📡 Request ${i}: Status ${response.status} ${response.statusText}`);
                    
                    if (response.status === 429) {
                        results += `
                            <div class="test-result-rate-limited">
                                <span>⏰ Request ${i}</span>
                                <span>Rate Limited - Monthly limit exceeded (100/month)</span>
                            </div>
                        `;
                        console.log(`⏰ Request ${i}: Rate Limited - Monthly limit exceeded (100/month)`);
                        break;
                    } else {
                        results += `
                            <div class="test-result-success">
                                <span>✅ Request ${i}</span>
                                <span>Status: ${response.status}</span>
                            </div>
                        `;
                    }
                    
                    // Small delay between requests
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                } catch (error) {
                    results += `
                        <div class="test-result-error">
                            <span>💥 Request ${i}</span>
                            <span>Network Error: ${error.message}</span>
                        </div>
                    `;
                    console.error(`💥 Request ${i}: Network Error:`, error);
                }
            }
            
            resultDiv.innerHTML = results;
            console.log('🏁 Rate limit simulation completed');
        }

        // Initialize console
        console.log('🚀 Enhanced TecDoc API Test Interface Loaded');
        console.log('📊 FREE Plan Limits: 100 requests/month, 1000 requests/hour');
        console.log('🎯 Testing clear "Not Found" indicators and rate limit management');
    </script>
</body>
</html>

// app/page.tsx
"use client";

import React, { useState } from "react";

export default function Playground() {
  const [partNumber, setPartNumber] = useState("");
  const [loading, setLoading] = useState(false);
  const [jsonResult, setJsonResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setJsonResult(null);

    try {
      const res = await fetch("/api/lookup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ partNumber }),
      });
      const data = await res.json();
      if (!res.ok) {
        setError(
          data?.error || "Lookup failed. Please check the server logs."
        );
      } else {
        setJsonResult(data);
      }
    } catch (err: any) {
      setError(err?.message || "Unexpected error");
    } finally {
      setLoading(false);
    }
  }

  function copyJson() {
    if (!jsonResult) return;
    navigator.clipboard.writeText(JSON.stringify(jsonResult, null, 2));
  }

  function downloadJson() {
    if (!jsonResult) return;
    const blob = new Blob([JSON.stringify(jsonResult, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${jsonResult.partArticleNumber || "result"}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  const verified = jsonResult?.primaryImageVerified && jsonResult?.primaryImageUrl;

  return (
    <main style={{ maxWidth: 900, margin: "40px auto", padding: 16 }}>
      <h1 style={{ fontSize: 24, marginBottom: 12 }}>
        Automotive Part Lookup Playground
      </h1>
      <form onSubmit={onSubmit} style={{ display: "flex", gap: 8, marginBottom: 16 }}>
        <input
          value={partNumber}
          onChange={(e) => setPartNumber(e.target.value)}
          placeholder="Enter partArticleNumber (e.g., 05-90283-SX)"
          style={{
            flex: 1,
            padding: "10px 12px",
            border: "1px solid #ccc",
            borderRadius: 6,
          }}
          required
        />
        <button
          type="submit"
          disabled={loading}
          style={{
            padding: "10px 16px",
            borderRadius: 6,
            border: "none",
            background: "#111",
            color: "#fff",
            cursor: "pointer",
          }}
        >
          {loading ? "Searching…" : "Search"}
        </button>
      </form>

      {error && (
        <div
          style={{
            background: "#fee",
            border: "1px solid #f99",
            color: "#900",
            padding: 12,
            borderRadius: 6,
            marginBottom: 16,
          }}
        >
          {error}
        </div>
      )}

      {jsonResult && (
        <div style={{ display: "grid", gap: 16 }}>
          <div style={{ display: "flex", gap: 8 }}>
            <button onClick={copyJson} style={{ padding: "8px 12px" }}>
              Copy JSON
            </button>
            <button onClick={downloadJson} style={{ padding: "8px 12px" }}>
              Download JSON
            </button>
            {verified && (
              <a
                href={`/api/fetch-image?url=${encodeURIComponent(
                  jsonResult.primaryImageUrl
                )}`}
                style={{
                  padding: "8px 12px",
                  background: "#0a0",
                  color: "#fff",
                  borderRadius: 6,
                  textDecoration: "none",
                }}
              >
                Download Verified Image
              </a>
            )}
          </div>

          <pre
            style={{
              padding: 16,
              background: "#0b1020",
              color: "#e3f2fd",
              borderRadius: 8,
              overflow: "auto",
              maxHeight: 600,
            }}
          >
{JSON.stringify(jsonResult, null, 2)}
          </pre>

          <div style={{ display: "flex", gap: 24, alignItems: "flex-start" }}>
            <div style={{ flex: "0 0 320px" }}>
              <h3 style={{ margin: "8px 0" }}>Primary Image</h3>
              {verified ? (
                <img
                  src={jsonResult.primaryImageUrl}
                  alt="Verified part"
                  style={{
                    maxWidth: 320,
                    width: "100%",
                    border: "1px solid #ddd",
                    borderRadius: 8,
                  }}
                />
              ) : (
                <div
                  style={{
                    padding: 12,
                    border: "1px dashed #aaa",
                    borderRadius: 8,
                    color: "#666",
                  }}
                >
                  No verifiable image found.
                </div>
              )}
            </div>
            <div style={{ flex: 1 }}>
              <h3 style={{ margin: "8px 0" }}>Notes</h3>
              <ul style={{ lineHeight: 1.6 }}>
                <li>
                  Uses OpenAI <code>/v1/responses</code> with{" "}
                  <code>tools: [&#123; type: "web_search" &#125;]</code> and{" "}
                  <code>text.format = "json_schema"</code>.
                </li>
                <li>
                  Server verifies the image: HTTPS, image content-type, and that
                  the source page visibly contains the exact part number.
                </li>
                <li>
                  If strings are truly missing online, they become{" "}
                  <em>"Data not found in search results"</em>, and{" "}
                  <code>confidence</code> becomes <code>25</code>.
                </li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}

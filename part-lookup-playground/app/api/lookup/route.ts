// app/api/lookup/route.ts
import { NextRequest, NextResponse } from "next/server";
import Ajv, { JSONSchemaType } from "ajv";

/** ===================== Types ===================== **/
type Description = {
  generalInformation: string;
  technicalInformation: string;
  applicability: string;
  originalNumbers: string;
  oemNumbers: string;
};

type OutputShape = {
  productName: string;
  sku: string;
  partArticleNumber: string;
  manufacturer: string;
  brandOrManufacturer: string;
  category:
    | "Tyres & Related Products"
    | "Brake Parts & Systems"
    | "Filters"
    | "Oils & Fluids"
    | "Engine"
    | "Window Cleaning"
    | "Glow Plug & Ignition"
    | "Wishbones & Suspension"
    | "Electrical Systems"
    | "Damping"
    | "Exhaust Gas Recirculation"
    | "Gaskets Sealing Rings"
    | "Belts Chains Rollers"
    | "Forced Induction Components"
    | "Exhaust"
    | "Engine Cooling System"
    | "Interior"
    | "Body"
    | "Fuel Supply System"
    | "Steering"
    | "Heating Ventilation"
    | "Clutch"
    | "Drive Shaft And Cv Joint"
    | "Air Suspension"
    | "Towbar Parts"
    | "Gearbox"
    | "Air Conditioning"
    | "Bearings"
    | "Propshafts And Differentials"
    | "Sensors Relays Control Units"
    | "Repair Kits"
    | "Lighting"
    | "Tuning"
    | "Fasteners";
  subcategory: string;
  description: Description;
  vehicleCompatibility: string;
  primaryImageUrl: string | null;
  primaryImageSourcePage: string | null;
  primaryImageVerified: boolean;
  stockQuantity: number;
  retailPrice: number;
  confidence: number;
  searchSources: string[];
};

/** ===================== Schema ===================== **/
const schema: JSONSchemaType<OutputShape> = {
  type: "object",
  additionalProperties: false,
  required: [
    "productName",
    "sku",
    "partArticleNumber",
    "manufacturer",
    "brandOrManufacturer",
    "category",
    "subcategory",
    "description",
    "vehicleCompatibility",
    "primaryImageUrl",
    "primaryImageSourcePage",
    "primaryImageVerified",
    "stockQuantity",
    "retailPrice",
    "confidence",
    "searchSources",
  ],
  properties: {
    productName: { type: "string", maxLength: 80 },
    sku: { type: "string" },
    partArticleNumber: { type: "string" },
    manufacturer: { type: "string", maxLength: 50 },
    brandOrManufacturer: { type: "string", maxLength: 50 },
    category: {
      type: "string",
      enum: [
        "Tyres & Related Products",
        "Brake Parts & Systems",
        "Filters",
        "Oils & Fluids",
        "Engine",
        "Window Cleaning",
        "Glow Plug & Ignition",
        "Wishbones & Suspension",
        "Electrical Systems",
        "Damping",
        "Exhaust Gas Recirculation",
        "Gaskets Sealing Rings",
        "Belts Chains Rollers",
        "Forced Induction Components",
        "Exhaust",
        "Engine Cooling System",
        "Interior",
        "Body",
        "Fuel Supply System",
        "Steering",
        "Heating Ventilation",
        "Clutch",
        "Drive Shaft And Cv Joint",
        "Air Suspension",
        "Towbar Parts",
        "Gearbox",
        "Air Conditioning",
        "Bearings",
        "Propshafts And Differentials",
        "Sensors Relays Control Units",
        "Repair Kits",
        "Lighting",
        "Tuning",
        "Fasteners",
      ] as any,
    },
    subcategory: { type: "string", maxLength: 40 },
    description: {
      type: "object",
      additionalProperties: false,
      required: [
        "generalInformation",
        "technicalInformation",
        "applicability",
        "originalNumbers",
        "oemNumbers",
      ],
      properties: {
        generalInformation: { type: "string" },
        technicalInformation: { type: "string" },
        applicability: { type: "string" },
        originalNumbers: { type: "string" },
        oemNumbers: { type: "string" },
      },
    },
    vehicleCompatibility: { type: "string" },
    primaryImageUrl: {
      anyOf: [{ type: "string", pattern: "^https://", nullable: false }, { type: "null" }],
    },
    primaryImageSourcePage: {
      anyOf: [{ type: "string", pattern: "^https://", nullable: false }, { type: "null" }],
    },
    primaryImageVerified: { type: "boolean" },
    stockQuantity: { type: "number" },
    retailPrice: { type: "number" },
    confidence: { type: "number", minimum: 0, maximum: 100 },
    searchSources: {
      type: "array",
      items: { type: "string", pattern: "^https://" },
    },
  },
};

const ajv = new Ajv({ allErrors: true, allowUnionTypes: true });
const validate = ajv.compile(schema);

/** ===================== OpenAI Responses API ===================== **/
const OPENAI_URL = "https://api.openai.com/v1/responses";
// Prefer higher daily TPD for heavy runs:
const MODEL = "gpt-5-mini";

/** ===================== Utilities ===================== **/
function normalizePartVariants(raw: string): string[] {
  const s = (raw || "").trim();
  const compact = s.replace(/\s+/g, "");
  const dashy = s.replace(/[^A-Za-z0-9]/g, "-");
  const spaced = s.replace(/[-_.]/g, " ");
  const alnum = s.replace(/[^A-Za-z0-9]/g, "");
  return Array.from(new Set([s, s.toUpperCase(), compact, dashy, spaced, alnum]));
}

async function fetchText(url: string): Promise<string> {
  const res = await fetch(url, {
    method: "GET",
    headers: {
      "User-Agent": "Mozilla/5.0 (compatible; PartLookupBot/1.2; +https://example.com)",
      Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    },
  });
  if (!res.ok) throw new Error(`Failed to fetch page: ${res.status}`);
  return await res.text();
}

async function headOrGetImage(url: string): Promise<Response> {
  const head = await fetch(url, { method: "HEAD" });
  if (head.ok) return head;
  const get = await fetch(url, { method: "GET", headers: { Range: "bytes=0-0" } });
  if (!get.ok) throw new Error(`Failed to check image: ${get.status}`);
  return get;
}

function hasPartNumberOnPage(html: string, part: string): boolean {
  const variants = normalizePartVariants(part);
  const hay = html.toUpperCase();
  return variants.some((v) => hay.includes(v.toUpperCase()));
}

/** Patchers & confidence **/
function coalesceMissingStrings(o: any): boolean {
  let patched = false;
  const ensure = (obj: any, key: string) => {
    if (typeof obj[key] !== "string" || obj[key].trim() === "") {
      obj[key] = "Data not found in search results";
      patched = true;
    }
  };
  ensure(o, "productName");
  ensure(o, "sku");
  ensure(o, "manufacturer");
  ensure(o, "brandOrManufacturer");
  ensure(o, "subcategory");
  ensure(o, "vehicleCompatibility");
  o.description ||= {};
  ensure(o.description, "generalInformation");
  ensure(o.description, "technicalInformation");
  ensure(o.description, "applicability");
  ensure(o.description, "originalNumbers");
  ensure(o.description, "oemNumbers");
  return patched;
}
function computeConfidence(o: OutputShape, hadPatching: boolean): number {
  const sourcesOk = Array.isArray(o.searchSources) && o.searchSources.length >= 2;
  const imageOk = !!o.primaryImageUrl && o.primaryImageVerified === true;
  if (hadPatching || !sourcesOk) return 25;
  return imageOk ? 95 : 75;
}

/** ===================== Enrichment Helpers ===================== **/
function textFromHtml(html: string): string {
  return html
    .replace(/<script[\s\S]*?<\/script>/gi, " ")
    .replace(/<style[\s\S]*?<\/style>/gi, " ")
    .replace(/<[^>]+>/g, " ")
    .replace(/\s+/g, " ")
    .trim();
}
function splitCandidates(s: string): string[] {
  return s
    .split(/[\n\r,;|·•]+/g)
    .map((t) => t.trim())
    .filter((t) => t.length >= 2 && t.length <= 200);
}

const MAKE_WORDS =
  /(Audi|VW|Volkswagen|Seat|Skoda|BMW|Mercedes|Mercedes-Benz|Opel|Vauxhall|Ford|Peugeot|Citro[eë]n|Renault|Dacia|Fiat|Alfa Romeo|Lancia|Toyota|Lexus|Nissan|Mitsubishi|Mazda|Subaru|Hyundai|Kia|Suzuki|Volvo|Saab|Porsche|Jaguar|Land Rover|Range Rover|Mini|Chevrolet|GMC|Cadillac|Jeep|Dodge|Chrysler|RAM|SAF|BPW|MAN|DAF|Iveco|Scania|Renault Trucks|Kamaz|UAZ|GAZ)/i;

function cleanNumberToken(t: string): string {
  if (!t) return "";
  let x = t.replace(/[^\x20-\x7E]/g, ""); // strip all non-printable / binary junk
  x = x.replace(/\u00A0/g, " ").trim();
  x = x.replace(
    /\b(OEN|OEM|OE|OES|Ref\.?|Reference|Orig\.?|Original(?:e| )?(?:Teil|Number|Nummer)|No\.|Nr\.?)\b[:\-\s]*/gi,
    ""
  );
  x = x.replace(/\s{2,}/g, " ").replace(/^[–\-,:;]+|[–\-,:;]+$/g, "");
  // Only allow plausible article numbers: letters, digits, dots, dashes, slashes, spaces
  if (!/^[A-Za-z0-9.\-\/ ]{2,40}$/.test(x)) return "";
  return x.trim();
}

function sectionAround(html: string, regex: RegExp, win = 16000): string {
  const m = regex.exec(html);
  if (!m) return html; // fall back to whole page if we don't find a heading
  const i = Math.max(0, m.index - 2000);
  return html.slice(i, Math.min(html.length, m.index + win));
}
function extractOEMAndCross(html: string) {
  const heading =
    /(OEM|OE|OEN)[-\s]?(Number|Nummer|Numer|Numero|Número|Номер)|Original(?:e| )?(?:Equipment|Teil|Ersatzteil|Nummer)|Cross[-\s]?(?:ref(?:erence)?|reference)|Reference(?: numbers?)?|Vergleichsnummern|Vergleichsnummer|Referenznummern|Referenznummer/i;
  const raw = sectionAround(html, heading);
  const text = textFromHtml(raw);
  const cand = splitCandidates(text);
  const oem = new Set<string>();
  const xref = new Set<string>();
  for (const line of cand) {
    const tok = cleanNumberToken(line);
    if (!tok) continue;
    // Heuristic: if line also contains a make brand word, treat as OEM; else cross/ref
    if (MAKE_WORDS.test(line)) oem.add(tok);
    else if (/[A-Z0-9]/i.test(tok) && tok.length >= 3) xref.add(tok);
  }
  return { oem, xref };
}

function normalizeYearToken(s: string): string | null {
  if (!s) return null;
  const t = s.replace(/\s+/g, " ").trim();
  const mRange = t.match(/\b((?:19|20)\d{2})\s*(?:[-–/]|to|bis)\s*((?:19|20)\d{2})\b/i);
  if (mRange) return `${mRange[1]}–${mRange[2]}`;
  const mSingle = t.match(/\b(?:from|ab|since)?\s*((?:19|20)\d{2})(?:\s*(?:onwards|>|and up|\+))?\b/i);
  if (mSingle) return `${mSingle[1]}–${mSingle[1]}`;
  const mOpenStart = t.match(/\b((?:19|20)\d{2})\s*[-–/]\s*$/);
  if (mOpenStart) return `${mOpenStart[1]}–${mOpenStart[1]}`;
  const mOpenEnd = t.match(/^\s*[-–/]\s*((?:19|20)\d{2})\b/);
  if (mOpenEnd) return `${mOpenEnd[1]}–${mOpenEnd[1]}`;
  return null;
}
function buildVehicleTuple(
  make?: string,
  model?: string,
  engine?: string,
  years?: string | null
): string | null {
  const parts: string[] = [];
  if (make) parts.push(make.trim());
  if (model) parts.push(model.trim());
  if (engine) parts.push(engine.trim());
  if (parts.length === 0) return null;
  const base = parts.join(" ");
  if (years) return `${base} (${years})`;
  return base;
}

/** Ultra-flexible applicability extractor (accepts many shapes) */
function extractApplicability(html: string): Set<string> {
  const text = textFromHtml(html);
  const out = new Set<string>();

  const lines = text.split(/[\r\n]+|(?<=\.)\s+/).map((l) => l.trim()).filter(Boolean);

  const MAKE = MAKE_WORDS.source;

  const rxMakeModelEngineYears = new RegExp(
    `\\b(${MAKE})\\s+([A-Za-z0-9][A-Za-z0-9\\-./ ]{1,48})\\s+([A-Za-z0-9][A-Za-z0-9\\-./ ]{1,24})\\s+((?:19|20)\\d{2}[^A-Za-z0-9]{0,3}(?:[-–/]|to|bis)\\s*(?:19|20)\\d{2}|(?:from|ab|since)\\s*(?:19|20)\\d{2}|(?:19|20)\\d{2})`,
    "i"
  );
  const rxMakeModelYears = new RegExp(
    `\\b(${MAKE})\\s+([A-Za-z0-9][A-Za-z0-9\\-./ ]{1,48})\\s*\\(?(?:model\\s*)?((?:19|20)\\d{2}[^A-Za-z0-9]{0,3}(?:[-–/]|to|bis)\\s*(?:19|20)\\d{2}|(?:from|ab|since)\\s*(?:19|20)\\d{2}|(?:19|20)\\d{2})\\)?`,
    "i"
  );
  const rxMakeModel = new RegExp(`\\b(${MAKE})\\s+([A-Za-z0-9][A-Za-z0-9\\-./ ]{1,48})\\b`, "i");

  function findYearsNear(idx: number): string | null {
    const w = 3;
    for (let i = Math.max(0, idx - w); i <= Math.min(lines.length - 1, idx + w); i++) {
      const y = normalizeYearToken(lines[i]);
      if (y) return y;
      const m = lines[i].match(/\b((?:19|20)\d{2})\s*[-–/]\s*((?:19|20)\d{2})\b/);
      if (m) return `${m[1]}–${m[2]}`;
    }
    return null;
  }

  lines.forEach((l, idx) => {
    const m = rxMakeModelEngineYears.exec(l);
    if (m) {
      const tup = buildVehicleTuple(m[1], m[2], m[3], normalizeYearToken(m[4]) || findYearsNear(idx));
      if (tup) out.add(tup);
    }
  });
  lines.forEach((l, idx) => {
    const m = rxMakeModelYears.exec(l);
    if (m) {
      const tup = buildVehicleTuple(m[1], m[2], undefined, normalizeYearToken(m[3]) || findYearsNear(idx));
      if (tup) out.add(tup);
    }
  });
  lines.forEach((l, idx) => {
    const mm = rxMakeModel.exec(l);
    if (mm) {
      const make = mm[1],
        model = mm[2];
      // try to find engine token near
      const window = lines.slice(Math.max(0, idx - 1), Math.min(lines.length, idx + 2)).join(" ");
      const em = window.match(
        /\b(OM[0-9A-Z]{3,}|D[0-9]{1,2}[A-Z]?[A-Z0-9\-]*|[0-9]\.[0-9]\s*[A-Za-z]{2,4}|[A-Z]{2,4}[0-9]{2,4}|[A-Z0-9.\-]{3,10})\b/
      );
      const engine = em?.[1];
      const years = findYearsNear(idx);
      const tup = buildVehicleTuple(make, model, engine, years);
      if (tup) out.add(tup);
    }
  });

  return out;
}

function extractTechnical(html: string): Set<string> {
  const t = textFromHtml(html);
  const out = new Set<string>();
  const kv = t.match(
    /([A-Z][A-Za-z /ØøΔΣμ°\-]{2,48})\s*[:=]\s*([0-9.,]+(?:\s*(?:mm|cm|m|kg|g|Nm|bar|V|A|W|°C|°F|L|µm|in|ft|pcs|Ø))?)/g
  );
  if (kv) kv.forEach((s) => out.add(s.replace(/\s+/g, " ").trim()));
  const specs = t.match(
    /\b(?:Inner Diameter|Outer Diameter|Diameter|Height|Width|Length|Weight|Thread|Voltage|Power|Capacity|Piston(?:\s*Ø)?)\b[^.,;]{0,60}[0-9][^,;]{0,40}/gi
  );
  if (specs) specs.forEach((s) => out.add(s.replace(/\s+/g, " ").trim()));
  return out;
}
function findImageUrls(html: string): string[] {
  const urls = new Set<string>();
  const og =
    html.match(/<meta[^>]+property=["']og:image["'][^>]+content=["'](https?:\/\/[^"']+)["']/gi) || [];
  og.forEach((m) => {
    const u = m.match(/content=["'](https?:\/\/[^"']+)["']/i)?.[1];
    if (u?.startsWith("https://")) urls.add(u);
  });
  const jsonLd = html.match(/<script type=["']application\/ld\+json["'][\s\S]*?<\/script>/gi) || [];
  jsonLd.forEach((block) => {
    const u = block.match(/"image"\s*:\s*"(https?:\/\/[^"]+)"/i)?.[1];
    if (u?.startsWith("https://")) urls.add(u);
  });
  const img = html.match(/<img[^>]+(?:data-src|data-large|src)=["'](https?:\/\/[^"']+)["']/gi) || [];
  img.forEach((m) => {
    const u = m.match(/["'](https?:\/\/[^"']+)["']/)?.[1];
    if (u?.startsWith("https://") && !u.match(/\.(svg|gif)$/i)) urls.add(u);
  });
  return Array.from(urls);
}
function joinComma(items: Set<string> | string[]): string {
  const arr = Array.isArray(items) ? items : Array.from(items);
  return arr
    .map((s) => s.replace(/\betc\.?/gi, "").trim())
    .filter(Boolean)
    .join(", ");
}
function stripEtc(s: string) {
  return (s || "").replace(/\betc\.?/gi, "").replace(/\s{2,}/g, " ").trim();
}

/** Known catalogs: seed search + follow product links containing the part */
const HOSTS = [
  "trucks.autodoc.co.uk",
  "www.autodoc.co.uk",
  "www.autodoc.parts",
  "www.pkwteile.de",
  "www.euspares.co.uk",
  "www.buycarparts.co.uk",
  "www.autoteiledirekt.de",
  "www.mister-auto.co.uk",
  "www.oscaro.com",
  "lkwteile.autodoc.de",
];

function seedUrls(part: string): string[] {
  const v = normalizePartVariants(part);
  const q = Array.from(new Set(v));
  const out = new Set<string>();
  for (const host of HOSTS) {
    for (const s of q) {
      const enc = encodeURIComponent(s);
      out.add(`https://${host}/search?search=${enc}`);
      out.add(`https://${host}/search?keyword=${enc}`);
      out.add(`https://${host}/${enc}`);
    }
  }
  // direct guesses (Autodoc’s numeric id is unknown; these still help for some hosts)
  for (const s of q) {
    out.add(`https://www.autodoc.parts/car-parts/oem/${s}`);
  }
  return Array.from(out);
}

function extractLinksForPart(html: string, part: string, hostFilter?: string): string[] {
  const v = normalizePartVariants(part);
  const urls = new Set<string>();
  const re = /https?:\/\/[^\s"'<>]+/gi;
  let m: RegExpExecArray | null;
  while ((m = re.exec(html))) {
    const u = m[0];
    if (!u.startsWith("http")) continue;
    if (hostFilter && !u.includes(hostFilter)) continue;
    const inHosts = HOSTS.some((h) => u.includes(h));
    if (!inHosts) continue;
    const hitsPart = v.some((pv) => u.toLowerCase().includes(pv.toLowerCase()));
    if (hitsPart) urls.add(u.split(/[)"'\s]+/)[0]);
  }
  return Array.from(urls).slice(0, 12); // keep it reasonable
}

/** Enrich by crawling seeds + model sources + followed product links */
async function enrichFromSources(sources: string[], part: string) {
  const seeds = seedUrls(part);
  const expanded = new Set<string>([...seeds, ...(sources || [])]);

  // Crawl search pages first, then follow product links we detect.
  const toVisit: string[] = Array.from(expanded);
  const visited = new Set<string>();

  const oem = new Set<string>();
  const xref = new Set<string>();
  const appl = new Set<string>();
  const tech = new Set<string>();
  let image: string | null = null;
  let imagePage: string | null = null;

  // Crawl all pages in parallel instead of serially
  const fetchPages = await Promise.allSettled(
    toVisit.map(async (url) => {
      if (!url || visited.has(url)) return null;
      visited.add(url);

      const res = await fetch(url, {
        headers: { "User-Agent": "Mozilla/5.0 (compatible; PartLookupBot/1.2)" },
      });
      if (!res.ok) return null;
      const html = await res.text();
      return { url, html };
    })
  );

  for (const p of fetchPages) {
    if (p.status !== "fulfilled" || !p.value) continue;
    const { url, html } = p.value;

    // From search pages, follow likely product links with the part in URL
    if (/\/search\?/.test(url)) {
      const more = extractLinksForPart(html, part);
      more.forEach((u) => {
        if (!visited.has(u)) toVisit.push(u);
      });
    }

    const ex = extractOEMAndCross(html);
    ex.oem.forEach((v) => oem.add(v));
    ex.xref.forEach((v) => xref.add(v));
    extractApplicability(html).forEach((v) => appl.add(v));
    extractTechnical(html).forEach((v) => tech.add(v));

    // Image: require part visible on the page
    if (!image && hasPartNumberOnPage(html, part)) {
      for (const u of findImageUrls(html)) {
        try {
          const head = await headOrGetImage(u);
          const ct = head.headers.get("content-type")?.toLowerCase() || "";
          if (ct.startsWith("image/")) {
            image = u;
            imagePage = url;
            break;
          }
        } catch {
          /* ignore */
        }
      }
    }
  }
  return { oem, xref, appl, tech, image, imagePage, expandedSources: Array.from(visited) };
}

/** ===================== Route ===================== **/
export async function POST(req: NextRequest) {
  try {
    const { partNumber } = await req.json();
    if (!partNumber || typeof partNumber !== "string") {
      return NextResponse.json(
        { error: "Provide JSON body: { partNumber: string }" },
        { status: 400 }
      );
    }

    /** -------- FLEXIBLE Prompts (no strict format) -------- */
    const systemPrompt = `
You are a research-grade automotive catalog agent. Use ONLY ONLY ONLY online sources via the built-in web_search tool. NO offline knowledge. NO assumptions. NEVER invent values. Do not invent data. ALWAYS respond in ENGLISH ONLY even if the found information is in another language.

GOAL
Return STRICT JSON matching the provided schema. For vehicle compatibility and applicability:
• Be FLEXIBLE and EXHAUSTIVE based on what exists online.
• If only Make is available, output just "Make".
• If Make+Model are available, output "Make Model".
• If Make+Model+Engine are available, include all three.
• When years are available in ANY form, append them in parentheses (normalize to "YYYY–YYYY" when possible, but DO NOT DROP an entry because years/engine are missing).
• All items MUST be comma-separated in a single line. ZERO filler words (no "etc.", "others", "such as", labels, or commentary).
• "vehicleCompatibility" MUST be an exact copy of "description.applicability".

SOURCES
Prefer manufacturer and major catalogs/distributors (AUTODOC, PKWTeile, EUspares, Autodoc Trucks, Mister-Auto, Oscaro, Diesel Technic, Febi/Bilstein, and the rest of the internet). Use many part-number variants (upper/lower, digits-only, with/without punctuation).

IMAGE
Provide exactly one HTTPS DIRECT image URL of THIS exact part number and the HTTPS page that visibly shows the same part number. If verification fails, set both URLs to null and primaryImageVerified false.

OTHER FIELDS
• productName: exact catalog title (≤80 chars).
• category: one of the provided enums; subcategory ≤40 chars (catalog wording).
• description.* sections are single-line, comma-separated lists without commentary.
• searchSources: include ALL distinct HTTPS pages actually used (no limit).`;

    const userInstruction = `
Input partArticleNumber: "${partNumber}"

Populate every field from online sources. For "description.applicability":
• Build a single comma-separated line where each item is one of:
  - "Make"
  - "Make Model"
  - "Make Model Engine"
  - Any of the above optionally followed by years in parentheses (any source year form; normalize to "YYYY–YYYY" when possible).
• Do NOT omit valid items just because engine/years are missing.
• "vehicleCompatibility" must be EXACTLY the same string as "description.applicability".
ALL OUTPUT MUST BE IN ENGLISH.
If a source provides details in another language, TRANSLATE it to English before returning.

If a field truly cannot be found anywhere, use "Data not found in search results" (strings) or 0 (numbers). STRICT JSON only.`;

    /** -------- Responses API payload -------- */
    const payload = {
      model: MODEL,
      input: [
        { role: "system", content: [{ type: "input_text", text: systemPrompt }] },
        { role: "user", content: [{ type: "input_text", text: userInstruction }] },
      ],
      tools: [{ type: "web_search" as const }],
      tool_choice: "auto" as const,
      text: {
        format: {
          type: "json_schema",
          name: "PartLookupStrictSchema",
          schema,
          strict: true,
        },
      },
      max_output_tokens: 8000,
    };

    const resp = await fetch(OPENAI_URL, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
        "Content-Type": "application/json",
        "OpenAI-Beta": "tools=v1",
      },
      body: JSON.stringify(payload, (_k, v) => (typeof v === "function" ? undefined : v)),
    });

    if (!resp.ok) {
      const e = await resp.text();
      return NextResponse.json({ error: `OpenAI error ${resp.status}: ${e}` }, { status: 500 });
    }

    const data = await resp.json();

    /** -------- Extract model JSON -------- */
    let rawJson: string | null = null;
    if (typeof data.output_text === "string") rawJson = data.output_text;
    else if (Array.isArray(data.output)) {
      for (const item of data.output) {
        if (Array.isArray(item.content)) {
          for (const c of item.content) {
            if (typeof c.text === "string") {
              rawJson = c.text;
              break;
            }
          }
        }
        if (rawJson) break;
      }
    }
    if (!rawJson)
      return NextResponse.json(
        { error: "Could not parse JSON from Responses API." },
        { status: 500 }
      );

    let result: OutputShape;
    try {
      result = JSON.parse(rawJson);
    } catch {
      return NextResponse.json(
        { error: "Responses API did not return valid JSON." },
        { status: 500 }
      );
    }

    /** -------- Defaults -------- */
    if (!result.sku || result.sku.trim() === "") result.sku = partNumber;

    /** -------- Enrich: seeds + sources + follow product links -------- */
    const crawl = await enrichFromSources(result.searchSources || [], partNumber);

    // Merge OEM numbers
    const mergedOEM = new Set<string>();
    (result.description.oemNumbers || "")
      .split(",")
      .forEach((t) => {
        const v = cleanNumberToken(t);
        if (v) mergedOEM.add(v);
      });
    crawl.oem.forEach((v) => mergedOEM.add(v));
    result.description.oemNumbers = joinComma(mergedOEM) || "Data not found in search results";

    // Merge cross/original numbers
    const mergedXref = new Set<string>();
    (result.description.originalNumbers || "")
      .split(",")
      .forEach((t) => {
        const v = cleanNumberToken(t);
        if (v) mergedXref.add(v);
      });
    crawl.xref.forEach((v) => mergedXref.add(v));
    result.description.originalNumbers = joinComma(mergedXref) || "Data not found in search results";

    // Merge technical specs
    const mergedTech = new Set<string>();
    (result.description.technicalInformation || "")
      .split(/[;,]\s*/g)
      .forEach((t) => {
        const v = stripEtc(t);
        if (v) mergedTech.add(v);
      });
    crawl.tech.forEach((v) => mergedTech.add(v));
    result.description.technicalInformation =
      joinComma(mergedTech) || "Data not found in search results";

    // --- Ensure generalInformation is always detailed ---
    if (crawl.tech.size > 0) {
      result.description.generalInformation =
        (result.description.generalInformation || "") +
        " | Technical details: " +
        joinComma(crawl.tech);
    }

    // Merge applicability (flexible) and mirror to vehicleCompatibility
    const mergedAppl = new Set<string>();
    (result.description.applicability || "")
      .split(/[;,]\s*/g)
      .forEach((t) => {
        const v = stripEtc(t);
        if (v) mergedAppl.add(v);
      });
    crawl.appl.forEach((v) => mergedAppl.add(v));
    const applStr = joinComma(mergedAppl) || "Data not found in search results";
    result.description.applicability = applStr;
    result.vehicleCompatibility = applStr;

    // Merge sources with ones we actually crawled
    const mergedSources = new Set<string>(
      Array.isArray(result.searchSources) ? result.searchSources : []
    );
    crawl.expandedSources.forEach((u) => mergedSources.add(u));
    result.searchSources = Array.from(mergedSources);

    /** -------- Image verification / upgrade -------- */
    if (!result.primaryImageVerified) {
      if (crawl.image && crawl.imagePage) {
        try {
          const pageHtml = await fetchText(crawl.imagePage);
          const showsPart = hasPartNumberOnPage(pageHtml, partNumber);
          const imgRes = await headOrGetImage(crawl.image);
          const ctype = imgRes.headers.get("content-type")?.toLowerCase() || "";
          if (showsPart && ctype.startsWith("image/")) {
            result.primaryImageUrl = crawl.image;
            result.primaryImageSourcePage = crawl.imagePage;
            result.primaryImageVerified = true;
          } else {
            result.primaryImageUrl = null;
            result.primaryImageSourcePage = null;
            result.primaryImageVerified = false;
          }
        } catch {
          result.primaryImageUrl = null;
          result.primaryImageSourcePage = null;
          result.primaryImageVerified = false;
        }
      } else {
        result.primaryImageUrl = result.primaryImageUrl ?? null;
        result.primaryImageSourcePage = result.primaryImageSourcePage ?? null;
        result.primaryImageVerified = false;
      }
    }

    /** -------- Final sanitize & confidence -------- */
    const hadPatching = coalesceMissingStrings(result);
    result.description.generalInformation = stripEtc(result.description.generalInformation);
    result.description.technicalInformation = stripEtc(result.description.technicalInformation);
    result.description.applicability = stripEtc(result.description.applicability);
    result.description.originalNumbers = stripEtc(result.description.originalNumbers);
    result.description.oemNumbers = stripEtc(result.description.oemNumbers);
    result.vehicleCompatibility = stripEtc(result.vehicleCompatibility);

    result.confidence = computeConfidence(result, hadPatching);

    /** -------- Validate & return -------- */
    const valid = validate(result);
    if (!valid)
      return NextResponse.json(
        { error: "Validation failed", details: validate.errors },
        { status: 500 }
      );
    return NextResponse.json(result, { status: 200 });
  } catch (err: any) {
    return NextResponse.json({ error: err?.message || "Unknown error" }, { status: 500 });
  }
}

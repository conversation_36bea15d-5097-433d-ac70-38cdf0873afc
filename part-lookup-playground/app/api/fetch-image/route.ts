// app/api/fetch-image/route.ts
// Optional: proxy-download the verified image to avoid CORS or blocked HEAD
import { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  const url = req.nextUrl.searchParams.get("url");
  if (!url || !url.startsWith("https://")) {
    return new Response("Missing or invalid url", { status: 400 });
  }
  const res = await fetch(url);
  if (!res.ok) return new Response("Failed to fetch image", { status: 502 });
  const ctype = res.headers.get("content-type") || "application/octet-stream";
  return new Response(res.body, {
    status: 200,
    headers: {
      "Content-Type": ctype,
      "Content-Disposition": `attachment; filename="part-image"`,
      "Cache-Control": "no-store",
    },
  });
}

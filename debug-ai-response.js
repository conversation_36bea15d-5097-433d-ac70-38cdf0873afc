/**
 * Debug AI Response Format
 * Test the optimized AI response to ensure compatibility
 */

import { analyzeColumnsWithProductionAI } from './src/services/aiMappingBackend.js';

// Test data that matches your CSV structure
const testData = [
  {
    "Vendor": "SUPPLIER1",
    "Item": "12345",
    "Brand": "BOSCH",
    "Type": "Brake Disc",
    "Vehicle": "BMW 3 Series",
    "Program": "Standard",
    "Main_OE_References": "34116792217",
    "OE_Brand": "BMW",
    "Weight_kg": "2.5",
    "Packing_Length_mm": "300",
    "Packing_Height_mm": "50",
    "Packing_Width_mm": "300",
    "Packing_Volume_dm3": "4.5",
    "Delivery_Period_days": "3",
    "Netto_Price_EUR": "45.99",
    "Order": "100",
    "MOQ": "1",
    "Paired_Article": "12346",
    "Note": "High quality brake disc",
    "Price_Date": "2024-01-15"
  },
  {
    "Vendor": "SUPPLIER2",
    "Item": "67890",
    "Brand": "BREMBO",
    "Type": "Brake Pad",
    "Vehicle": "Audi A4",
    "Program": "Premium",
    "Main_OE_References": "8E0698151A",
    "OE_Brand": "AUDI",
    "Weight_kg": "1.2",
    "Packing_Length_mm": "200",
    "Packing_Height_mm": "30",
    "Packing_Width_mm": "150",
    "Packing_Volume_dm3": "0.9",
    "Delivery_Period_days": "2",
    "Netto_Price_EUR": "89.50",
    "Order": "50",
    "MOQ": "2",
    "Paired_Article": "67891",
    "Note": "Ceramic brake pads",
    "Price_Date": "2024-01-16"
  }
];

async function debugAIResponse() {
  console.log('🔍 Debugging AI Response Format...\n');
  
  try {
    console.log('📊 Input Data:');
    console.log('Headers:', Object.keys(testData[0]));
    console.log('Rows:', testData.length);
    console.log('');
    
    console.log('🤖 Calling AI Service...');
    const result = await analyzeColumnsWithProductionAI(testData);
    
    console.log('✅ AI Response Received');
    console.log('');
    
    // Check response structure
    console.log('📋 Response Structure Analysis:');
    console.log('Type:', typeof result);
    console.log('Has mappings:', !!result.mappings);
    console.log('Mappings is array:', Array.isArray(result.mappings));
    console.log('Mappings length:', result.mappings?.length || 0);
    console.log('Has confidence:', !!result.confidence);
    console.log('Has suggestedCategory:', !!result.suggestedCategory);
    console.log('Has suggestedSubcategory:', !!result.suggestedSubcategory);
    console.log('Has unmappedColumns:', !!result.unmappedColumns);
    console.log('');
    
    // Show actual response
    console.log('📝 Full Response:');
    console.log(JSON.stringify(result, null, 2));
    console.log('');
    
    // Test mapping structure
    if (result.mappings && Array.isArray(result.mappings)) {
      console.log('🔍 Mapping Analysis:');
      result.mappings.forEach((mapping, index) => {
        console.log(`${index + 1}. Mapping:`, mapping);
        console.log('   Has originalColumn:', !!mapping.originalColumn);
        console.log('   Has targetField:', !!mapping.targetField);
        console.log('   Has confidence:', !!mapping.confidence);
        console.log('   Has col:', !!mapping.col);
        console.log('   Has field:', !!mapping.field);
        console.log('   Has conf:', !!mapping.conf);
      });
    }
    
    // Test compatibility with import function
    console.log('');
    console.log('🧪 Testing Import Compatibility...');
    
    // Simulate the mapping process
    const aiMapping = {};
    if (result.mappings) {
      result.mappings.forEach((mapping) => {
        // Check both old and new format
        const originalColumn = mapping.originalColumn || mapping.col;
        const targetField = mapping.targetField || mapping.field;
        
        if (originalColumn && targetField) {
          aiMapping[originalColumn] = targetField;
          console.log(`✓ Mapped: ${originalColumn} → ${targetField}`);
        } else {
          console.log(`❌ Invalid mapping:`, mapping);
        }
      });
    }
    
    console.log('');
    console.log('📊 Final Mapping Dictionary:');
    console.log(aiMapping);
    
  } catch (error) {
    console.error('❌ Error during debugging:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

debugAIResponse();

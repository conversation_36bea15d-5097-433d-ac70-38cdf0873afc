/**
 * Test script for AI mapping with production API key
 * Run with: node test-ai-production.js
 */

const OPENAI_API_KEY = '********************************************************************************************************************************************************************';

// Sample data for testing
const sampleData = [
  {
    "Vendor": "SUPPLIER1",
    "Item": "12345",
    "Brand": "BOSCH",
    "Type": "Brake Disc",
    "Vehicle": "BMW 3 Series",
    "Program": "Standard",
    "Main_OE_References": "34116792217",
    "OE_Brand": "BMW",
    "Weight_kg": "2.5",
    "Packing_Length_mm": "300",
    "Packing_Height_mm": "50",
    "Packing_Width_mm": "300",
    "Packing_Volume_dm3": "4.5",
    "Delivery_Period_days": "3",
    "Netto_Price_EUR": "45.99",
    "Order": "100",
    "MOQ": "1",
    "Paired_Article": "12346",
    "Note": "High quality brake disc",
    "Price_Date": "2024-01-15"
  },
  {
    "Vendor": "SUPPLIER2",
    "Item": "67890",
    "Brand": "BREMBO",
    "Type": "Brake Pad",
    "Vehicle": "Audi A4",
    "Program": "Premium",
    "Main_OE_References": "8E0698151A",
    "OE_Brand": "AUDI",
    "Weight_kg": "1.2",
    "Packing_Length_mm": "200",
    "Packing_Height_mm": "30",
    "Packing_Width_mm": "150",
    "Packing_Volume_dm3": "0.9",
    "Delivery_Period_days": "2",
    "Netto_Price_EUR": "89.50",
    "Order": "50",
    "MOQ": "2",
    "Paired_Article": "67891",
    "Note": "Ceramic brake pads",
    "Price_Date": "2024-01-16"
  },
  {
    "Vendor": "SUPPLIER3",
    "Item": "11111",
    "Brand": "FEBI",
    "Type": "Oil Filter",
    "Vehicle": "Mercedes C-Class",
    "Program": "Economy",
    "Main_OE_References": "A0001802609",
    "OE_Brand": "MERCEDES",
    "Weight_kg": "0.3",
    "Packing_Length_mm": "100",
    "Packing_Height_mm": "80",
    "Packing_Width_mm": "100",
    "Packing_Volume_dm3": "0.8",
    "Delivery_Period_days": "1",
    "Netto_Price_EUR": "12.75",
    "Order": "200",
    "MOQ": "5",
    "Paired_Article": "11112",
    "Note": "Standard oil filter",
    "Price_Date": "2024-01-17"
  }
];

// Target fields for mapping
const TARGET_FIELDS = {
  partArticleNumber: 'Part Article Number - Primary product identifier',
  name: 'Product Name - Main product title/description',
  sku: 'SKU - Stock Keeping Unit identifier',
  manufacturer: 'Manufacturer - Brand or company that makes the product',
  supplierName: 'Supplier Name - Company/vendor supplying the product',
  stockQuantity: 'Stock Quantity - Number of units available',
  retailPrice: 'Retail Price - Selling price for merchants',
  wholesalePrice: 'Wholesale Price - Bulk pricing for suppliers',
  category: 'Category - Main product category',
  subcategory: 'Subcategory - Specific product subcategory',
  descriptionAndSpecifications: 'Description - Detailed product description and specifications',
  primaryImage: 'Primary Image - Main product image URL',
  additionalImages: 'Additional Images - Array of additional image URLs',
  shippingOrigin: 'Shipping Origin - Location where product ships from',
  vehicleCompatibility: 'Vehicle Compatibility - Compatible vehicle types (text input)',
  status: 'Status - Product status (draft, active, out_of_stock, discontinued)',
  width: 'Tyre Width - Width measurement for tyres',
  aspectRatio: 'Aspect Ratio - Aspect ratio for tyres',
  rimDiameter: 'Rim Diameter - Rim diameter for tyres',
  loadIndex: 'Load Index - Load index for tyres',
  speedRating: 'Speed Rating - Speed rating for tyres',
  season: 'Season - Tyre season type (summer, winter, all-season)'
};

// Available categories
const availableCategories = [
  {
    id: 'brakes',
    displayName: 'Brakes',
    description: 'Brake components including discs, pads, calipers',
    subcategories: [
      { id: 'brake-disc', displayName: 'Brake Discs' },
      { id: 'brake-pad', displayName: 'Brake Pads' },
      { id: 'brake-caliper', displayName: 'Brake Calipers' }
    ]
  },
  {
    id: 'filters',
    displayName: 'Filters',
    description: 'Oil, air, fuel, and cabin filters',
    subcategories: [
      { id: 'oil-filter', displayName: 'Oil Filters' },
      { id: 'air-filter', displayName: 'Air Filters' },
      { id: 'fuel-filter', displayName: 'Fuel Filters' }
    ]
  }
];

function createAIPrompt(headers, sampleData) {
  return `You are an expert data analyst specializing in automotive parts and product data mapping. 

TASK: Analyze the provided column headers and sample data to map each column to the most appropriate target field in our product system.

COLUMN HEADERS: ${headers.join(', ')}

SAMPLE DATA (first 3 rows):
${sampleData.map((row, index) => 
  `Row ${index + 1}: ${headers.map(header => `${header}: "${row[header] || ''}"`).join(', ')}`
).join('\n')}

TARGET FIELDS AVAILABLE:
${Object.entries(TARGET_FIELDS).map(([key, description]) => `- ${key}: ${description}`).join('\n')}

AVAILABLE CATEGORIES:
${availableCategories.map(cat => 
  `- ${cat.id} (${cat.displayName}): ${cat.description}\n  Subcategories: ${cat.subcategories.map(sub => sub.displayName).join(', ')}`
).join('\n')}

ANALYSIS REQUIREMENTS:
1. Analyze both column headers AND sample cell values to understand the semantic meaning
2. Map each column to the most appropriate target field
3. Suggest the best category and subcategory based on the product data
4. Provide confidence scores (0-100) for each mapping
5. Identify any columns that cannot be mapped (will go to description)

SPECIAL MAPPING RULES:
- "Vendor" or "Supplier" columns typically map to "supplierName"
- "Item", "Part Number", "Article Number", "SKU" typically map to "partArticleNumber"
- "Brand", "Manufacturer", "Make" typically map to "manufacturer"
- Price columns should map to "retailPrice" or "wholesalePrice" based on context
- Vehicle-related columns should map to "vehicleCompatibility" (text input)
- Weight, dimensions, packaging info should go to "descriptionAndSpecifications"

RESPONSE FORMAT (JSON only):
{
  "mappings": [
    {
      "originalColumn": "column_name",
      "targetField": "target_field_name",
      "confidence": 95,
      "reasoning": "explanation of why this mapping makes sense",
      "sampleValues": ["sample1", "sample2", "sample3"]
    }
  ],
  "suggestedCategory": "category_id",
  "suggestedSubcategory": "subcategory_id", 
  "unmappedColumns": ["column1", "column2"],
  "confidence": 85
}

Analyze the data and provide the JSON response:`;
}

async function testAIMapping() {
  console.log('🤖 Testing AI Column Mapping with Production API Key...\n');
  
  const headers = Object.keys(sampleData[0]);
  const prompt = createAIPrompt(headers, sampleData);
  
  console.log('📊 Sample Data Headers:', headers.join(', '));
  console.log('📝 Sample Data Rows:', sampleData.length);
  console.log('🔑 API Key Status:', OPENAI_API_KEY ? 'Configured' : 'Missing');
  console.log('');
  
  try {
    console.log('🚀 Sending request to OpenAI API...');
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert data analyst specializing in automotive parts data mapping. Always respond with valid JSON only.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ OpenAI API Error:', response.status, response.statusText);
      console.error('Error details:', errorText);
      return;
    }

    const result = await response.json();
    const aiResponse = result.choices[0]?.message?.content;

    if (!aiResponse) {
      console.error('❌ No response from OpenAI API');
      return;
    }

    console.log('✅ AI Analysis Completed Successfully!\n');
    
    // Parse the JSON response
    const parsedResult = JSON.parse(aiResponse);
    
    console.log('🎯 Overall Confidence:', parsedResult.confidence + '%');
    console.log('📂 Suggested Category:', parsedResult.suggestedCategory);
    console.log('🏷️ Suggested Subcategory:', parsedResult.suggestedSubcategory);
    console.log('');
    
    console.log('📊 Column Mappings:');
    parsedResult.mappings.forEach((mapping, index) => {
      console.log(`${index + 1}. "${mapping.originalColumn}" → ${mapping.targetField}`);
      console.log(`   Confidence: ${mapping.confidence}%`);
      console.log(`   Reasoning: ${mapping.reasoning}`);
      console.log(`   Sample Values: [${mapping.sampleValues.join(', ')}]`);
      console.log('');
    });
    
    if (parsedResult.unmappedColumns.length > 0) {
      console.log('🔍 Unmapped Columns:');
      parsedResult.unmappedColumns.forEach(col => console.log(`   • ${col}`));
      console.log('   (These will be added to product description)');
    }
    
    console.log('\n🎉 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('\n💡 Note: This script requires Node.js 18+ with fetch support or install node-fetch');
    }
  }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ fetch is not available. Please use Node.js 18+ or install node-fetch');
  console.log('To install node-fetch: npm install node-fetch');
  process.exit(1);
}

// Run the test
testAIMapping();

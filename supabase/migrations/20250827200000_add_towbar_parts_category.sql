-- =====================================================
-- AROUZ MARKET - Add Towbar / parts Category Migration
-- Generated: 2025-08-27T20:00:00.000Z
-- Purpose: Add Towbar / parts category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Towbar / parts category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('towbar-parts', 'Towbar / parts', 'Towbar / parts', 'Complete towbar and towing system components including towbar electric kits, towbars, tow hook covers, pedestal trailer hitches, and all towing-related parts and accessories', 'TOW', 20)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Towbar / parts subcategories (4 total)
-- Tow bars and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('towbar-electric-kit', 'Towbar electric kit', 'Towbar electric kit', 'towbar-parts', 1),
  ('towbar', 'Towbar', 'Towbar', 'towbar-parts', 2),
  ('tow-hook-cover', 'Tow hook cover', 'Tow hook cover', 'towbar-parts', 3),
  ('pedestal-trailer-hitch', 'Pedestal, trailer hitch', 'Pedestal, trailer hitch', 'towbar-parts', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;
    
    RAISE NOTICE '🚀 Creating storage folders for Towbar / parts category...';
    
    -- Create category folder: "Towbar / parts"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Towbar / parts/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'towbar-parts',
                'category_name', 'Towbar / parts',
                'display_name', 'Towbar / parts',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;
        
        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Towbar / parts';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;
    
    -- Create subcategory folders for all 4 subcategories
    FOR subcategory_record IN 
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'towbar-parts'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'towbar-parts',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;
            
            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Towbar / parts/';
    RAISE NOTICE 'subcategory/Towbar electric kit/';
    RAISE NOTICE 'subcategory/Towbar/';
    RAISE NOTICE 'subcategory/Tow hook cover/';
    RAISE NOTICE 'subcategory/Pedestal, trailer hitch/';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Towbar / parts/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';
    
END $$;

-- Verification queries
SELECT 'Towbar / parts category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as towbar_parts_subcategories FROM subcategories WHERE category_id = 'towbar-parts';

-- Show the new towbar / parts category and its subcategories
SELECT 'TOWBAR / PARTS CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'towbar-parts';
SELECT 'TOWBAR / PARTS SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'towbar-parts' ORDER BY sort_order;

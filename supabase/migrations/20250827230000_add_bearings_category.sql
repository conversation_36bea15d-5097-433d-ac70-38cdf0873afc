-- =====================================================
-- AROUZ MARKET - Add Bearings Category Migration
-- Generated: 2025-08-27T23:00:00.000Z
-- Purpose: Add Bearings category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Bearings category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('bearings', 'Bearings', 'Bearings', 'Complete bearing system components including wheel bearings, propshaft bearings, clutch bearings, engine bearings, electrical bearings, and transmission bearings', 'BRG', 23)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Bearings subcategories (13 total)
-- Suspension components (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('wheel-bearing', 'Wheel bearing', 'Wheel bearing', 'bearings', 1),
  ('propshaft-bearing', 'Propshaft bearing', 'Propshaft bearing', 'bearings', 2),
  ('shaft-seal-wheel-hub', 'Shaft seal, wheel hub', 'Shaft seal, wheel hub', 'bearings', 3),
  ('bearing-stub-axle', 'Bearing, stub axle', 'Bearing, stub axle', 'bearings', 4),
  ('intermediate-shaft-bearing', 'Intermediate shaft bearing', 'Intermediate shaft bearing', 'bearings', 5)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Clutch components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('clutch-release-bearing', 'Clutch release bearing', 'Clutch release bearing', 'bearings', 6),
  ('central-slave-cylinder', 'Central slave cylinder', 'Central slave cylinder', 'bearings', 7),
  ('sleeve', 'Sleeve', 'Sleeve', 'bearings', 8)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Engine components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('crankshaft-bearing', 'Crankshaft bearing', 'Crankshaft bearing', 'bearings', 9),
  ('main-bearings-crankshaft', 'Main bearings, crankshaft', 'Main bearings, crankshaft', 'bearings', 10),
  ('camshaft-bushes', 'Camshaft bushes', 'Camshaft bushes', 'bearings', 11)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Electrical components (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('drive-bearing-alternator', 'Drive bearing, alternator', 'Drive bearing, alternator', 'bearings', 12)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Transmission components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('gearbox-bearing', 'Gearbox bearing', 'Gearbox bearing', 'bearings', 13),
  ('flange-lid-manual-transmission', 'Flange lid, manual transmission', 'Flange lid, manual transmission', 'bearings', 14)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Bearings category...';

    -- Create category folder: "Bearings"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Bearings/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'bearings',
                'category_name', 'Bearings',
                'display_name', 'Bearings',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Bearings';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 14 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'bearings'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'bearings',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Bearings/';
    RAISE NOTICE 'subcategory/Wheel bearing/';
    RAISE NOTICE 'subcategory/Propshaft bearing/';
    RAISE NOTICE 'subcategory/Shaft seal, wheel hub/';
    RAISE NOTICE 'subcategory/Bearing, stub axle/';
    RAISE NOTICE 'subcategory/Intermediate shaft bearing/';
    RAISE NOTICE 'subcategory/Clutch release bearing/';
    RAISE NOTICE 'subcategory/Central slave cylinder/';
    RAISE NOTICE 'subcategory/Sleeve/';
    RAISE NOTICE 'subcategory/Crankshaft bearing/';
    RAISE NOTICE 'subcategory/Main bearings, crankshaft/';
    RAISE NOTICE 'subcategory/Camshaft bushes/';
    RAISE NOTICE 'subcategory/Drive bearing, alternator/';
    RAISE NOTICE 'subcategory/Gearbox bearing/';
    RAISE NOTICE 'subcategory/Flange lid, manual transmission/';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Bearings/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Bearings category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as bearings_subcategories FROM subcategories WHERE category_id = 'bearings';

-- Show the new bearings category and its subcategories
SELECT 'BEARINGS CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'bearings';
SELECT 'BEARINGS SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'bearings' ORDER BY sort_order;

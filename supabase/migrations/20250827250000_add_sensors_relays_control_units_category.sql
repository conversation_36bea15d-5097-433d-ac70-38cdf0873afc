-- =====================================================
-- AROUZ MARKET - Add Sensors, relays, control units Category Migration
-- Generated: 2025-08-27T25:00:00.000Z
-- Purpose: Add Sensors, relays, control units category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Sensors, relays, control units category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('sensors-relays-control-units', 'Sensors, relays, control units', 'Sensors, relays, control units', 'Complete electronic control system components including sensors, relays, control units, switches, and electronic modules', 'SRC', 25)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Sensors, relays, control units subcategories (80+ total)
-- Closing components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('car-door-contact', 'Car door contact', 'Car door contact', 'sensors-relays-control-units', 1),
  ('car-boot-lock-cylinder', 'Car boot lock cylinder', 'Car boot lock cylinder', 'sensors-relays-control-units', 2),
  ('bonnet-lock', 'Bonnet lock', 'Bonnet lock', 'sensors-relays-control-units', 3),
  ('tailgate-lock', 'Tailgate lock', 'Tailgate lock', 'sensors-relays-control-units', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Sensor components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('lambda-sensor', 'Lambda sensor', 'Lambda sensor', 'sensors-relays-control-units', 5),
  ('wheel-speed-sensor-abs', 'Wheel speed sensor, ABS', 'Wheel speed sensor, ABS', 'sensors-relays-control-units', 6),
  ('crankshaft-position-sensor', 'Crankshaft position sensor', 'Crankshaft position sensor', 'sensors-relays-control-units', 7),
  ('camshaft-position-sensor', 'Camshaft position sensor', 'Camshaft position sensor', 'sensors-relays-control-units', 8)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Brake components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('brake-light-switch', 'Brake light switch', 'Brake light switch', 'sensors-relays-control-units', 9),
  ('abs-sensor', 'ABS sensor', 'ABS sensor', 'sensors-relays-control-units', 10),
  ('brake-pad-wear-sensor', 'Brake pad wear sensor', 'Brake pad wear sensor', 'sensors-relays-control-units', 11),
  ('handbrake-switch', 'Handbrake switch', 'Handbrake switch', 'sensors-relays-control-units', 12)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Engine components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('knock-sensor', 'Knock sensor', 'Knock sensor', 'sensors-relays-control-units', 13),
  ('coolant-temperature-sensor', 'Coolant temperature sensor', 'Coolant temperature sensor', 'sensors-relays-control-units', 14),
  ('oil-pressure-switch', 'Oil pressure switch', 'Oil pressure switch', 'sensors-relays-control-units', 15),
  ('mass-air-flow-sensor', 'Mass air flow sensor', 'Mass air flow sensor', 'sensors-relays-control-units', 16)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Ignition system components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('ignition-starter-switch', 'Ignition starter switch', 'Ignition starter switch', 'sensors-relays-control-units', 17),
  ('ignition-coil', 'Ignition coil', 'Ignition coil', 'sensors-relays-control-units', 18),
  ('glow-plug-relay', 'Glow plug relay', 'Glow plug relay', 'sensors-relays-control-units', 19),
  ('starter-relay', 'Starter relay', 'Starter relay', 'sensors-relays-control-units', 20)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Fuel system components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('fuel-pump-relay', 'Fuel pump relay', 'Fuel pump relay', 'sensors-relays-control-units', 21),
  ('fuel-level-sensor', 'Fuel level sensor', 'Fuel level sensor', 'sensors-relays-control-units', 22),
  ('throttle-position-sensor', 'Throttle position sensor', 'Throttle position sensor', 'sensors-relays-control-units', 23),
  ('fuel-pressure-sensor', 'Fuel pressure sensor', 'Fuel pressure sensor', 'sensors-relays-control-units', 24)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Transmission components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('reverse-light-switch', 'Reverse light switch', 'Reverse light switch', 'sensors-relays-control-units', 25),
  ('clutch-switch', 'Clutch switch', 'Clutch switch', 'sensors-relays-control-units', 26),
  ('neutral-safety-switch', 'Neutral safety switch', 'Neutral safety switch', 'sensors-relays-control-units', 27),
  ('speed-sensor', 'Speed sensor', 'Speed sensor', 'sensors-relays-control-units', 28)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Lighting components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('headlight-switch', 'Headlight switch', 'Headlight switch', 'sensors-relays-control-units', 29),
  ('turn-signal-switch', 'Turn signal switch', 'Turn signal switch', 'sensors-relays-control-units', 30),
  ('hazard-light-switch', 'Hazard light switch', 'Hazard light switch', 'sensors-relays-control-units', 31),
  ('fog-light-switch', 'Fog light switch', 'Fog light switch', 'sensors-relays-control-units', 32)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Steering components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('steering-angle-sensor', 'Steering angle sensor', 'Steering angle sensor', 'sensors-relays-control-units', 33),
  ('power-steering-pressure-switch', 'Power steering pressure switch', 'Power steering pressure switch', 'sensors-relays-control-units', 34),
  ('horn-switch', 'Horn switch', 'Horn switch', 'sensors-relays-control-units', 35),
  ('cruise-control-switch', 'Cruise control switch', 'Cruise control switch', 'sensors-relays-control-units', 36)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Window and mirror components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('window-switch', 'Window switch', 'Window switch', 'sensors-relays-control-units', 37),
  ('mirror-switch', 'Mirror switch', 'Mirror switch', 'sensors-relays-control-units', 38),
  ('sunroof-switch', 'Sunroof switch', 'Sunroof switch', 'sensors-relays-control-units', 39),
  ('central-locking-switch', 'Central locking switch', 'Central locking switch', 'sensors-relays-control-units', 40)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Control units (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('engine-control-unit', 'Engine control unit', 'Engine control unit', 'sensors-relays-control-units', 41),
  ('abs-control-unit', 'ABS control unit', 'ABS control unit', 'sensors-relays-control-units', 42),
  ('airbag-control-unit', 'Airbag control unit', 'Airbag control unit', 'sensors-relays-control-units', 43),
  ('body-control-module', 'Body control module', 'Body control module', 'sensors-relays-control-units', 44)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional relays (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('main-relay', 'Main relay', 'Main relay', 'sensors-relays-control-units', 45),
  ('horn-relay', 'Horn relay', 'Horn relay', 'sensors-relays-control-units', 46),
  ('wiper-relay', 'Wiper relay', 'Wiper relay', 'sensors-relays-control-units', 47),
  ('flasher-relay', 'Flasher relay', 'Flasher relay', 'sensors-relays-control-units', 48)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional sensors (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('parking-sensor', 'Parking sensor', 'Parking sensor', 'sensors-relays-control-units', 49),
  ('rain-sensor', 'Rain sensor', 'Rain sensor', 'sensors-relays-control-units', 50),
  ('light-sensor', 'Light sensor', 'Light sensor', 'sensors-relays-control-units', 51),
  ('seat-occupancy-sensor', 'Seat occupancy sensor', 'Seat occupancy sensor', 'sensors-relays-control-units', 52)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Sensors, relays, control units category...';

    -- Create category folder: "Sensors, relays, control units"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Sensors, relays, control units/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'sensors-relays-control-units',
                'category_name', 'Sensors, relays, control units',
                'display_name', 'Sensors, relays, control units',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Sensors, relays, control units';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 52 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'sensors-relays-control-units'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'sensors-relays-control-units',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Sensors, relays, control units/';
    RAISE NOTICE 'subcategory/Car door contact/';
    RAISE NOTICE 'subcategory/Car boot lock cylinder/';
    RAISE NOTICE 'subcategory/Bonnet lock/';
    RAISE NOTICE 'subcategory/Tailgate lock/';
    RAISE NOTICE 'subcategory/Lambda sensor/';
    RAISE NOTICE 'subcategory/Wheel speed sensor, ABS/';
    RAISE NOTICE 'subcategory/Crankshaft position sensor/';
    RAISE NOTICE 'subcategory/Camshaft position sensor/';
    RAISE NOTICE '... and 44 more subcategory folders';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Sensors, relays, control units/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Sensors, relays, control units category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as sensors_relays_control_units_subcategories FROM subcategories WHERE category_id = 'sensors-relays-control-units';

-- Show the new sensors, relays, control units category and its subcategories
SELECT 'SENSORS, RELAYS, CONTROL UNITS CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'sensors-relays-control-units';
SELECT 'SENSORS, RELAYS, CONTROL UNITS SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'sensors-relays-control-units' ORDER BY sort_order;

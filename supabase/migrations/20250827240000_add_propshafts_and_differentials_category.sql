-- =====================================================
-- AROUZ MARKET - Add Propshafts & Differentials Category Migration
-- Generated: 2025-08-27T24:00:00.000Z
-- Purpose: Add Propshafts & Differentials category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Propshafts & Differentials category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('propshafts-and-differentials', 'Propshafts and differentials', 'Propshafts & Differentials', 'Complete propshaft and differential system components including propshaft parts, differential components, transmission fluids, and transfer case parts', 'PD', 24)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Propshafts & Differentials subcategories (12 total)
-- Propeller shaft and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('propshaft-coupling', 'Propshaft coupling', 'Propshaft coupling', 'propshafts-and-differentials', 1),
  ('propshaft-bearing', 'Propshaft bearing', 'Propshaft bearing', 'propshafts-and-differentials', 2),
  ('propshaft', 'Propshaft', 'Propshaft', 'propshafts-and-differentials', 3),
  ('centering-bush-propshaft', 'Centering bush, propshaft', 'Centering bush, propshaft', 'propshafts-and-differentials', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Differentials and related components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('differential-seal', 'Differential seal', 'Differential seal', 'propshafts-and-differentials', 5),
  ('differential-parts', 'Differential parts', 'Differential parts', 'propshafts-and-differentials', 6),
  ('repair-kit-differential', 'Repair kit, differential', 'Repair kit, differential', 'propshafts-and-differentials', 7)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Oils and fluids (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('gearbox-oil-and-transmission-fluid', 'Gearbox oil and transmission fluid', 'Gearbox oil and transmission fluid', 'propshafts-and-differentials', 8),
  ('automatic-transmission-fluid', 'Automatic transmission fluid', 'Automatic transmission fluid', 'propshafts-and-differentials', 9)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Transfer case components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('mounting-transfer-gear', 'Mounting, transfer gear', 'Mounting, transfer gear', 'propshafts-and-differentials', 10),
  ('shaft-seal-transfer-case', 'Shaft seal, transfer case', 'Shaft seal, transfer case', 'propshafts-and-differentials', 11),
  ('transfer-case-parts', 'Transfer case parts', 'Transfer case parts', 'propshafts-and-differentials', 12)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Propshafts & Differentials category...';

    -- Create category folder: "Propshafts & Differentials"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Propshafts & Differentials/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'propshafts-and-differentials',
                'category_name', 'Propshafts and differentials',
                'display_name', 'Propshafts & Differentials',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Propshafts & Differentials';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 12 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'propshafts-and-differentials'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'propshafts-and-differentials',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Propshafts & Differentials/';
    RAISE NOTICE 'subcategory/Propshaft coupling/';
    RAISE NOTICE 'subcategory/Propshaft bearing/';
    RAISE NOTICE 'subcategory/Propshaft/';
    RAISE NOTICE 'subcategory/Centering bush, propshaft/';
    RAISE NOTICE 'subcategory/Differential seal/';
    RAISE NOTICE 'subcategory/Differential parts/';
    RAISE NOTICE 'subcategory/Repair kit, differential/';
    RAISE NOTICE 'subcategory/Gearbox oil and transmission fluid/';
    RAISE NOTICE 'subcategory/Automatic transmission fluid/';
    RAISE NOTICE 'subcategory/Mounting, transfer gear/';
    RAISE NOTICE 'subcategory/Shaft seal, transfer case/';
    RAISE NOTICE 'subcategory/Transfer case parts/';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Propshafts & Differentials/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Propshafts & Differentials category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as propshafts_differentials_subcategories FROM subcategories WHERE category_id = 'propshafts-and-differentials';

-- Show the new propshafts & differentials category and its subcategories
SELECT 'PROPSHAFTS & DIFFERENTIALS CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'propshafts-and-differentials';
SELECT 'PROPSHAFTS & DIFFERENTIALS SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'propshafts-and-differentials' ORDER BY sort_order;

-- =====================================================
-- AROUZ MARKET - Add Interior Category Migration
-- Generated: 2025-08-27T14:00:00.000Z
-- Purpose: Add Interior category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Interior category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('interior', 'Interior', 'Interior', 'Complete interior components including seats, dashboard, controls, lighting, climate control, locks, and all cabin-related parts and accessories', 'INT', 14)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Interior subcategories (49 total)
-- Gas springs (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('boot-struts', 'Boot struts', 'Boot struts', 'interior', 1),
  ('gas-spring-folding-top', 'Gas spring, folding top', 'Gas spring, folding top', 'interior', 2),
  ('gas-spring-foldaway-table', 'Gas spring, foldaway table', 'Gas spring, foldaway table', 'interior', 3)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Power windows and related components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('window-regulator', 'Window regulator', 'Window regulator', 'interior', 4),
  ('window-switch', 'Window switch', 'Window switch', 'interior', 5),
  ('window-winder-handle', 'Window winder handle', 'Window winder handle', 'interior', 6)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Interior parts (10 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('boot', 'Boot', 'Boot', 'interior', 7),
  ('floor-mats', 'Floor mats', 'Floor mats', 'interior', 8),
  ('gear-stick-knob', 'Gear stick knob', 'Gear stick knob', 'interior', 9),
  ('pedals-and-pedal-covers', 'Pedals and pedal covers', 'Pedals and pedal covers', 'interior', 10),
  ('seat-adjustment', 'Seat adjustment', 'Seat adjustment', 'interior', 11),
  ('rear-view-mirror', 'Rear view mirror', 'Rear view mirror', 'interior', 12),
  ('handbrake-switch', 'Handbrake switch', 'Handbrake switch', 'interior', 13),
  ('cup-holder', 'Cup holder', 'Cup holder', 'interior', 14),
  ('sun-visor', 'Sun visor', 'Sun visor', 'interior', 15),
  ('panelling', 'Panelling', 'Panelling', 'interior', 16),
  ('safety-belt', 'Safety belt', 'Safety belt', 'interior', 17)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Locks and related components (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('door-lock', 'Door lock', 'Door lock', 'interior', 18),
  ('door-handle', 'Door handle', 'Door handle', 'interior', 19),
  ('door-lock-barrel', 'Door lock barrel', 'Door lock barrel', 'interior', 20),
  ('ignition-switch', 'Ignition switch', 'Ignition switch', 'interior', 21),
  ('central-locking', 'Central locking', 'Central locking', 'interior', 22),
  ('locking-knob', 'Locking knob', 'Locking knob', 'interior', 23),
  ('interior-locks', 'Interior locks', 'Interior locks', 'interior', 24)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Electrical components (12 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('indicator-stalk', 'Indicator stalk', 'Indicator stalk', 'interior', 25),
  ('aerial', 'Aerial', 'Aerial', 'interior', 26),
  ('parking-sensors', 'Parking sensors', 'Parking sensors', 'interior', 27),
  ('lighting-controls', 'Lighting controls', 'Lighting controls', 'interior', 28),
  ('headlight-switch', 'Headlight switch', 'Headlight switch', 'interior', 29),
  ('brake-light-switch', 'Brake light switch', 'Brake light switch', 'interior', 30),
  ('speedometer-cable', 'Speedometer cable', 'Speedometer cable', 'interior', 31),
  ('reverse-light-switch', 'Reverse light switch', 'Reverse light switch', 'interior', 32),
  ('mirror-switch', 'Mirror switch', 'Mirror switch', 'interior', 33),
  ('hazard-switch', 'Hazard switch', 'Hazard switch', 'interior', 34),
  ('accelerator-pedal', 'Accelerator pedal', 'Accelerator pedal', 'interior', 35),
  ('window-motor', 'Window motor', 'Window motor', 'interior', 36),
  ('steering-angle-sensor', 'Steering angle sensor', 'Steering angle sensor', 'interior', 37),
  ('rain-sensor', 'Rain sensor', 'Rain sensor', 'interior', 38),
  ('multifunctional-relay', 'Multifunctional relay', 'Multifunctional relay', 'interior', 39),
  ('central-electrics', 'Central electrics', 'Central electrics', 'interior', 40)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Lighting and signaling components (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('interior-lights', 'Interior lights', 'Interior lights', 'interior', 41),
  ('door-lights', 'Door lights', 'Door lights', 'interior', 42),
  ('boot-light', 'Boot light', 'Boot light', 'interior', 43),
  ('indicator-relay', 'Indicator relay', 'Indicator relay', 'interior', 44),
  ('door-contact-switch', 'Door contact switch', 'Door contact switch', 'interior', 45),
  ('fog-light-switch', 'Fog light switch', 'Fog light switch', 'interior', 46)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Climate control system components (7 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('heater-resistor', 'Heater resistor', 'Heater resistor', 'interior', 47),
  ('blower-control-unit', 'Blower control unit', 'Blower control unit', 'interior', 48),
  ('auxiliary-water-pump', 'Auxiliary water pump', 'Auxiliary water pump', 'interior', 49),
  ('outside-temperature-sensor', 'Outside temperature sensor', 'Outside temperature sensor', 'interior', 50),
  ('ac-relay', 'AC relay', 'AC relay', 'interior', 51),
  ('interior-temperature-sensor', 'Interior temperature sensor', 'Interior temperature sensor', 'interior', 52),
  ('ac-control-unit', 'AC control unit', 'AC control unit', 'interior', 53)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Controls and related components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('clutch-switch', 'Clutch switch', 'Clutch switch', 'interior', 54),
  ('accelerator-pedal-position-sensor', 'Accelerator pedal position sensor', 'Accelerator pedal position sensor', 'interior', 55)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Interior category...';

    -- Create category folder: "Interior"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Interior/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'interior',
                'category_name', 'Interior',
                'display_name', 'Interior',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Interior';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 55 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'interior'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'interior',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Interior/';
    RAISE NOTICE 'subcategory/Boot struts/';
    RAISE NOTICE 'subcategory/Window regulator/';
    RAISE NOTICE 'subcategory/Floor mats/';
    RAISE NOTICE 'subcategory/Safety belt/';
    RAISE NOTICE '... (and 51 more subcategory folders)';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Interior/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Interior category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as interior_subcategories FROM subcategories WHERE category_id = 'interior';

-- Show the new interior category and its subcategories
SELECT 'INTERIOR CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'interior';
SELECT 'INTERIOR SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'interior' ORDER BY sort_order;

-- =====================================================
-- AROUZ MARKET - Add Steering Category Migration
-- Generated: 2025-08-27T16:00:00.000Z
-- Purpose: Add Steering category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Steering category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('steering', 'Steering', 'Steering', 'Complete steering system components including racks, pumps, tie rods, ball joints, power steering components, and all steering-related parts and accessories', 'STR', 16)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Steering subcategories (25 total)
-- Tie rod ends and related components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('track-rod-end', 'Track rod end', 'Track rod end', 'steering', 1),
  ('repair-kit-tie-rod-end', 'Repair kit, tie rod end', 'Repair kit, tie rod end', 'steering', 2)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Tie rods, drag links, and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('tie-rod', 'Tie rod', 'Tie rod', 'steering', 3),
  ('inner-tie-rod', 'Inner tie rod', 'Inner tie rod', 'steering', 4),
  ('steering-rack-boot', 'Steering rack boot', 'Steering rack boot', 'steering', 5),
  ('centre-rod-assembly', 'Centre rod assembly', 'Centre rod assembly', 'steering', 6)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Ball joints and related components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('ball-joint', 'Ball joint', 'Ball joint', 'steering', 7),
  ('repair-kit-support-steering-link', 'Repair kit, support- / steering link', 'Repair kit, support- / steering link', 'steering', 8)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Tools (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('steering-tools', 'Steering tools', 'Steering tools', 'steering', 9)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Power steering pumps and related components (7 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('power-steering-pump', 'Power steering pump', 'Power steering pump', 'steering', 10),
  ('steering-hose-pipe', 'Steering hose / pipe', 'Steering hose / pipe', 'steering', 11),
  ('hydraulic-oil-expansion-tank', 'Hydraulic oil expansion tank', 'Hydraulic oil expansion tank', 'steering', 12),
  ('power-steering-filter', 'Power steering filter', 'Power steering filter', 'steering', 13),
  ('power-steering-fluid', 'Power steering fluid', 'Power steering fluid', 'steering', 14),
  ('power-steering-pump-pulley', 'Power steering pump pulley', 'Power steering pump pulley', 'steering', 15),
  ('gasket-set-hydraulic-pump', 'Gasket set, hydraulic pump', 'Gasket set, hydraulic pump', 'steering', 16),
  ('oil-cooler-steering-system', 'Oil cooler, steering system', 'Oil cooler, steering system', 'steering', 17)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Steering components (9 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('steering-rack', 'Steering rack', 'Steering rack', 'steering', 18),
  ('steering-damper', 'Steering damper', 'Steering damper', 'steering', 19),
  ('steering-rack-repair-kit', 'Steering rack repair kit', 'Steering rack repair kit', 'steering', 20),
  ('steering-column-universal-joint', 'Steering column universal joint', 'Steering column universal joint', 'steering', 21),
  ('steering-mounting', 'Steering mounting', 'Steering mounting', 'steering', 22),
  ('steering-angle-sensor', 'Steering angle sensor', 'Steering angle sensor', 'steering', 23),
  ('electric-power-steering-column', 'Electric power steering column', 'Electric power steering column', 'steering', 24),
  ('power-steering-pressure-switch', 'Power steering pressure switch', 'Power steering pressure switch', 'steering', 25),
  ('steering-shaft', 'Steering shaft', 'Steering shaft', 'steering', 26)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Steering idler arms and related components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('steering-linkage', 'Steering linkage', 'Steering linkage', 'steering', 27),
  ('steering-arm', 'Steering arm', 'Steering arm', 'steering', 28),
  ('bushing-drop-arm-shaft', 'Bushing, drop arm shaft', 'Bushing, drop arm shaft', 'steering', 29)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Steering category...';

    -- Create category folder: "Steering"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Steering/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'steering',
                'category_name', 'Steering',
                'display_name', 'Steering',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Steering';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 29 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'steering'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'steering',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Steering/';
    RAISE NOTICE 'subcategory/Track rod end/';
    RAISE NOTICE 'subcategory/Tie rod/';
    RAISE NOTICE 'subcategory/Ball joint/';
    RAISE NOTICE 'subcategory/Power steering pump/';
    RAISE NOTICE 'subcategory/Steering rack/';
    RAISE NOTICE '... (and 24 more subcategory folders)';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Steering/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Steering category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as steering_subcategories FROM subcategories WHERE category_id = 'steering';

-- Show the new steering category and its subcategories
SELECT 'STEERING CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'steering';
SELECT 'STEERING SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'steering' ORDER BY sort_order;

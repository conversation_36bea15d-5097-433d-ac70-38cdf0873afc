-- =====================================================
-- AROUZ MARKET - Add Tuning Category Migration
-- Generated: 2025-08-27T28:00:00.000Z
-- Purpose: Add Tuning category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Tuning category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('tuning', 'Tuning', 'Tuning', 'Complete automotive tuning and performance enhancement components including performance clutch, suspension, exhaust, brake pads, grilles, brake discs, wheel spacers, air filters, chip tuning, and strut braces', 'TN', 28)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Tuning subcategories (9 total)
-- Performance components (9 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('performance-clutch', 'Performance clutch', 'Performance clutch', 'tuning', 1),
  ('sport-suspension', 'Sport suspension', 'Sport suspension', 'tuning', 2),
  ('performance-exhaust', 'Performance exhaust', 'Performance exhaust', 'tuning', 3),
  ('racing-brake-pads', 'Racing brake pads', 'Racing brake pads', 'tuning', 4),
  ('sport-grille', 'Sport grille', 'Sport grille', 'tuning', 5),
  ('performance-brake-discs', 'Performance brake discs', 'Performance brake discs', 'tuning', 6),
  ('wheel-spacers', 'Wheel spacers', 'Wheel spacers', 'tuning', 7),
  ('performance-air-filter', 'Performance air filter', 'Performance air filter', 'tuning', 8),
  ('chip-tuning-box', 'Chip tuning box', 'Chip tuning box', 'tuning', 9)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional performance component
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('strut-brace', 'Strut brace', 'Strut brace', 'tuning', 10)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Tuning category...';

    -- Create category folder: "Tuning"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Tuning/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'tuning',
                'category_name', 'Tuning',
                'display_name', 'Tuning',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Tuning';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 10 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'tuning'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'tuning',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Tuning/';
    RAISE NOTICE 'subcategory/Performance clutch/';
    RAISE NOTICE 'subcategory/Sport suspension/';
    RAISE NOTICE 'subcategory/Performance exhaust/';
    RAISE NOTICE 'subcategory/Racing brake pads/';
    RAISE NOTICE 'subcategory/Sport grille/';
    RAISE NOTICE 'subcategory/Performance brake discs/';
    RAISE NOTICE '... and 4 more subcategory folders';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Tuning/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Tuning category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as tuning_subcategories FROM subcategories WHERE category_id = 'tuning';

-- Show the new tuning category and its subcategories
SELECT 'TUNING CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'tuning';
SELECT 'TUNING SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'tuning' ORDER BY sort_order;

-- Create search analytics table for tracking search performance and user behavior
-- This migration creates the infrastructure for intelligent search analytics

-- Drop existing views and tables if they exist to avoid conflicts
DROP VIEW IF EXISTS search_performance_summary CASCADE;
DROP VIEW IF EXISTS popular_search_queries CASCADE;
DROP VIEW IF EXISTS zero_results_queries CASCADE;
DROP TABLE IF EXISTS search_analytics CASCADE;
DROP TABLE IF EXISTS search_suggestions_cache CASCADE;

-- 1. Create search analytics table
CREATE TABLE search_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  query TEXT NOT NULL,
  search_type TEXT NOT NULL CHECK (search_type IN ('text', 'suggestion', 'recent', 'popular')),
  results_count INTEGER NOT NULL DEFAULT 0,
  search_time_ms INTEGER NOT NULL DEFAULT 0,
  filters_applied JSONB DEFAULT '{}',
  clicked_result_id TEXT,
  clicked_result_position INTEGER,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_agent TEXT,
  language TEXT DEFAULT 'en',
  
  -- Constraints
  CONSTRAINT valid_results_count CHECK (results_count >= 0),
  CONSTRAINT valid_search_time CHECK (search_time_ms >= 0),
  CONSTRAINT valid_position CHECK (clicked_result_position IS NULL OR clicked_result_position > 0)
);

-- 2. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_search_analytics_timestamp ON search_analytics(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_search_analytics_query ON search_analytics(query);
CREATE INDEX IF NOT EXISTS idx_search_analytics_session ON search_analytics(session_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_user ON search_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_search_type ON search_analytics(search_type);
CREATE INDEX IF NOT EXISTS idx_search_analytics_results_count ON search_analytics(results_count);
CREATE INDEX IF NOT EXISTS idx_search_analytics_language ON search_analytics(language);

-- 3. Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_search_analytics_query_timestamp ON search_analytics(query, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_search_analytics_session_timestamp ON search_analytics(session_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_search_analytics_type_timestamp ON search_analytics(search_type, timestamp DESC);

-- 4. Create search suggestions cache table
CREATE TABLE search_suggestions_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  query_prefix TEXT NOT NULL,
  suggestions JSONB NOT NULL DEFAULT '[]',
  language TEXT NOT NULL DEFAULT 'en',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 hour'),
  
  -- Unique constraint for query prefix and language
  UNIQUE(query_prefix, language)
);

-- 5. Create indexes for suggestions cache
CREATE INDEX IF NOT EXISTS idx_search_suggestions_prefix ON search_suggestions_cache(query_prefix);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_language ON search_suggestions_cache(language);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_expires ON search_suggestions_cache(expires_at);

-- 6. Create search performance summary view
CREATE OR REPLACE VIEW search_performance_summary AS
SELECT
  DATE(search_analytics.timestamp) as date,
  language,
  COUNT(*) as total_searches,
  AVG(search_time_ms) as avg_search_time_ms,
  COUNT(CASE WHEN results_count > 0 THEN 1 END) as successful_searches,
  ROUND(
    (COUNT(CASE WHEN results_count > 0 THEN 1 END)::DECIMAL / COUNT(*)) * 100,
    2
  ) as success_rate_percent,
  COUNT(CASE WHEN search_time_ms > 1000 THEN 1 END) as slow_searches,
  COUNT(CASE WHEN clicked_result_id IS NOT NULL THEN 1 END) as searches_with_clicks
FROM search_analytics
WHERE search_analytics.timestamp >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(search_analytics.timestamp), language
ORDER BY date DESC, language;

-- 7. Create popular queries view
CREATE OR REPLACE VIEW popular_search_queries AS
SELECT
  query,
  language,
  COUNT(*) as search_count,
  AVG(results_count) as avg_results,
  AVG(search_time_ms) as avg_search_time_ms,
  COUNT(CASE WHEN clicked_result_id IS NOT NULL THEN 1 END) as click_count,
  ROUND(
    (COUNT(CASE WHEN clicked_result_id IS NOT NULL THEN 1 END)::DECIMAL / COUNT(*)) * 100,
    2
  ) as click_through_rate_percent,
  MAX(search_analytics.timestamp) as last_searched
FROM search_analytics
WHERE search_analytics.timestamp >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY query, language
HAVING COUNT(*) >= 2  -- Only show queries searched at least twice
ORDER BY search_count DESC, click_through_rate_percent DESC
LIMIT 100;

-- 8. Create zero results queries view
CREATE OR REPLACE VIEW zero_results_queries AS
SELECT
  query,
  language,
  COUNT(*) as search_count,
  AVG(search_time_ms) as avg_search_time_ms,
  MAX(search_analytics.timestamp) as last_searched
FROM search_analytics
WHERE results_count = 0
  AND search_analytics.timestamp >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY query, language
HAVING COUNT(*) >= 2  -- Only show queries with multiple zero results
ORDER BY search_count DESC
LIMIT 50;

-- 9. Create function to clean old analytics data
CREATE OR REPLACE FUNCTION cleanup_old_search_analytics()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete analytics data older than 90 days
  DELETE FROM search_analytics
  WHERE search_analytics.timestamp < CURRENT_DATE - INTERVAL '90 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Delete expired suggestion cache entries
  DELETE FROM search_suggestions_cache 
  WHERE expires_at < NOW();
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 10. Create function to update suggestion cache
CREATE OR REPLACE FUNCTION update_search_suggestions_cache()
RETURNS VOID AS $$
BEGIN
  -- Update suggestions cache with popular queries
  INSERT INTO search_suggestions_cache (query_prefix, suggestions, language, updated_at, expires_at)
  SELECT 
    LEFT(query, 3) as query_prefix,
    jsonb_agg(
      jsonb_build_object(
        'text', query,
        'type', 'popular',
        'count', search_count
      ) ORDER BY search_count DESC
    ) as suggestions,
    language,
    NOW() as updated_at,
    NOW() + INTERVAL '1 hour' as expires_at
  FROM popular_search_queries
  WHERE LENGTH(query) >= 3
  GROUP BY LEFT(query, 3), language
  ON CONFLICT (query_prefix, language) 
  DO UPDATE SET 
    suggestions = EXCLUDED.suggestions,
    updated_at = EXCLUDED.updated_at,
    expires_at = EXCLUDED.expires_at;
END;
$$ LANGUAGE plpgsql;

-- 11. Enable Row Level Security
ALTER TABLE search_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_suggestions_cache ENABLE ROW LEVEL SECURITY;

-- 12. Create RLS policies for search analytics
-- Allow users to insert their own analytics data
CREATE POLICY "Users can insert search analytics" ON search_analytics
  FOR INSERT WITH CHECK (
    user_id IS NULL OR user_id = auth.uid()
  );

-- Allow users to read their own analytics data
CREATE POLICY "Users can read own search analytics" ON search_analytics
  FOR SELECT USING (
    user_id IS NULL OR user_id = auth.uid()
  );

-- Allow anonymous users to insert analytics (for non-authenticated searches)
CREATE POLICY "Anonymous can insert search analytics" ON search_analytics
  FOR INSERT WITH CHECK (user_id IS NULL);

-- 13. Create RLS policies for suggestions cache
-- Allow everyone to read suggestions cache
CREATE POLICY "Everyone can read suggestions cache" ON search_suggestions_cache
  FOR SELECT USING (true);

-- Only allow system to update suggestions cache
CREATE POLICY "System can manage suggestions cache" ON search_suggestions_cache
  FOR ALL USING (false);

-- 14. Create scheduled job to cleanup old data (if pg_cron is available)
-- This would typically be set up separately in production
-- SELECT cron.schedule('cleanup-search-analytics', '0 2 * * *', 'SELECT cleanup_old_search_analytics();');
-- SELECT cron.schedule('update-search-suggestions', '*/30 * * * *', 'SELECT update_search_suggestions_cache();');

-- 15. Add comments for documentation
COMMENT ON TABLE search_analytics IS 'Tracks search queries, performance metrics, and user interactions for analytics and optimization';
COMMENT ON TABLE search_suggestions_cache IS 'Caches search suggestions for improved performance';
COMMENT ON VIEW search_performance_summary IS 'Daily summary of search performance metrics';
COMMENT ON VIEW popular_search_queries IS 'Most popular search queries with performance metrics';
COMMENT ON VIEW zero_results_queries IS 'Queries that consistently return zero results for optimization';

COMMENT ON COLUMN search_analytics.session_id IS 'Unique session identifier for grouping related searches';
COMMENT ON COLUMN search_analytics.search_type IS 'Type of search: text, suggestion, recent, or popular';
COMMENT ON COLUMN search_analytics.search_time_ms IS 'Search execution time in milliseconds';
COMMENT ON COLUMN search_analytics.filters_applied IS 'JSON object containing applied search filters';
COMMENT ON COLUMN search_analytics.clicked_result_id IS 'Product ID if user clicked on a search result';
COMMENT ON COLUMN search_analytics.clicked_result_position IS 'Position of clicked result in search results (1-based)';

-- 16. Grant necessary permissions
-- Grant usage to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT ON search_analytics TO authenticated;
GRANT SELECT ON search_suggestions_cache TO authenticated;
GRANT SELECT ON search_performance_summary TO authenticated;
GRANT SELECT ON popular_search_queries TO authenticated;
GRANT SELECT ON zero_results_queries TO authenticated;

-- Grant usage to anonymous users for analytics
GRANT USAGE ON SCHEMA public TO anon;
GRANT INSERT ON search_analytics TO anon;
GRANT SELECT ON search_suggestions_cache TO anon;

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Search analytics infrastructure created successfully';
  RAISE NOTICE '📊 Tables: search_analytics, search_suggestions_cache';
  RAISE NOTICE '📈 Views: search_performance_summary, popular_search_queries, zero_results_queries';
  RAISE NOTICE '🔧 Functions: cleanup_old_search_analytics, update_search_suggestions_cache';
  RAISE NOTICE '🔒 RLS policies enabled for data security';
END $$;

-- =====================================================
-- AROUZ MARKET - Add Air suspension Category Migration
-- Generated: 2025-08-27T19:00:00.000Z
-- Purpose: Add Air suspension category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Air suspension category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('air-suspension', 'Air suspension', 'Air suspension', 'Complete air suspension system components including air suspension struts, boots, compressors, relay controls, and all air suspension-related parts and accessories', 'AIR', 19)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Air suspension subcategories (4 total)
-- Air suspension components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('air-suspension-strut', 'Air suspension', 'Air suspension', 'air-suspension', 1),
  ('air-suspension-boot', 'Air suspension boot', 'Air suspension boot', 'air-suspension', 2),
  ('air-suspension-compressor', 'Air suspension compressor', 'Air suspension compressor', 'air-suspension', 3),
  ('relay-levelling-control', 'Relay, levelling control', 'Relay, levelling control', 'air-suspension', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;
    
    RAISE NOTICE '🚀 Creating storage folders for Air suspension category...';
    
    -- Create category folder: "Air suspension"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Air suspension/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'air-suspension',
                'category_name', 'Air suspension',
                'display_name', 'Air suspension',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;
        
        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Air suspension';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;
    
    -- Create subcategory folders for all 4 subcategories
    FOR subcategory_record IN 
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'air-suspension'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'air-suspension',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;
            
            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Air suspension/';
    RAISE NOTICE 'subcategory/Air suspension/';
    RAISE NOTICE 'subcategory/Air suspension boot/';
    RAISE NOTICE 'subcategory/Air suspension compressor/';
    RAISE NOTICE 'subcategory/Relay, levelling control/';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Air suspension/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';
    
END $$;

-- Verification queries
SELECT 'Air suspension category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as air_suspension_subcategories FROM subcategories WHERE category_id = 'air-suspension';

-- Show the new air suspension category and its subcategories
SELECT 'AIR SUSPENSION CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'air-suspension';
SELECT 'AIR SUSPENSION SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'air-suspension' ORDER BY sort_order;

-- =====================================================
-- AROUZ MARKET - Add Repair kits Category Migration
-- Generated: 2025-08-27T26:00:00.000Z
-- Purpose: Add Repair kits category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Repair kits category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('repair-kits', 'Repair kits', 'Repair kits', 'Complete repair kit components for all vehicle systems including engine, brake, electrical, suspension, clutch, transmission, fuel, and steering system repair kits', 'RK', 26)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Repair kits subcategories (25 total)
-- Engine components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('repair-set-crankcase-breather', 'Repair set, crankcase breather', 'Repair set, crankcase breather', 'repair-kits', 1),
  ('valve-engine-block-breather', 'Valve, engine block breather', 'Valve, engine block breather', 'repair-kits', 2)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Brake components (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('brake-caliper-repair-kit', 'Brake caliper repair kit', 'Brake caliper repair kit', 'repair-kits', 3),
  ('guide-sleeve-kit-brake-caliper', 'Guide sleeve kit, brake caliper', 'Guide sleeve kit, brake caliper', 'repair-kits', 4),
  ('accessory-kit-brake-shoes', 'Accessory kit, brake shoes', 'Accessory kit, brake shoes', 'repair-kits', 5),
  ('master-cylinder-repair-kit', 'Master cylinder repair kit', 'Master cylinder repair kit', 'repair-kits', 6),
  ('repair-kit-wheel-brake-cylinder', 'Repair kit, wheel brake cylinder', 'Repair kit, wheel brake cylinder', 'repair-kits', 7)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional brake component
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('handbrake-repair-kit', 'Handbrake repair kit', 'Handbrake repair kit', 'repair-kits', 8)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Electrical components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('alternator-parts', 'Alternator parts', 'Alternator parts', 'repair-kits', 9),
  ('starter-motor-parts', 'Starter motor parts', 'Starter motor parts', 'repair-kits', 10),
  ('repair-kit-distributor', 'Repair kit, distributor', 'Repair kit, distributor', 'repair-kits', 11)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Suspension components (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('suspension-kit', 'Suspension kit', 'Suspension kit', 'repair-kits', 12),
  ('anti-roll-bar-stabiliser-kit', 'Anti-roll bar stabiliser kit', 'Anti-roll bar stabiliser kit', 'repair-kits', 13),
  ('repair-kit-wheel-suspension', 'Repair kit, wheel suspension', 'Repair kit, wheel suspension', 'repair-kits', 14),
  ('mounting-kit-control-lever', 'Mounting kit, control lever', 'Mounting kit, control lever', 'repair-kits', 15),
  ('repair-kit-support-steering-link', 'Repair kit, support-/ steering link', 'Repair kit, support-/ steering link', 'repair-kits', 16)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Clutch components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('repair-kit-clutch-master-cylinder', 'Repair kit, clutch master cylinder', 'Repair kit, clutch master cylinder', 'repair-kits', 17),
  ('clutch-slave-cylinder-repair-kit', 'Clutch slave cylinder repair kit', 'Clutch slave cylinder repair kit', 'repair-kits', 18),
  ('release-set-clutch-operation', 'Release set, clutch operation', 'Release set, clutch operation', 'repair-kits', 19)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Transmission components (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('gear-lever-repair-kit', 'Gear lever repair kit', 'Gear lever repair kit', 'repair-kits', 20),
  ('repair-kit-automatic-adjustment', 'Repair kit, automatic adjustment', 'Repair kit, automatic adjustment', 'repair-kits', 21),
  ('gear-linkage-repair-kit', 'Gear linkage repair kit', 'Gear linkage repair kit', 'repair-kits', 22),
  ('repair-kit-differential', 'Repair kit, differential', 'Repair kit, differential', 'repair-kits', 23),
  ('repair-kit-manual-transmission-flange', 'Repair kit, manual transmission flange', 'Repair kit, manual transmission flange', 'repair-kits', 24)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Fuel system components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('carburettor-repair-kit', 'Carburettor repair kit', 'Carburettor repair kit', 'repair-kits', 25),
  ('injector-repair-kit', 'Injector repair kit', 'Injector repair kit', 'repair-kits', 26),
  ('fuel-pump-repair-kit', 'Fuel pump repair kit', 'Fuel pump repair kit', 'repair-kits', 27)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Steering system components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('steering-rack-repair-kit', 'Steering rack repair kit', 'Steering rack repair kit', 'repair-kits', 28),
  ('gasket-set-hydraulic-pump', 'Gasket set, hydraulic pump', 'Gasket set, hydraulic pump', 'repair-kits', 29)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Repair kits category...';

    -- Create category folder: "Repair kits"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Repair kits/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'repair-kits',
                'category_name', 'Repair kits',
                'display_name', 'Repair kits',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Repair kits';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 29 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'repair-kits'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'repair-kits',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Repair kits/';
    RAISE NOTICE 'subcategory/Repair set, crankcase breather/';
    RAISE NOTICE 'subcategory/Valve, engine block breather/';
    RAISE NOTICE 'subcategory/Brake caliper repair kit/';
    RAISE NOTICE 'subcategory/Guide sleeve kit, brake caliper/';
    RAISE NOTICE 'subcategory/Accessory kit, brake shoes/';
    RAISE NOTICE 'subcategory/Master cylinder repair kit/';
    RAISE NOTICE '... and 23 more subcategory folders';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Repair kits/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Repair kits category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as repair_kits_subcategories FROM subcategories WHERE category_id = 'repair-kits';

-- Show the new repair kits category and its subcategories
SELECT 'REPAIR KITS CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'repair-kits';
SELECT 'REPAIR KITS SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'repair-kits' ORDER BY sort_order;

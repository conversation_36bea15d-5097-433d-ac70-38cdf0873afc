-- =====================================================
-- AROUZ MARKET - Add Fasteners Category Migration
-- Generated: 2025-08-27T29:00:00.000Z
-- Purpose: Add Fasteners category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Fasteners category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('fasteners', 'Fasteners', 'Fasteners', 'Complete automotive fasteners and hardware including universal hoses, pipes, clamps, gaskets, O-rings, rivets, nuts, and screws for all vehicle assembly and repair needs', 'FS', 29)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Fasteners subcategories (6 total)
-- Hardware and fastening components (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('universal-hoses-pipes', 'Universal hoses/pipes', 'Universal hoses/pipes', 'fasteners', 1),
  ('clamps', 'Clamps', 'Clamps', 'fasteners', 2),
  ('universal-gaskets-o-rings', 'Universal gaskets & O-rings', 'Universal gaskets & O-rings', 'fasteners', 3),
  ('rivets', 'Rivets', 'Rivets', 'fasteners', 4),
  ('nuts', 'Nuts', 'Nuts', 'fasteners', 5),
  ('screws', 'Screws', 'Screws', 'fasteners', 6)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Fasteners category...';

    -- Create category folder: "Fasteners"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Fasteners/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'fasteners',
                'category_name', 'Fasteners',
                'display_name', 'Fasteners',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Fasteners';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 6 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'fasteners'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'fasteners',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Fasteners/';
    RAISE NOTICE 'subcategory/Universal hoses/pipes/';
    RAISE NOTICE 'subcategory/Clamps/';
    RAISE NOTICE 'subcategory/Universal gaskets & O-rings/';
    RAISE NOTICE 'subcategory/Rivets/';
    RAISE NOTICE 'subcategory/Nuts/';
    RAISE NOTICE 'subcategory/Screws/';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Fasteners/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Fasteners category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as fasteners_subcategories FROM subcategories WHERE category_id = 'fasteners';

-- Show the new fasteners category and its subcategories
SELECT 'FASTENERS CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'fasteners';
SELECT 'FASTENERS SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'fasteners' ORDER BY sort_order;

-- =====================================================
-- AROUZ MARKET - Add Gearbox Category Migration
-- Generated: 2025-08-27T21:00:00.000Z
-- Purpose: Add Gearbox category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Gearbox category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('gearbox', 'Gearbox', 'Gearbox', 'Complete gearbox and transmission system components including transmission fluids, filters, seals, gear components, mounts, electrical parts, flywheels, and repair kits', 'GBX', 21)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Gearbox subcategories (23 total)
-- Transmission lubrication parts (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('gearbox-oil-and-transmission-fluid', 'Gearbox oil and transmission fluid', 'Gearbox oil and transmission fluid', 'gearbox', 1),
  ('automatic-transmission-filter', 'Automatic transmission filter', 'Automatic transmission filter', 'gearbox', 2),
  ('gearbox-oil-seal', 'Gearbox oil seal', 'Gearbox oil seal', 'gearbox', 3),
  ('automatic-gearbox-oil-change-kit', 'Automatic gearbox oil change kit', 'Automatic gearbox oil change kit', 'gearbox', 4),
  ('automatic-transmission-fluid', 'Automatic transmission fluid', 'Automatic transmission fluid', 'gearbox', 5),
  ('transmission-oil-pan', 'Transmission oil pan', 'Transmission oil pan', 'gearbox', 6),
  ('seal-automatic-transmission-oil-pan', 'Seal, automatic transmission oil pan', 'Seal, automatic transmission oil pan', 'gearbox', 7),
  ('automatic-transmission-oil-cooler', 'Automatic transmission oil cooler', 'Automatic transmission oil cooler', 'gearbox', 8)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Gear shifting components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('gear-stick-knob', 'Gear stick knob', 'Gear stick knob', 'gearbox', 9),
  ('gear-cable', 'Gear cable', 'Gear cable', 'gearbox', 10),
  ('gear-lever-repair-kit', 'Gear lever repair kit', 'Gear lever repair kit', 'gearbox', 11)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Transmission mounts (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('gearbox-mount', 'Gearbox mount', 'Gearbox mount', 'gearbox', 12),
  ('mounting-transfer-gear', 'Mounting, transfer gear', 'Mounting, transfer gear', 'gearbox', 13)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Transmission electrical parts (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('reverse-light-switch', 'Reverse light switch', 'Reverse light switch', 'gearbox', 14),
  ('speed-sensor', 'Speed sensor', 'Speed sensor', 'gearbox', 15),
  ('transmission-control-module', 'Transmission control module', 'Transmission control module', 'gearbox', 16),
  ('automatic-gearbox-solenoid', 'Automatic gearbox solenoid', 'Automatic gearbox solenoid', 'gearbox', 17)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Flywheels and related components (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('flywheel', 'Flywheel', 'Flywheel', 'gearbox', 18)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Repair kits (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('gear-linkage-repair-kit', 'Gear linkage repair kit', 'Gear linkage repair kit', 'gearbox', 19),
  ('gearbox-bearing', 'Gearbox bearing', 'Gearbox bearing', 'gearbox', 20),
  ('repair-kit-manual-transmission-flange', 'Repair kit, manual transmission flange', 'Repair kit, manual transmission flange', 'gearbox', 21),
  ('transfer-case-parts', 'Transfer case parts', 'Transfer case parts', 'gearbox', 22),
  ('flange-lid-manual-transmission', 'Flange lid, manual transmission', 'Flange lid, manual transmission', 'gearbox', 23)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Gearbox category...';

    -- Create category folder: "Gearbox"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Gearbox/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'gearbox',
                'category_name', 'Gearbox',
                'display_name', 'Gearbox',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Gearbox';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 23 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'gearbox'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'gearbox',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Gearbox/';
    RAISE NOTICE 'subcategory/Gearbox oil and transmission fluid/';
    RAISE NOTICE 'subcategory/Automatic transmission filter/';
    RAISE NOTICE 'subcategory/Gearbox oil seal/';
    RAISE NOTICE 'subcategory/Automatic gearbox oil change kit/';
    RAISE NOTICE 'subcategory/Automatic transmission fluid/';
    RAISE NOTICE 'subcategory/Transmission oil pan/';
    RAISE NOTICE 'subcategory/Seal, automatic transmission oil pan/';
    RAISE NOTICE 'subcategory/Automatic transmission oil cooler/';
    RAISE NOTICE 'subcategory/Gear stick knob/';
    RAISE NOTICE 'subcategory/Gear cable/';
    RAISE NOTICE 'subcategory/Gear lever repair kit/';
    RAISE NOTICE 'subcategory/Gearbox mount/';
    RAISE NOTICE 'subcategory/Mounting, transfer gear/';
    RAISE NOTICE 'subcategory/Reverse light switch/';
    RAISE NOTICE 'subcategory/Speed sensor/';
    RAISE NOTICE 'subcategory/Transmission control module/';
    RAISE NOTICE 'subcategory/Automatic gearbox solenoid/';
    RAISE NOTICE 'subcategory/Flywheel/';
    RAISE NOTICE 'subcategory/Gear linkage repair kit/';
    RAISE NOTICE 'subcategory/Gearbox bearing/';
    RAISE NOTICE 'subcategory/Repair kit, manual transmission flange/';
    RAISE NOTICE 'subcategory/Transfer case parts/';
    RAISE NOTICE 'subcategory/Flange lid, manual transmission/';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Gearbox/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Gearbox category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as gearbox_subcategories FROM subcategories WHERE category_id = 'gearbox';

-- Show the new gearbox category and its subcategories
SELECT 'GEARBOX CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'gearbox';
SELECT 'GEARBOX SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'gearbox' ORDER BY sort_order;

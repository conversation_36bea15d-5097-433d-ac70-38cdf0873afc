-- =====================================================
-- AROUZ MARKET - Add Fuel Supply System Category Migration
-- Generated: 2025-08-27T15:00:00.000Z
-- Purpose: Add Fuel supply system category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Fuel supply system category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('fuel-supply-system', 'Fuel supply system', 'Fuel supply system', 'Complete fuel system components including pumps, filters, injectors, tanks, sensors, carburettors, and all fuel delivery and management components', 'FUEL', 15)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Fuel supply system subcategories (42 total)
-- Fuel filters and related components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('fuel-filter', 'Fuel filter', 'Fuel filter', 'fuel-supply-system', 1),
  ('valve-fuel-filter', 'Valve, fuel filter', 'Valve, fuel filter', 'fuel-supply-system', 2),
  ('fuel-pump-filter', 'Fuel pump filter', 'Fuel pump filter', 'fuel-supply-system', 3)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Fuel supply system components (7 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('fuel-pump', 'Fuel pump', 'Fuel pump', 'fuel-supply-system', 4),
  ('fuel-line', 'Fuel line', 'Fuel line', 'fuel-supply-system', 5),
  ('fuel-pump-relay', 'Fuel pump relay', 'Fuel pump relay', 'fuel-supply-system', 6),
  ('fuel-pump-gasket', 'Fuel pump gasket', 'Fuel pump gasket', 'fuel-supply-system', 7),
  ('fuel-overflow-pipe', 'Fuel overflow pipe', 'Fuel overflow pipe', 'fuel-supply-system', 8),
  ('seal-fuel-line', 'Seal, fuel line', 'Seal, fuel line', 'fuel-supply-system', 9),
  ('fuel-cut-off-switch', 'Fuel cut-off switch', 'Fuel cut-off switch', 'fuel-supply-system', 10),
  ('injection-system', 'Injection system', 'Injection system', 'fuel-supply-system', 11),
  ('fuel-pump-repair-kit', 'Fuel pump repair kit', 'Fuel pump repair kit', 'fuel-supply-system', 12),
  ('in-tank-fuel-pump', 'In-tank fuel pump', 'In-tank fuel pump', 'fuel-supply-system', 13)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Fuel injection system components (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('fuel-tank-and-fuel-tank-cap', 'Fuel tank and fuel tank cap', 'Fuel tank and fuel tank cap', 'fuel-supply-system', 14),
  ('injectors', 'Injectors', 'Injectors', 'fuel-supply-system', 15),
  ('injector-seals', 'Injector seals', 'Injector seals', 'fuel-supply-system', 16),
  ('throttle-body', 'Throttle body', 'Throttle body', 'fuel-supply-system', 17),
  ('high-pressure-fuel-pump-hpfp', 'High pressure fuel pump (HPFP)', 'High pressure fuel pump (HPFP)', 'fuel-supply-system', 18),
  ('fuel-rail', 'Fuel rail', 'Fuel rail', 'fuel-supply-system', 19),
  ('heat-shield-injection-system', 'Heat shield, injection system', 'Heat shield, injection system', 'fuel-supply-system', 20),
  ('engine-control-unit-ecu', 'Engine control unit (ECU)', 'Engine control unit (ECU)', 'fuel-supply-system', 21)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Fuel system components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('fuel-pressure-regulator', 'Fuel pressure regulator', 'Fuel pressure regulator', 'fuel-supply-system', 22),
  ('injector-repair-kit', 'Injector repair kit', 'Injector repair kit', 'fuel-supply-system', 23),
  ('pressure-tank-fuel-supply', 'Pressure tank, fuel supply', 'Pressure tank, fuel supply', 'fuel-supply-system', 24),
  ('oil-filter-housing', 'Oil filter housing', 'Oil filter housing', 'fuel-supply-system', 25)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Intake components (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('mass-air-flow-sensor-maf', 'Mass air flow sensor (MAF)', 'Mass air flow sensor (MAF)', 'fuel-supply-system', 26),
  ('idle-air-control-valve', 'Idle air control valve', 'Idle air control valve', 'fuel-supply-system', 27),
  ('throttle-cable', 'Throttle cable', 'Throttle cable', 'fuel-supply-system', 28),
  ('accelerator-pedal-fuel', 'Accelerator pedal', 'Accelerator pedal', 'fuel-supply-system', 29),
  ('control-throttle-blade', 'Control, throttle blade', 'Control, throttle blade', 'fuel-supply-system', 30),
  ('idle-control-valve', 'Idle control valve', 'Idle control valve', 'fuel-supply-system', 31)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Carburettors and related components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('carburettor-and-parts', 'Carburettor and parts', 'Carburettor and parts', 'fuel-supply-system', 32),
  ('carburettor-flange', 'Carburettor flange', 'Carburettor flange', 'fuel-supply-system', 33),
  ('carburettor-repair-kit', 'Carburettor repair kit', 'Carburettor repair kit', 'fuel-supply-system', 34)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Electronic components (9 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('lambda-sensor', 'Lambda sensor', 'Lambda sensor', 'fuel-supply-system', 35),
  ('fuel-pressure-sensor', 'Fuel pressure sensor', 'Fuel pressure sensor', 'fuel-supply-system', 36),
  ('throttle-position-sensor', 'Throttle position sensor', 'Throttle position sensor', 'fuel-supply-system', 37),
  ('fuel-level-sensor', 'Fuel level sensor', 'Fuel level sensor', 'fuel-supply-system', 38),
  ('seal-fuel-sender-unit', 'Seal, fuel sender unit', 'Seal, fuel sender unit', 'fuel-supply-system', 39),
  ('fuel-temperature-sensor', 'Fuel temperature sensor', 'Fuel temperature sensor', 'fuel-supply-system', 40),
  ('knock-sensor', 'Knock sensor', 'Knock sensor', 'fuel-supply-system', 41),
  ('accelerator-pedal-position-sensor', 'Accelerator pedal position sensor', 'Accelerator pedal position sensor', 'fuel-supply-system', 42),
  ('cold-start-temperature-switch', 'Cold start temperature switch', 'Cold start temperature switch', 'fuel-supply-system', 43),
  ('fuel-tank-pressure-sensor', 'Fuel tank pressure sensor', 'Fuel tank pressure sensor', 'fuel-supply-system', 44)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Tools (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('fuel-system-tools', 'Fuel system tools', 'Fuel system tools', 'fuel-supply-system', 45)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Fuel tanks and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('fuel-tank-breather-valve', 'Fuel tank breather valve', 'Fuel tank breather valve', 'fuel-supply-system', 46),
  ('fuel-flap', 'Fuel flap', 'Fuel flap', 'fuel-supply-system', 47),
  ('seal-injection-pump', 'Seal, injection pump', 'Seal, injection pump', 'fuel-supply-system', 48),
  ('fuel-cap', 'Fuel cap', 'Fuel cap', 'fuel-supply-system', 49)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Fuel supply system category...';

    -- Create category folder: "Fuel supply system"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Fuel supply system/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'fuel-supply-system',
                'category_name', 'Fuel supply system',
                'display_name', 'Fuel supply system',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Fuel supply system';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 49 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'fuel-supply-system'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'fuel-supply-system',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Fuel supply system/';
    RAISE NOTICE 'subcategory/Fuel filter/';
    RAISE NOTICE 'subcategory/Fuel pump/';
    RAISE NOTICE 'subcategory/Injectors/';
    RAISE NOTICE 'subcategory/Throttle body/';
    RAISE NOTICE '... (and 45 more subcategory folders)';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Fuel supply system/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Fuel supply system category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as fuel_supply_system_subcategories FROM subcategories WHERE category_id = 'fuel-supply-system';

-- Show the new fuel supply system category and its subcategories
SELECT 'FUEL SUPPLY SYSTEM CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'fuel-supply-system';
SELECT 'FUEL SUPPLY SYSTEM SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'fuel-supply-system' ORDER BY sort_order;

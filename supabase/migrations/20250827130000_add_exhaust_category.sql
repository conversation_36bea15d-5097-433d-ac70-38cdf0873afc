-- =====================================================
-- AROUZ MARKET - Add Exhaust Category Migration
-- Generated: 2025-08-27T13:00:00.000Z
-- Purpose: Add Exhaust category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Exhaust category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('exhaust', 'Exhaust', 'Exhaust', 'Complete exhaust system components including silencers, pipes, catalytic converters, EGR systems, turbochargers, and all related mounting hardware and sensors', 'EXH', 13)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Exhaust subcategories (41 total)
-- Electrical components (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('lambda-sensor', 'Lambda sensor', 'Lambda sensor', 'exhaust', 1)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Exhaust components (9 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('exhaust-silencer', 'Exhaust silencer', 'Exhaust silencer', 'exhaust', 2),
  ('mounting-kit-exhaust-system', 'Mounting kit, exhaust system', 'Mounting kit, exhaust system', 'exhaust', 3),
  ('middle-silencer', 'Middle silencer', 'Middle silencer', 'exhaust', 4),
  ('exhaust-pipe', 'Exhaust pipe', 'Exhaust pipe', 'exhaust', 5),
  ('flex-pipe', 'Flex pipe', 'Flex pipe', 'exhaust', 6),
  ('front-silencer', 'Front silencer', 'Front silencer', 'exhaust', 7),
  ('exhaust-tips', 'Exhaust tips', 'Exhaust tips', 'exhaust', 8),
  ('heat-shield', 'Heat shield', 'Heat shield', 'exhaust', 9),
  ('exhaust-flap', 'Exhaust flap', 'Exhaust flap', 'exhaust', 10)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Exhaust mounts, gaskets, and seals (9 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('exhaust-hanger', 'Exhaust hanger', 'Exhaust hanger', 'exhaust', 11),
  ('exhaust-gasket', 'Exhaust gasket', 'Exhaust gasket', 'exhaust', 12),
  ('exhaust-clamp', 'Exhaust clamp', 'Exhaust clamp', 'exhaust', 13),
  ('clamp-exhaust-system', 'Clamp, exhaust system', 'Clamp, exhaust system', 'exhaust', 14),
  ('silencer-rubber', 'Silencer rubber', 'Silencer rubber', 'exhaust', 15),
  ('silencer-mounting-kit', 'Silencer mounting kit', 'Silencer mounting kit', 'exhaust', 16),
  ('clamp-silencer', 'Clamp, silencer', 'Clamp, silencer', 'exhaust', 17),
  ('catalytic-converter-mounting-kit', 'Catalytic converter mounting kit', 'Catalytic converter mounting kit', 'exhaust', 18),
  ('exhaust-manifold-mounting-kit', 'Exhaust manifold mounting kit', 'Exhaust manifold mounting kit', 'exhaust', 19)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Exhaust manifolds and related components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('exhaust-manifold-gasket', 'Exhaust manifold gasket', 'Exhaust manifold gasket', 'exhaust', 20),
  ('exhaust-manifold', 'Exhaust manifold', 'Exhaust manifold', 'exhaust', 21)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Exhaust gas recirculation components (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('egr-valve', 'EGR valve', 'EGR valve', 'exhaust', 22),
  ('egr-valve-gasket', 'Egr valve gasket', 'Egr valve gasket', 'exhaust', 23),
  ('egr-cooler', 'EGR cooler', 'EGR cooler', 'exhaust', 24),
  ('secondary-air-pump', 'Secondary air pump', 'Secondary air pump', 'exhaust', 25),
  ('secondary-air-valve', 'Secondary air valve', 'Secondary air valve', 'exhaust', 26)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Catalytic converters and related components (7 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('catalytic-converter', 'Catalytic converter', 'Catalytic converter', 'exhaust', 27),
  ('diesel-particulate-filter-dpf', 'Diesel particulate filter (DPF)', 'Diesel particulate filter (DPF)', 'exhaust', 28),
  ('exhaust-pressure-sensor-dpf', 'Exhaust pressure sensor (DPF)', 'Exhaust pressure sensor (DPF)', 'exhaust', 29),
  ('exhaust-gas-temperature-sensor', 'Exhaust gas temperature sensor', 'Exhaust gas temperature sensor', 'exhaust', 30),
  ('dpf-cleaning-agent', 'DPF cleaning agent', 'DPF cleaning agent', 'exhaust', 31),
  ('dosage-module', 'Dosage module', 'Dosage module', 'exhaust', 32),
  ('euro5-euro6-nox-conversion', 'Euro5 / euro6 / nOx conversion', 'Euro5 / euro6 / nOx conversion', 'exhaust', 33)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Forced induction components (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('turbo', 'Turbo', 'Turbo', 'exhaust', 34),
  ('turbo-gasket', 'Turbo gasket', 'Turbo gasket', 'exhaust', 35),
  ('intercooler', 'Intercooler', 'Intercooler', 'exhaust', 36),
  ('intercooler-pipe', 'Intercooler pipe', 'Intercooler pipe', 'exhaust', 37),
  ('boost-pressure-sensor', 'Boost pressure sensor', 'Boost pressure sensor', 'exhaust', 38),
  ('turbo-solenoid-valve', 'Turbo solenoid valve', 'Turbo solenoid valve', 'exhaust', 39),
  ('turbo-oil-feed-pipe', 'Turbo oil feed pipe', 'Turbo oil feed pipe', 'exhaust', 40),
  ('diverter-valve', 'Diverter valve', 'Diverter valve', 'exhaust', 41)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Exhaust category...';

    -- Create category folder: "Exhaust"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Exhaust/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'exhaust',
                'category_name', 'Exhaust',
                'display_name', 'Exhaust',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Exhaust';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 41 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'exhaust'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'exhaust',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Exhaust/';
    RAISE NOTICE 'subcategory/Lambda sensor/';
    RAISE NOTICE 'subcategory/Exhaust silencer/';
    RAISE NOTICE 'subcategory/Catalytic converter/';
    RAISE NOTICE 'subcategory/Turbo/';
    RAISE NOTICE '... (and 37 more subcategory folders)';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Exhaust/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Exhaust category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as exhaust_subcategories FROM subcategories WHERE category_id = 'exhaust';

-- Show the new exhaust category and its subcategories
SELECT 'EXHAUST CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'exhaust';
SELECT 'EXHAUST SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'exhaust' ORDER BY sort_order;

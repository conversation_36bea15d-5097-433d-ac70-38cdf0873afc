-- =====================================================
-- AROUZ MARKET - Add Lighting Category Migration
-- Generated: 2025-08-27T27:00:00.000Z
-- Purpose: Add Lighting category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Lighting category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('lighting', 'Lighting', 'Lighting', 'Complete automotive lighting solutions including headlights, rear lights, bulbs, controls, auxiliary lighting, and xenon lights for all vehicle types', 'LT', 27)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Lighting subcategories (22 total)
-- Headlights and lights (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('rear-lights', 'Rear lights', 'Rear lights', 'lighting', 1),
  ('headlights', 'Headlights', 'Headlights', 'lighting', 2)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Bulbs (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('headlight-bulb', 'Headlight bulb', 'Headlight bulb', 'lighting', 3),
  ('fog-light-bulb', 'Fog light bulb', 'Fog light bulb', 'lighting', 4),
  ('indicator-bulb', 'Indicator bulb', 'Indicator bulb', 'lighting', 5),
  ('spotlight-bulb', 'Spotlight bulb', 'Spotlight bulb', 'lighting', 6),
  ('combination-rearlight-bulb', 'Combination rearlight bulb', 'Combination rearlight bulb', 'lighting', 7),
  ('brake-light-bulb', 'Brake light bulb', 'Brake light bulb', 'lighting', 8),
  ('reverse-light-bulb', 'Reverse light bulb', 'Reverse light bulb', 'lighting', 9),
  ('number-plate-bulb', 'Number plate bulb', 'Number plate bulb', 'lighting', 10)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Controls and related components (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('lighting-controls', 'Lighting controls', 'Lighting controls', 'lighting', 11)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Auxiliary, signaling, and interior lights (10 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('number-plate-light', 'Number plate light', 'Number plate light', 'lighting', 12),
  ('indicator', 'Indicator', 'Indicator', 'lighting', 13),
  ('fog-lights', 'Fog lights', 'Fog lights', 'lighting', 14),
  ('interior-lights', 'Interior lights', 'Interior lights', 'lighting', 15),
  ('door-lights', 'Door lights', 'Door lights', 'lighting', 16),
  ('boot-light', 'Boot light', 'Boot light', 'lighting', 17),
  ('additional-lighting', 'Additional lighting', 'Additional lighting', 'lighting', 18),
  ('rear-fog-light', 'Rear fog light', 'Rear fog light', 'lighting', 19),
  ('parking-light', 'Parking light', 'Parking light', 'lighting', 20),
  ('daytime-running-lights', 'Daytime running lights', 'Daytime running lights', 'lighting', 21)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional auxiliary lights (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('reverse-lights', 'Reverse lights', 'Reverse lights', 'lighting', 22),
  ('spotlights', 'Spotlights', 'Spotlights', 'lighting', 23),
  ('stop-light', 'Stop light', 'Stop light', 'lighting', 24)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Xenon lights (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('xenon-light', 'Xenon light', 'Xenon light', 'lighting', 25)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Lighting category...';

    -- Create category folder: "Lighting"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Lighting/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'lighting',
                'category_name', 'Lighting',
                'display_name', 'Lighting',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Lighting';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 25 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'lighting'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'lighting',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Lighting/';
    RAISE NOTICE 'subcategory/Rear lights/';
    RAISE NOTICE 'subcategory/Headlights/';
    RAISE NOTICE 'subcategory/Headlight bulb/';
    RAISE NOTICE 'subcategory/Fog light bulb/';
    RAISE NOTICE 'subcategory/Indicator bulb/';
    RAISE NOTICE 'subcategory/Spotlight bulb/';
    RAISE NOTICE '... and 19 more subcategory folders';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Lighting/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Lighting category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as lighting_subcategories FROM subcategories WHERE category_id = 'lighting';

-- Show the new lighting category and its subcategories
SELECT 'LIGHTING CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'lighting';
SELECT 'LIGHTING SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'lighting' ORDER BY sort_order;

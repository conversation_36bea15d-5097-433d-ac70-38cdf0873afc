-- =====================================================
-- AROUZ MARKET - Add Clutch Category Migration
-- Generated: 2025-08-27T17:00:00.000Z
-- Purpose: Add Clutch category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Clutch category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('clutch', 'Clutch', 'Clutch', 'Complete clutch system components including clutch kits, pressure plates, release bearings, slave cylinders, flywheels, and all clutch-related parts and accessories', 'CLT', 17)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Clutch subcategories (20 total)
-- Clutch kits and related components (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('clutch-kit', 'Clutch kit', 'Clutch kit', 'clutch', 1),
  ('central-slave-cylinder', 'Central slave cylinder', 'Central slave cylinder', 'clutch', 2),
  ('clutch-release-bearing', 'Clutch release bearing', 'Clutch release bearing', 'clutch', 3),
  ('clutch-plate', 'Clutch plate', 'Clutch plate', 'clutch', 4),
  ('pilot-bearing', 'Pilot bearing', 'Pilot bearing', 'clutch', 5),
  ('clutch-pressure-plate', 'Clutch pressure plate', 'Clutch pressure plate', 'clutch', 6)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Clutch release components (9 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('clutch-master-cylinder', 'Clutch master cylinder', 'Clutch master cylinder', 'clutch', 7),
  ('clutch-slave-cylinder', 'Clutch slave cylinder', 'Clutch slave cylinder', 'clutch', 8),
  ('clutch-cable', 'Clutch cable', 'Clutch cable', 'clutch', 9),
  ('sleeve', 'Sleeve', 'Sleeve', 'clutch', 10),
  ('clutch-fork', 'Clutch fork', 'Clutch fork', 'clutch', 11),
  ('pedals-and-pedal-covers', 'Pedals and pedal covers', 'Pedals and pedal covers', 'clutch', 12),
  ('clutch-slave-cylinder-repair-kit', 'Clutch slave cylinder repair kit', 'Clutch slave cylinder repair kit', 'clutch', 13),
  ('clutch-switch', 'Clutch switch', 'Clutch switch', 'clutch', 14),
  ('clutch-pipe', 'Clutch pipe', 'Clutch pipe', 'clutch', 15),
  ('release-set-clutch-operation', 'Release set, clutch operation', 'Release set, clutch operation', 'clutch', 16)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Flywheels and related components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('flywheel', 'Flywheel', 'Flywheel', 'clutch', 17),
  ('flywheel-bolt', 'Flywheel bolt', 'Flywheel bolt', 'clutch', 18)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Tools (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('clutch-tools', 'Clutch tools', 'Clutch tools', 'clutch', 19)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Car Care (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('clutch-brake-cleaner', 'Clutch & brake cleaner', 'Clutch & brake cleaner', 'clutch', 20)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Clutch category...';

    -- Create category folder: "Clutch"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Clutch/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'clutch',
                'category_name', 'Clutch',
                'display_name', 'Clutch',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Clutch';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 20 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'clutch'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'clutch',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Clutch/';
    RAISE NOTICE 'subcategory/Clutch kit/';
    RAISE NOTICE 'subcategory/Central slave cylinder/';
    RAISE NOTICE 'subcategory/Clutch release bearing/';
    RAISE NOTICE 'subcategory/Clutch plate/';
    RAISE NOTICE 'subcategory/Pilot bearing/';
    RAISE NOTICE '... (and 15 more subcategory folders)';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Clutch/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Clutch category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as clutch_subcategories FROM subcategories WHERE category_id = 'clutch';

-- Show the new clutch category and its subcategories
SELECT 'CLUTCH CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'clutch';
SELECT 'CLUTCH SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'clutch' ORDER BY sort_order;

-- =====================================================
-- AROUZ MARKET - Add Air conditioning Category Migration
-- Generated: 2025-08-27T22:00:00.000Z
-- Purpose: Add Air conditioning category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Air conditioning category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('air-conditioning', 'Air conditioning', 'Air conditioning', 'Complete air conditioning system components including cabin filters, heat exchangers, compressors, blower motors, hoses, and electrical components', 'AC', 22)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Air conditioning subcategories (22 total)
-- Cabin filters and related components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('cabin-filter', 'Cabin filter', 'Cabin filter', 'air-conditioning', 1),
  ('receiver-drier', 'Receiver drier', 'Receiver drier', 'air-conditioning', 2)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Climate control heat exchangers and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('condenser', 'Condenser', 'Condenser', 'air-conditioning', 3),
  ('heater-matrix', 'Heater matrix', 'Heater matrix', 'air-conditioning', 4),
  ('evaporator', 'Evaporator', 'Evaporator', 'air-conditioning', 5),
  ('heater-control-valve', 'Heater control valve', 'Heater control valve', 'air-conditioning', 6)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Air conditioning compressors and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('ac-compressor', 'AC compressor', 'AC compressor', 'air-conditioning', 7),
  ('compressor-clutch', 'Compressor clutch', 'Compressor clutch', 'air-conditioning', 8),
  ('expansion-valve', 'Expansion valve', 'Expansion valve', 'air-conditioning', 9),
  ('ac-compressor-valve', 'AC compressor valve', 'AC compressor valve', 'air-conditioning', 10)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Blower motors and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('blower-motor', 'Blower motor', 'Blower motor', 'air-conditioning', 11),
  ('heater-resistor', 'Heater resistor', 'Heater resistor', 'air-conditioning', 12),
  ('radiator-fan-switch', 'Radiator fan switch', 'Radiator fan switch', 'air-conditioning', 13),
  ('blower-air-conditioner', 'Blower, air conditioner', 'Blower, air conditioner', 'air-conditioning', 14)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Air conditioning hoses (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('air-conditioning-pipe-ac', 'Air conditioning pipe (AC)', 'Air conditioning pipe (AC)', 'air-conditioning', 15),
  ('fan-control-module', 'Fan control module', 'Fan control module', 'air-conditioning', 16)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Air conditioning electrical components (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('ac-pressure-switch', 'AC pressure switch', 'AC pressure switch', 'air-conditioning', 17),
  ('outside-temperature-sensor', 'Outside temperature sensor', 'Outside temperature sensor', 'air-conditioning', 18),
  ('air-recirculation-flap-motor', 'Air recirculation flap motor', 'Air recirculation flap motor', 'air-conditioning', 19),
  ('ac-relay', 'AC relay', 'AC relay', 'air-conditioning', 20),
  ('interior-temperature-sensor', 'Interior temperature sensor', 'Interior temperature sensor', 'air-conditioning', 21),
  ('cooling-fan-relay', 'Cooling fan relay', 'Cooling fan relay', 'air-conditioning', 22),
  ('air-quality-sensor', 'Air quality sensor', 'Air quality sensor', 'air-conditioning', 23),
  ('ac-control-unit', 'AC control unit', 'AC control unit', 'air-conditioning', 24)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Air conditioning category...';

    -- Create category folder: "Air conditioning"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Air conditioning/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'air-conditioning',
                'category_name', 'Air conditioning',
                'display_name', 'Air conditioning',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Air conditioning';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 24 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'air-conditioning'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'air-conditioning',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Air conditioning/';
    RAISE NOTICE 'subcategory/Cabin filter/';
    RAISE NOTICE 'subcategory/Receiver drier/';
    RAISE NOTICE 'subcategory/Condenser/';
    RAISE NOTICE 'subcategory/Heater matrix/';
    RAISE NOTICE 'subcategory/Evaporator/';
    RAISE NOTICE 'subcategory/Heater control valve/';
    RAISE NOTICE 'subcategory/AC compressor/';
    RAISE NOTICE 'subcategory/Compressor clutch/';
    RAISE NOTICE 'subcategory/Expansion valve/';
    RAISE NOTICE 'subcategory/AC compressor valve/';
    RAISE NOTICE 'subcategory/Blower motor/';
    RAISE NOTICE 'subcategory/Heater resistor/';
    RAISE NOTICE 'subcategory/Radiator fan switch/';
    RAISE NOTICE 'subcategory/Blower, air conditioner/';
    RAISE NOTICE 'subcategory/Air conditioning pipe (AC)/';
    RAISE NOTICE 'subcategory/Fan control module/';
    RAISE NOTICE 'subcategory/AC pressure switch/';
    RAISE NOTICE 'subcategory/Outside temperature sensor/';
    RAISE NOTICE 'subcategory/Air recirculation flap motor/';
    RAISE NOTICE 'subcategory/AC relay/';
    RAISE NOTICE 'subcategory/Interior temperature sensor/';
    RAISE NOTICE 'subcategory/Cooling fan relay/';
    RAISE NOTICE 'subcategory/Air quality sensor/';
    RAISE NOTICE 'subcategory/AC control unit/';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Air conditioning/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Air conditioning category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as air_conditioning_subcategories FROM subcategories WHERE category_id = 'air-conditioning';

-- Show the new air conditioning category and its subcategories
SELECT 'AIR CONDITIONING CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'air-conditioning';
SELECT 'AIR CONDITIONING SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'air-conditioning' ORDER BY sort_order;

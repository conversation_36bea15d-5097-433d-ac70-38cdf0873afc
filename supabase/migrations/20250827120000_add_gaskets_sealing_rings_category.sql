-- =====================================================
-- AROUZ MARKET - Add Gaskets and sealing rings Category Migration
-- Generated: 2025-08-27T12:00:00.000Z
-- Purpose: Add Gaskets and sealing rings category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Gaskets and sealing rings category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('gaskets-sealing-rings', 'Gaskets and sealing rings', 'Gaskets and sealing rings', 'Gaskets, seals, and sealing rings for all vehicle systems including engine, transmission, cooling, and fuel systems', 'GSR', 12)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Gaskets and sealing rings subcategories (49 total)
-- Exhaust manifold gaskets (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('intake-manifold-gasket', 'Intake manifold gasket', 'Intake manifold gasket', 'gaskets-sealing-rings', 1),
  ('exhaust-gasket', 'Exhaust gasket', 'Exhaust gasket', 'gaskets-sealing-rings', 2),
  ('turbo-gasket', 'Turbo gasket', 'Turbo gasket', 'gaskets-sealing-rings', 3),
  ('exhaust-manifold-gasket', 'Exhaust manifold gasket', 'Exhaust manifold gasket', 'gaskets-sealing-rings', 4),
  ('mounting-kit-charger', 'Mounting kit, charger', 'Mounting kit, charger', 'gaskets-sealing-rings', 5),
  ('egr-valve-gasket', 'Egr valve gasket', 'Egr valve gasket', 'gaskets-sealing-rings', 6)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Cylinder head seals and gaskets (7 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('rocker-cover-gasket', 'Rocker cover gasket', 'Rocker cover gasket', 'gaskets-sealing-rings', 7),
  ('head-gasket', 'Head gasket', 'Head gasket', 'gaskets-sealing-rings', 8),
  ('valve-stem-seals', 'Valve stem seals', 'Valve stem seals', 'gaskets-sealing-rings', 9),
  ('camshaft-seal', 'Camshaft seal', 'Camshaft seal', 'gaskets-sealing-rings', 10),
  ('timing-cover-gasket', 'Timing cover gasket', 'Timing cover gasket', 'gaskets-sealing-rings', 11),
  ('seal-oil-filter-cap', 'Seal, oil filter cap', 'Seal, oil filter cap', 'gaskets-sealing-rings', 12),
  ('seal-timing-chain-tensioner', 'Seal, timing chain tensioner', 'Seal, timing chain tensioner', 'gaskets-sealing-rings', 13)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Engine block seals and gaskets (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('oil-drain-plug-gasket', 'Oil drain plug gasket', 'Oil drain plug gasket', 'gaskets-sealing-rings', 14),
  ('crankshaft-seal', 'Crankshaft seal', 'Crankshaft seal', 'gaskets-sealing-rings', 15),
  ('sump-gasket', 'Sump gasket', 'Sump gasket', 'gaskets-sealing-rings', 16),
  ('crankcase-gasket', 'Crankcase gasket', 'Crankcase gasket', 'gaskets-sealing-rings', 17),
  ('full-engine-gasket-set', 'Full engine gasket set', 'Full engine gasket set', 'gaskets-sealing-rings', 18),
  ('seal-crankcase-breather', 'Seal, crankcase breather', 'Seal, crankcase breather', 'gaskets-sealing-rings', 19),
  ('o-ring-seal-cylinder-sleeve', 'O-ring seal, cylinder sleeve', 'O-ring seal, cylinder sleeve', 'gaskets-sealing-rings', 20)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Fuel system seals and gaskets (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('injector-seals', 'Injector seals', 'Injector seals', 'gaskets-sealing-rings', 21),
  ('fuel-pump-gasket', 'Fuel pump gasket', 'Fuel pump gasket', 'gaskets-sealing-rings', 22),
  ('seal-injection-pump', 'Seal, injection pump', 'Seal, injection pump', 'gaskets-sealing-rings', 23),
  ('seal-fuel-line', 'Seal, fuel line', 'Seal, fuel line', 'gaskets-sealing-rings', 24),
  ('seal-fuel-filter', 'Seal, fuel filter', 'Seal, fuel filter', 'gaskets-sealing-rings', 25)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Lubrication system seals and gaskets (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('oil-cooler-gasket', 'Oil cooler gasket', 'Oil cooler gasket', 'gaskets-sealing-rings', 26),
  ('oil-filter-housing-gasket', 'Oil filter housing gasket', 'Oil filter housing gasket', 'gaskets-sealing-rings', 27),
  ('seal-gasket-oil-dipstick', 'Seal / gasket, oil dipstick', 'Seal / gasket, oil dipstick', 'gaskets-sealing-rings', 28),
  ('oil-pump-gasket', 'Oil pump gasket', 'Oil pump gasket', 'gaskets-sealing-rings', 29),
  ('shaft-seal-oil-pump', 'Shaft seal, oil pump', 'Shaft seal, oil pump', 'gaskets-sealing-rings', 30)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Cooling system seals and gaskets (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('coolant-circuit-seals', 'Coolant circuit seals', 'Coolant circuit seals', 'gaskets-sealing-rings', 31),
  ('thermostat-gasket', 'Thermostat gasket', 'Thermostat gasket', 'gaskets-sealing-rings', 32),
  ('water-pump-gasket', 'Water pump gasket', 'Water pump gasket', 'gaskets-sealing-rings', 33),
  ('gasket-coolant-flange', 'Gasket, coolant flange', 'Gasket, coolant flange', 'gaskets-sealing-rings', 34),
  ('coolant-pipe-seal', 'Coolant pipe seal', 'Coolant pipe seal', 'gaskets-sealing-rings', 35),
  ('seal-ring-radiator-cap-bolt', 'Seal ring, radiator cap bolt', 'Seal ring, radiator cap bolt', 'gaskets-sealing-rings', 36),
  ('radiator-gasket', 'Radiator gasket', 'Radiator gasket', 'gaskets-sealing-rings', 37),
  ('seal-ring-for-coolant-pipe', 'Seal ring for coolant pipe', 'Seal ring for coolant pipe', 'gaskets-sealing-rings', 38)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Transmission seals and gaskets (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('differential-seal', 'Differential seal', 'Differential seal', 'gaskets-sealing-rings', 39),
  ('gearbox-oil-seal', 'Gearbox oil seal', 'Gearbox oil seal', 'gaskets-sealing-rings', 40),
  ('drive-shaft-oil-seal', 'Drive shaft oil seal', 'Drive shaft oil seal', 'gaskets-sealing-rings', 41),
  ('seal-automatic-transmission-oil-pan', 'Seal, automatic transmission oil pan', 'Seal, automatic transmission oil pan', 'gaskets-sealing-rings', 42),
  ('shaft-seal-intermediate-shaft', 'Shaft seal, intermediate shaft', 'Shaft seal, intermediate shaft', 'gaskets-sealing-rings', 43),
  ('shaft-seal-transfer-case', 'Shaft seal, transfer case', 'Shaft seal, transfer case', 'gaskets-sealing-rings', 44)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Weatherstrips (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('window-seal', 'Window seal', 'Window seal', 'gaskets-sealing-rings', 45),
  ('door-seal', 'Door seal', 'Door seal', 'gaskets-sealing-rings', 46),
  ('sunroof-seal', 'Sunroof seal', 'Sunroof seal', 'gaskets-sealing-rings', 47),
  ('seal-combination-rearlight', 'Seal, combination rearlight', 'Seal, combination rearlight', 'gaskets-sealing-rings', 48)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Suspension components (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('shaft-seal-wheel-hub', 'Shaft seal, wheel hub', 'Shaft seal, wheel hub', 'gaskets-sealing-rings', 49)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Gaskets and sealing rings category...';

    -- Create category folder: "Gaskets and sealing rings"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Gaskets and sealing rings/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'gaskets-sealing-rings',
                'category_name', 'Gaskets and sealing rings',
                'display_name', 'Gaskets and sealing rings',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Gaskets and sealing rings';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 49 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'gaskets-sealing-rings'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'gaskets-sealing-rings',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Gaskets and sealing rings/';
    RAISE NOTICE 'subcategory/Intake manifold gasket/';
    RAISE NOTICE 'subcategory/Exhaust gasket/';
    RAISE NOTICE 'subcategory/Turbo gasket/';
    RAISE NOTICE '... (and 46 more subcategory folders)';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Gaskets and sealing rings/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Gaskets and sealing rings category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as gaskets_subcategories FROM subcategories WHERE category_id = 'gaskets-sealing-rings';

-- Show the new gaskets and sealing rings category and its subcategories
SELECT 'GASKETS AND SEALING RINGS CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'gaskets-sealing-rings';
SELECT 'GASKETS AND SEALING RINGS SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'gaskets-sealing-rings' ORDER BY sort_order;

-- =====================================================
-- AROUZ MARKET - Add Drive shaft & cv joint Category Migration
-- Generated: 2025-08-27T18:00:00.000Z
-- Purpose: Add Drive shaft & cv joint category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Drive shaft & cv joint category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('drive-shaft-and-cv-joint', 'Drive shaft & cv joint', 'Drive shaft & cv joint', 'Complete drive shaft and CV joint system components including CV boots, CV joints, drive shafts, hub nuts, tripod hubs, and all related drivetrain parts and accessories', 'DSH', 18)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Drive shaft & cv joint subcategories (8 total)
-- CV joints and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('cv-boot', 'Cv boot', 'Cv boot', 'drive-shaft-and-cv-joint', 1),
  ('cv-joint', 'Cv joint', 'Cv joint', 'drive-shaft-and-cv-joint', 2),
  ('driveshaft-bolts', 'Driveshaft bolts', 'Driveshaft bolts', 'drive-shaft-and-cv-joint', 3),
  ('tripod-hub', 'Tripod hub', 'Tripod hub', 'drive-shaft-and-cv-joint', 4),
  ('hub-nut', 'Hub nut', 'Hub nut', 'drive-shaft-and-cv-joint', 5)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Drive shafts and related components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('drive-shaft', 'Drive shaft', 'Drive shaft', 'drive-shaft-and-cv-joint', 6),
  ('drive-shaft-oil-seal', 'Drive shaft oil seal', 'Drive shaft oil seal', 'drive-shaft-and-cv-joint', 7),
  ('intermediate-shaft-bearing', 'Intermediate shaft bearing', 'Intermediate shaft bearing', 'drive-shaft-and-cv-joint', 8)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Create storage folders for the new category and subcategories
-- This creates the actual folder structure visible in Supabase Storage UI
DO $$
DECLARE
    placeholder_data BYTEA := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    current_user_id UUID;
    folder_count INTEGER := 0;
    category_record RECORD;
    subcategory_record RECORD;
BEGIN
    -- Get current user ID (fallback to a default if needed)
    SELECT COALESCE(auth.uid(), '00000000-0000-0000-0000-000000000000'::uuid) INTO current_user_id;

    RAISE NOTICE '🚀 Creating storage folders for Drive shaft & cv joint category...';

    -- Create category folder: "Drive shaft & cv joint"
    BEGIN
        INSERT INTO storage.objects (bucket_id, name, owner, metadata)
        VALUES (
            'category-images',
            'category/Drive shaft & cv joint/.keep',
            current_user_id,
            jsonb_build_object(
                'size', length(placeholder_data),
                'mimetype', 'image/png',
                'category_id', 'drive-shaft-and-cv-joint',
                'category_name', 'Drive shaft & cv joint',
                'display_name', 'Drive shaft & cv joint',
                'type', 'category_folder_placeholder'
            )
        )
        ON CONFLICT (bucket_id, name) DO NOTHING;

        folder_count := folder_count + 1;
        RAISE NOTICE 'Created category folder: Drive shaft & cv joint';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Category folder already exists or error: %', SQLERRM;
    END;

    -- Create subcategory folders for all 8 subcategories
    FOR subcategory_record IN
        SELECT s.id, s.name, s.display_name
        FROM subcategories s
        WHERE s.category_id = 'drive-shaft-and-cv-joint'
        ORDER BY s.sort_order
    LOOP
        BEGIN
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                current_user_id,
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', 'drive-shaft-and-cv-joint',
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;

            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Subcategory folder % already exists or error: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE '✅ Created % storage folders total', folder_count;
    RAISE NOTICE '';
    RAISE NOTICE '📁 FOLDER STRUCTURE CREATED:';
    RAISE NOTICE 'category/Drive shaft & cv joint/';
    RAISE NOTICE 'subcategory/Cv boot/';
    RAISE NOTICE 'subcategory/Cv joint/';
    RAISE NOTICE 'subcategory/Driveshaft bolts/';
    RAISE NOTICE 'subcategory/Tripod hub/';
    RAISE NOTICE 'subcategory/Hub nut/';
    RAISE NOTICE 'subcategory/Drive shaft/';
    RAISE NOTICE 'subcategory/Drive shaft oil seal/';
    RAISE NOTICE 'subcategory/Intermediate shaft bearing/';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Navigate to category/Drive shaft & cv joint/ folder';
    RAISE NOTICE '3. Upload category image (any name, preferably .png)';
    RAISE NOTICE '4. Navigate to subcategory folders and upload images';
    RAISE NOTICE '5. Images will automatically appear in the marketplace!';

END $$;

-- Verification queries
SELECT 'Drive shaft & cv joint category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as drive_shaft_cv_joint_subcategories FROM subcategories WHERE category_id = 'drive-shaft-and-cv-joint';

-- Show the new drive shaft & cv joint category and its subcategories
SELECT 'DRIVE SHAFT & CV JOINT CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'drive-shaft-and-cv-joint';
SELECT 'DRIVE SHAFT & CV JOINT SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'drive-shaft-and-cv-joint' ORDER BY sort_order;

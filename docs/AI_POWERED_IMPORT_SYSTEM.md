# AI-Powered Product Import System

## Overview

The AI-Powered Product Import System revolutionizes product data management by combining file upload capabilities with real-time web search and intelligent data enhancement. This system automatically enriches basic product information with comprehensive details sourced from the internet.

## Architecture

### 3-Step Import Flow

1. **File Upload & Data Extraction**
   - Supports CSV and Excel files
   - Intelligent field mapping using pattern recognition
   - Extracts basic product information (part numbers, names, brands, prices)

2. **AI Web Search Enhancement**
   - Real-time web search using Tavily API
   - OpenAI GPT-4o for intelligent data processing
   - Comprehensive product information gathering

3. **User Review & Completion**
   - Interactive review interface
   - Approve/reject individual products
   - Seamless integration with existing product management

## Technical Implementation

### Core Services

#### AIWebSearchService (`src/services/aiWebSearchService.ts`)
- **Integrated Web Search**: OpenAI's native web search capabilities
- **AI Processing**: OpenAI GPT-4o for data analysis and structuring
- **Automotive Focus**: Specialized search for automotive parts websites
- **Batch Processing**: Efficient handling of multiple products

#### EnhancedImportService (`src/services/enhancedImportService.ts`)
- **File Processing**: CSV/Excel parsing with intelligent field extraction
- **Session Management**: Progress tracking and state management
- **Data Orchestration**: Coordinates the 3-step import flow
- **Error Handling**: Comprehensive error recovery and fallback mechanisms

### UI Components

#### EnhancedImportDialog (`src/features/products/components/EnhancedImportDialog.tsx`)
- **Progressive UI**: Step-by-step interface with progress indicators
- **Real-time Updates**: Live progress tracking and status updates
- **Review Interface**: Interactive product approval/rejection system
- **Responsive Design**: Optimized for desktop and mobile

## Data Structure

### Enhanced Product Data Output

```typescript
interface EnhancedProductData {
  // Core Product Information
  productName: string;
  partArticleNumber: string;
  manufacturer: string;
  category: string; // Matches exact 34 categories
  stockQuantity?: number;
  retailPrice?: number;

  // Vehicle Compatibility
  vehicleCompatibility: string; // "Brand Model Engine (Year-Year)"

  // Detailed Description (5 sections)
  description: {
    generalInformation: string;
    technicalInformation: string;
    applicability: string;
    originalNumbers: string;
    oemNumbers: string;
  };

  // Metadata
  confidence: number;
  searchSources: string[];
}
```

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# AI Web Search configuration (OpenAI with integrated web search)
VITE_OPENAI_API_KEY=your-openai-api-key
```

### API Keys Setup

1. **OpenAI API Key** (Required)
   - Visit: https://platform.openai.com/api-keys
   - Create new API key
   - Add to environment variables
   - **Note**: Ensure your OpenAI account has web search capabilities enabled

## Features

### Intelligent Field Mapping
- **Pattern Recognition**: Automatically identifies part numbers, names, brands
- **Case-Insensitive**: Handles various column naming conventions
- **Flexible Parsing**: Supports multiple data formats and structures

### Comprehensive Web Search
- **Automotive Focus**: Searches specialized automotive parts websites
- **Multiple Sources**: Aggregates data from various reliable sources
- **Real-time Data**: Always fetches the latest product information

### AI-Powered Enhancement
- **Category Mapping**: 100% accurate categorization against 34 categories
- **Vehicle Compatibility**: Extracts complete compatibility with year ranges
- **Structured Descriptions**: Generates 5-section detailed descriptions
- **Quality Validation**: Confidence scoring for data reliability

### User Experience
- **Progress Tracking**: Real-time progress indicators
- **Error Recovery**: Graceful handling of failures
- **Batch Processing**: Efficient handling of large files
- **Review Interface**: Easy approval/rejection workflow

## Usage

### Basic Import Flow

1. **Access Import**: Click "AI-Powered Import" in the product import dialog
2. **Upload File**: Select CSV or Excel file with product data
3. **Wait for Enhancement**: AI automatically searches and enhances data
4. **Review Results**: Approve/reject individual products
5. **Complete Import**: Finalize import of approved products

### Supported File Formats

- **CSV**: Comma-separated values with headers
- **Excel**: .xlsx and .xls formats
- **Headers Required**: First row must contain column headers

### Expected Input Columns

The system intelligently maps various column names:

- **Part Numbers**: part_number, partnumber, sku, article_number, code
- **Product Names**: product_name, name, title, description
- **Manufacturers**: manufacturer, brand, make, supplier
- **Prices**: price, retail_price, cost, amount
- **Stock**: stock, quantity, qty, inventory

## Performance

### Optimization Features
- **Rate Limiting**: 1-second delay between API calls
- **Batch Processing**: Efficient handling of multiple products
- **Progress Tracking**: Real-time status updates
- **Error Recovery**: Fallback mechanisms for failed requests

### Scalability
- **Large Files**: Handles 100k+ row files efficiently
- **Memory Management**: Optimized for large datasets
- **Session Management**: Temporary storage with automatic cleanup

## Integration

### Existing System Compatibility
- **Data Grid**: Seamless integration with existing product tables
- **Validation**: Uses existing product validation rules
- **Categories**: Maps to existing 34-category system
- **User Permissions**: Respects existing access controls

### Future Enhancements
- **Custom Search Domains**: Configurable search sources
- **AI Model Selection**: Support for different AI models
- **Bulk Operations**: Enhanced batch processing capabilities
- **Analytics**: Import success metrics and insights

## Troubleshooting

### Common Issues

1. **API Key Errors**
   - Verify environment variables are set correctly
   - Check API key validity and permissions

2. **Search Failures**
   - Check internet connectivity
   - Verify Tavily API quota and limits

3. **Import Errors**
   - Ensure file format is supported
   - Check column headers match expected patterns

### Error Recovery
- **Automatic Fallback**: System provides default values for failed searches
- **Partial Success**: Continues processing even if some products fail
- **User Feedback**: Clear error messages and recovery suggestions

## Security

### Data Protection
- **API Keys**: Stored securely in environment variables
- **No Data Storage**: Search results not permanently stored
- **User Control**: Complete control over data approval/rejection

### Privacy
- **Minimal Data**: Only necessary product information is searched
- **No Personal Data**: System focuses on product specifications only
- **Secure Transmission**: All API calls use HTTPS encryption

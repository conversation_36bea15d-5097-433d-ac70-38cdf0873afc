<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI-Powered Column Mapping Test (Backend)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1e293b;
            margin: 0;
            font-size: 2rem;
        }
        .header p {
            color: #64748b;
            margin: 10px 0 0 0;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        .section h3 {
            margin: 0 0 15px 0;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .test-button {
            background: #fa7b00;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
        }
        .test-button:hover {
            background: #e56b00;
            transform: translateY(-1px);
        }
        .test-button:disabled {
            background: #94a3b8;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #dcfce7;
            border: 1px solid #bbf7d0;
            color: #166534;
        }
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        .info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            color: #1d4ed8;
        }
        .sample-data {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-available { background: #10b981; }
        .status-unavailable { background: #ef4444; }
        .mapping-result {
            margin: 10px 0;
            padding: 10px;
            background: #f8fafc;
            border-left: 4px solid #fa7b00;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI-Powered Column Mapping Test</h1>
            <p>Test the intelligent column mapping system with production backend service</p>
        </div>

        <div class="section">
            <h3>
                <span class="status-indicator status-available"></span>
                🔧 Backend Service Status
            </h3>
            <div id="serviceStatus" class="result info">
                ✅ Production AI service is ready
                🔑 OpenAI API key configured
                🚀 Backend service available
                📊 GPT-4 model ready for analysis
            </div>
        </div>

        <div class="section">
            <h3>📊 Test Data</h3>
            <p>Sample data with the exact column structure you mentioned:</p>
            <div class="sample-data">Vendor,Item,Brand,Type,Vehicle,Program,Main_OE_References,OE_Brand,Weight_kg,Packing_Length_mm,Packing_Height_mm,Packing_Width_mm,Packing_Volume_dm3,Delivery_Period_days,Netto_Price_EUR,Order,MOQ,Paired_Article,Note,Price_Date
SUPPLIER1,12345,BOSCH,Brake Disc,BMW 3 Series,Standard,34116792217,BMW,2.5,300,50,300,4.5,3,45.99,100,1,12346,High quality brake disc,2024-01-15
SUPPLIER2,67890,BREMBO,Brake Pad,Audi A4,Premium,8E0698151A,AUDI,1.2,200,30,150,0.9,2,89.50,50,2,67891,Ceramic brake pads,2024-01-16
SUPPLIER3,11111,FEBI,Oil Filter,Mercedes C-Class,Economy,A0001802609,MERCEDES,0.3,100,80,100,0.8,1,12.75,200,5,11112,Standard oil filter,2024-01-17</div>
            
            <button class="test-button" onclick="testAIMapping()">
                🧠 Test AI Mapping
            </button>
            
            <div id="result"></div>
        </div>
    </div>

    <script type="module">
        // Import the backend AI service
        import { testAIMappingWithSampleData, getAIMappingStatus } from './src/services/aiMappingBackend.js';

        // Make functions available globally
        window.testAIMappingWithSampleData = testAIMappingWithSampleData;
        window.getAIMappingStatus = getAIMappingStatus;

        // Update service status on load
        window.addEventListener('load', async () => {
            try {
                const status = getAIMappingStatus();
                const statusDiv = document.getElementById('serviceStatus');
                
                if (status.available) {
                    statusDiv.innerHTML = `
✅ Production AI service is ready
🔑 OpenAI API key: ${status.hasApiKey ? 'Configured' : 'Missing'}
🚀 Backend service: ${status.available ? 'Available' : 'Unavailable'}
📊 Model: ${status.service}
🎯 Key validation: ${status.keyValid ? 'Valid' : 'Invalid'}`;
                    statusDiv.className = 'result success';
                } else {
                    statusDiv.innerHTML = `
❌ AI service unavailable
🔑 API key: ${status.hasApiKey ? 'Present but invalid' : 'Missing'}
🚀 Backend service: Not available`;
                    statusDiv.className = 'result error';
                }
            } catch (error) {
                console.error('Status check failed:', error);
            }
        });
    </script>

    <script>
        async function testAIMapping() {
            const button = document.querySelector('.test-button');
            const resultDiv = document.getElementById('result');
            
            button.disabled = true;
            button.textContent = '🔄 Analyzing with AI...';
            
            try {
                resultDiv.innerHTML = '<div class="result info">🤖 Starting AI analysis with production backend...</div>';
                
                const result = await window.testAIMappingWithSampleData();
                
                // Display the results
                let html = '<div class="result success">';
                html += `<strong>✅ AI Analysis Completed Successfully!</strong>\n\n`;
                html += `🎯 Overall Confidence: ${result.confidence}%\n`;
                html += `📂 Suggested Category: ${result.suggestedCategory}\n`;
                html += `🏷️ Suggested Subcategory: ${result.suggestedSubcategory}\n\n`;
                html += `📊 Column Mappings (${result.mappings.length} found):\n`;
                
                result.mappings.forEach((mapping, index) => {
                    html += `\n${index + 1}. "${mapping.originalColumn}" → ${mapping.targetField}\n`;
                    html += `   Confidence: ${mapping.confidence}%\n`;
                    html += `   Reasoning: ${mapping.reasoning}\n`;
                    html += `   Sample Values: [${mapping.sampleValues.join(', ')}]\n`;
                });
                
                if (result.unmappedColumns.length > 0) {
                    html += `\n🔍 Unmapped Columns (${result.unmappedColumns.length}):\n`;
                    html += result.unmappedColumns.map(col => `   • ${col}`).join('\n');
                    html += '\n   (These will be added to product description)';
                }
                
                html += '</div>';
                resultDiv.innerHTML = html;
                
            } catch (error) {
                console.error('AI mapping test failed:', error);
                resultDiv.innerHTML = `<div class="result error">❌ Test failed: ${error.message}</div>`;
            } finally {
                button.disabled = false;
                button.textContent = '🧠 Test AI Mapping';
            }
        }
    </script>
</body>
</html>
